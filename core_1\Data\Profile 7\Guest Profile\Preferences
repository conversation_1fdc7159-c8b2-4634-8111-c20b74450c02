{"account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autofill": {"orphan_rows_removed": true, "upload_events_last_reset_timestamp": "*****************"}, "bookmark_bar": {"show_on_all_tabs": false}, "bookmarks": {"editing_enabled": false}, "browser": {"has_seen_welcome_page": false}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17498, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.121327, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "968c99aa-d700-4b0a-bd5d-90dca5a0b5b7"}}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "media": {"device_id_salt": "E4F50B4EEC2F8D3EE649B0F25BDEFE89"}, "media_router": {"receiver_id_hash_token": "1NxvYRoc9i6dddZLGZLJdiCvm4iVuq6P3FuScl0sviiQa+Hybs6PR1nXX3mCJ3ff6WmrL8QEdjH5Wm5bj0AQ0Q=="}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}, "last_fetch_attempt": "*****************"}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true}, "store_file_paths_to_delete": {}}, "plugins": {"plugins_list": []}, "profile": {"avatar_index": 0, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "pref_version": 1}, "created_by_version": "107.0.5304.88", "creation_time": "13394668550668977", "last_engagement_time": "13394668557972049", "managed_user_id": "", "name": "Guest"}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13394668550"}, "sessions": {"session_data_status": 1}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "107"}}