/**
 * Created by sanja<PERSON> kumar on 25/06/2020.
 */

#registration_help {
  color: gray;
}

#registration_footer span{
  color: gray; 
}

#registration_help:hover {
  color: red;
}

#registration_tutorial {
  color: gray;
}

#registration_tutorial:hover {
  color: red;
}

#register_form{
  width: 400px;
  height: 260px;
  /*display: flex;*/
  display: none;
  flex-direction: column;
  margin: auto;
  align-items: center; 
}

#form_title {
  color: #f29a00;
}

#email,#password,#company{
  width: 80%;
  height: 30px;
  border: none;
  outline: none;
  border-bottom: 1px solid #f29a00a8;
  padding: 5px;
  margin-bottom: 5px;
} 
#submit_btn{
   width: 150px;
  height: 30px;
  border: none;
  outline: none;
  border-radius: 5px;
  background: #f29a00a8;
  color: black;
  cursor: pointer;
  text-align: center;
   margin-top: 15px;
}
#login{
  margin-top: 8px;
  font-size: 12px;
  color: gray;
}

#login_a{
  color: #0000FF;
}

body {
  margin: 0;
  overflow: hidden;
  font-weight: 500;
  counter-reset: Serial; 
  height: 100%;
}
ul, div, p {
    padding: 0;
    margin: 0;
}

button:focus {outline:0;}

.editorHeader {
  /*background-color: #F5F5F5;*/
  padding: 6px 5px 8px 5px;
  border: 1px solid #898b894f;
  color: black;
  top: 0;
  right: 0;
  left: 0;
  height: 39px;
  overflow: hidden;
  z-index: +1;
}

.header1 {
  overflow: hidden;
}

.edit-icon {
  height: 14px;
  width: 14px;
  background-image: url(../icons/edit_black.svg);
  border: none;
  cursor: pointer;
  background-color: transparent;
  vertical-align: text-top;
}

.editorContent {
    background-color: #f29a00a8;
    /*background-color: #00bcd4a6;*/
    color: black;
    padding: 3px 1px 3px 3px;
    border-radius: 9px;
    font-size: 10px !important;
    margin: 0px 4px 0px 0px;
    line-height: 1em;
    position: relative;
    bottom: 1px;
    font-family: cursive;
    /*text-shadow: 2px 2px 4px #0000006b;*/
    /*box-shadow: 0 0 4px #887c7c94;*/
    cursor: pointer;
}

.editorContent:hover .savedValues.toolTip{
  visibility: visible;
}

.savedValues{
    top: 1px;
    line-height: 10px;
    width: max-content;
    font-family: system-ui, sans-serif;
}

.selectors-input {
    width: 100%;
    height: 20px;
    margin: 0px;
    outline: none;
    border: none;
    /*background: #e8e7e7;*/
}

.selector-editor-div{
  display: inline-block;
  border: 1px solid #c7c7c7;
  border-radius: 2px;
  width: calc(100% - 128px);
  height: 20px;
  background: white;
}

.configContainer {
  /*overflow: auto;*/
  height: 23px;
}

.total-match-count {
  display: inline-block;
  color: white;
  max-width: 270px;
  visibility: visible;
  height: 15px;
  margin-left: 34px;
  font-weight: 400;
  font-size: 10px !important;
  background: #0cb9a9;
  border-radius: 6px;
  padding: 1px 4px 0px 5px;
  vertical-align: -webkit-baseline-middle;
}

#selectorsHubEleContainer {
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100% - 85px);
    padding-top: 7px;
    width: 94%;
}

.reviewRequest {
    background-color: #71bde0;
    color: #FFF;
    padding: 4px;
    line-height: 18px;
    letter-spacing: 1px;
    position: relative;
    text-align: center;
    overflow: hidden;
}

.reviewRequest .title {
    color: black;
}

.reviewRequest .bold-link {
  text-decoration:none;
  color: #000;
  font-weight: bold;

}


.latestUpdate {
  background-color: #fcf8e3;
  color: #c09853;
}

ul#selectorsHubEleContainer div {
    color: #6d6b6b;
    margin-top: 4px;
    max-width: 95%;
    cursor: pointer;  
    list-style-type: none;
    padding-left: 5px;
}

ul#selectorsHubEleContainer div span {
    color: #4e4d4d;
    margin-top: 4px;
    max-width: 100%;
    cursor: pointer;  
    list-style-type: none;
    font-weight: 400;
    font-size: 11px !important;
}

.summary, .content {
  pointer-events: none;
  display: inline-block;
}

.wrongXpath {
  background-color: #FF6347;
  color: white;
}
.hideMatchCountMsg {
  visibility: hidden;
}

.theme-color {
    width: 16px;
    height: 20px;
    margin: -1px;
    border-radius: 18px;
    padding: 0px;
}

.level-padding {
  padding-left: 14px;
}

.text-node, .leaf-node {
  cursor: initial;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.parent, .child {
  cursor: pointer;
}

.parent.closed{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.closed > .child, .closed > .text-node, .closed > .leaf-node {
  display: none;
}

.children-cont > .leaf-node {
  display: block;
}

.open > .child, .open > .child {
  display: block;
}

.open > .children-cont > .child {
  display: block;
}

.closed:before {
  content: "\25B6";
  font-size: 10px !important;
  margin-right: 5px;
}

.open:before {
  content: "\25BC";
  font-size: 10px !important;
  margin-right: 5px;
  color: gray;
}

.children-cont:before, .text-node:before, .leaf-node:before  {
  content: "";
  margin: 0;
}

.open-tag-label, .close-tag-label {
  pointer-events: none;
}

.selectorsRow {
    background: #9e9e9e17;
    height: 23px;
    list-style: none;
    padding: 1px;
}

.listPosition {
  position: absolute;
  width: 100%;
}

.selectorsRow:hover {
  background: #e3e8ea;
}

.selectorsRow.suggestedXpath {
  background-color: #aba9a9ba;
  display: none;
}

.selectorsRow.iframeXpath {
  background-color: #0000005c;
  display: none;
  color: white;
}

.copy-btn{
  background-image: url(../icons/copy_black.svg);
  height: 15px;
  width: 15px;
  background-color: #c7deec1a;
  border: none;
  margin-left: 4px;
  margin-top: 3px;
  vertical-align: sub;
}

.copy-btn:hover {
  background-image: url(../icons/copy_blue.svg);
}

.edit-btn{
  background-image: url(../icons/edit_black.svg);
  height: 16px;
  background-color: #c7deec1a;
  border: none;
  margin-left: 6px;
}

.edit-btn:hover{
  background-image: url(../icons/edit_blue.svg);
}

.box {
  display: inline-block;
  /*overflow: hidden;*/
}

.typeOfLocator{
    font-size: 10px !important;
    margin-left: 5px;
    width: 90px;
    color: black;
    white-space: nowrap;
    text-overflow: ellipsis;
    border-radius: 5px;
    padding: 2 0px 2 5px;
    font-weight: 600;
    vertical-align: text-bottom;
}

.box.valueSelector{
  width: calc(100% - 175px);
  overflow: hidden;
  white-space:nowrap;
  text-overflow:ellipsis;
  margin-left: 5px;
  font-size: 11px !important;
  color: #4e4d4d;
  font-weight: 500;
  margin-top: 4px;
}

.donation-btn {
  background-image: url(../icons/donation_black.svg);
  /*overflow: hidden;*/
  background-color: white;
  height: 18px;
  width: 18px;
  border: none;
  margin-top: 3px;
  display: inline-block;
  cursor: pointer;
  padding-bottom: 5px;
}

.donationLink:hover .donation-btn{
  background-image: url(../icons/donation_blue.svg);
}

.donationLink:hover .donation.toolTip{
  visibility: visible;
}

.donation{
    top: 31px;
    line-height: 11px;
    margin-left: 25px;
}

.setting_btn{
  background-image: url(../icons/setting_black.svg);
  height: 14px;
  width: 14px;
  background-color: #F5F5F5;
  border: none;
  margin-left: 5px;
  cursor: pointer;
  display: inline-block;
}

.setting_btn:hover {
    background-image: url(../icons/setting_blue.svg) !important;
}

.quotes-btn{
  background-image: url(../icons/quotes_black.svg);
  height: 14px;
  width: 14px;
  background-color: white;
  border: none;
  cursor: pointer;
  display: inline-block;
  margin-bottom: 6px;
}

.quotes-btn:hover {
    background-image: url(../icons/quotes_blue.svg) !important;
}

.quotes-btn.active {
    background-image: url(../icons/quotes_blue.svg) !important;
}

.quotes-btn:hover .quotes.toolTip{
  visibility: visible;
}

.quotes{
    top: 90px;
    right: 15px;
    line-height: 10px;
    width: max-content;
}

.expand-btn{
  background-image: url(../icons/expand_gray.svg);
  height: 12px;
  width: 12px;
  background-color: white;
  border: none;
  cursor: pointer;
  display: inline-block;
  margin-bottom: 6px;
  margin-left: 1px;
  /*display: none;*/
}

.expand-btn:hover {
    background-image: url(../icons/expand_blue.svg) !important;
}

.expand-btn.active {
    background-image: url(../icons/expand_blue.svg) !important;
}

.expand-btn:hover .expand.toolTip{
  visibility: visible;
}

.expand{
    top: 108px;
    right: 15px;
    line-height: 10px;
    width: max-content;
}

.reset-btn{
  background-image: url(../icons/reset_black.svg);
  height: 14px;
  width: 14px;
  background-color: white;
  border: none;
  cursor: pointer;
  display: inline-block;
  margin-top: 6px;
}

.reset-btn:hover {
    background-image: url(../icons/reset_red.svg) !important;
}

.reset-btn:hover .reset.toolTip{
  visibility: visible;
}

.reset{
    top: 144px;
    right: 15px;
    line-height: 10px;
    width: max-content;
}

.toggle-btn {
  width: 30px;
  height: 14px;
  border-radius: 20px;
  padding: 2px 3px;
  /*margin: -5px 6px 0px 31px;*/
  display: inline-block;
  cursor: pointer;
  line-height: 30px;
  vertical-align: middle;
  box-sizing: border-box;
  /*display: none;*/
  margin-left: calc(100% - 36px);
  margin-top: -15px;
}

.toggle-btn:hover .toggle.toolTip{
  visibility: visible;
}

.toggle{
  top: 31px;
  right: 47px;
  line-height: 10px;
}


/*.toggle-btn.active:before {
  content: "On";
  color: black;
  font-size: 6px !important;
  position: fixed;
  top: var(--top, 27px);
}

.toggle-btn.inactive:before {
  content: "Off";
  color: black;
  font-size: 6px !important;
  position: absolute;
  top: var(--top, 27px);
  margin-left: 11px;
}*/

.toggle-btn.active {
    background-color: #f29a00a8;
}

.toggle-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    /*position: absolute;*/
    transition: transform 0.3s ease;
}

.toggle-btn.active .toggle-circle {
    background-color: #FFF;
    transform: translateX(calc(14px));
}

.toggle-btn.inactive .toggle-circle {
    background-color: #524e4e;
    transform: translateX(0px);
}

.toggle-btn.inactive {
    background-color: #b4bbba;
}

.autosuggest-toggle-btn {
  width: 30px;
  height: 14px;
  border-radius: 20px;
  padding: 2px 3px;
  /*margin: -5px 6px 0px 31px;*/
  display: inline-block;
  cursor: pointer;
  line-height: 30px;
  vertical-align: -webkit-baseline-middle;
  box-sizing: border-box;
  /*display: none;*/
  margin-top: 1px;
  margin-left: 5px;
}

.autosuggest-toggle-btn:hover .autosuggest-toggle.toolTip{
  visibility: visible;
}

.autosuggest-toggle{
  top: 48px;
  left: 86px;
  line-height: 10px;
}


/*.autosuggest-toggle-btn.active:before {
  content: "On";
  color: black;
  font-size: 6px !important;
  position: fixed;
  top: var(--top, 27px);
}*/

/*.autosuggest-toggle-btn.inactive:before {
  content: "Off";
  color: black;
  font-size: 6px !important;
  position: absolute;
  top: var(--top, 27px);
  margin-left: 11px;
}*/

.autosuggest-toggle-btn.active {
    background-color: #f29a00a8;
}

.autosuggest-toggle-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    /*position: absolute;*/
    transition: transform 0.3s ease;
}

.autosuggest-toggle-btn.active .autosuggest-toggle-circle {
    background-color: #FFF;
    transform: translateX(calc(14px));
}

.autosuggest-toggle-btn.inactive .autosuggest-toggle-circle {
    background-color: #524e4e;
    transform: translateX(0px);
}

.autosuggest-toggle-btn.inactive {
    background-color: #b4bbba;
}

.id-xpath{
  margin: 0px 0px 0px 8px;
}

.multiSelectorRecordBtn{
  background-image: url(../icons/capture_black.svg);
  overflow: hidden;
  background-color: white;
  height: 14px;
  width: 14px;
  border: none;
  /*margin-left: 3px;*/
  cursor: pointer;
  margin-bottom: 6px;
  /*display: none;*/
}

.multiSelectorRecordBtn.grey:hover{
  background-image: url(../icons/capture_blue.svg) !important;
}

.multiSelectorRecordBtn.grey{
  background-image: url(../icons/capture_black.svg) !important;
}

.multiSelectorRecordBtn.red{
  animation: blink 2s ease-in infinite;
  background-image: url(../icons/capture_red.svg) !important;
}

.multiSelectorRecordBtn:hover .capture.toolTip{
  visibility: visible;
}

.capture{
    top: 47px;
    right: 15px;
    line-height: 10px;
    width: max-content;
}

.testCaseStudioBtn{
  background-image: url(../icons/tcStudio_black.svg);
  overflow: hidden;
  background-color: #F5F5F5;
  height: 14px;
  width: 14px;
  border: none;
  /*margin-left: 5px;*/
  cursor: pointer;
  margin-bottom: 6px;
  display: none;
}

.redBlink {
  background-image: url(../icons/tcStudio_red.svg) !important;
  animation: blink 2s ease-in infinite;
}

.testCaseStudioBtn:hover{
  background-image: url(../icons/tcStudio_blue.svg);
}

.testCaseStudioBtn:hover .tcStudio.toolTip{
  visibility: visible;
  background-color: #EBBA16;
}

.tcStudio{
    top: 46px;
    right: 1px;
    line-height: 10px;
}


@keyframes blink {
  from, to { opacity: 1 }
  50% { opacity: 0.3 }
}

.exportButton{
    background-image: url(../icons/export_black.svg);
    overflow: hidden;
    background-color: #18800000;
    height: 15px;
    width: 15px;
    border: none;
    margin-left: 20px;
    cursor: pointer;
}

.exportButton:hover {
    background-image: url(../icons/export_blue.svg);
}

.resetSmart {
  top: 89px;
  right: 15px;
  line-height: 10px;
  width: max-content;
}

.smartFix {
  background-image: url(../icons/maintenance_black.svg);
  overflow: hidden;
  background-color: transparent;
  height: 14px;
  width: 14px;
  border: none;
  /*margin-left: 5px;*/
  cursor: pointer;
  margin-bottom: 6px;
  /*display: none;*/
}

.smartFix:hover .resetSmart.toolTip{
  visibility: visible;
}

.defaultsmartFix {
  background-image: url(../icons/maintenance_blue.svg);
}

.defaultsmartFix:hover {
  background-image: url(../icons/maintenance_blue.svg);
}

.smartFix:hover {
  background-image: url(../icons/maintenance_blue.svg);
}

.importButton{
  background-image: url(../icons/upload_black.svg);
  overflow: hidden;
  background-color: #18800000;
  height: 14px;
  width: 14px;
  border: none;
  margin-left: 5px;
  cursor: pointer;
  display: none;
}

.importButton:hover{
  background-image: url(../icons/upload_blue.svg);
}

.exportButton:hover .export.toolTip{
  visibility: visible;
}

.export{
  top: 25px;
  line-height: 12px;
  width: max-content;
}

.uploadModalIcon {
  top: 25px;
  right: 0px;
  line-height: 12px;
}

.importButton:hover .uploadModalIcon.toolTip {
  visibility: visible;
}

.openSelectorModal {
  background-image: url(../icons/editor_black.svg);
  background-size: contain;
  overflow: hidden;
  background-color: #18800000;
  height: 16px;
  width: 16px;
  border: none;
  margin-left: 20px;
  cursor: pointer;
}

.openSelectorModal:hover {
  background-image: url(../icons/editor_blue.svg);
}

.openSelectorModal:hover .pasteModal.toolTip {
  visibility: visible;
}

.copyAllBtn{
    background-image: url(../icons/copy_black.svg);
    overflow: hidden;
    background-color: #18800000;
    height: 15px;
    width: 15px;
    border: none;
    margin-left: 19px;
    cursor: pointer;
}

.copyAllBtn:hover {
    background-image: url(../icons/copy_blue.svg);
}

.copyAllBtn:hover .copyAll.toolTip{
  visibility: visible;
}

.copyAll{
  top: 25px;
  left: 45px;
  line-height: 10px;
}

.deleteAllBtn {
    background-image: url(../icons/delete_black.svg);
    overflow: hidden;
    background-color: #18800000;
    height: 15px;
    width: 15px;
    border: none;
    margin-left: 24px;
    cursor: pointer;
}

.deleteAllBtn:hover {
    background-image: url(../icons/delete_red.svg);
}

.deleteAllBtn:hover .deleteAll.toolTip{
  visibility: visible;
}

.deleteAll{
    top: 25;
    left: 75px;
    line-height: 10px;
}

.tutorialVideoIcon{
    background-image: url(../icons/info_black.svg);
    overflow: hidden;
    background-color: #18800000;
    height: 15px;
    width: 15px;
    border: none;
    margin-left: 20px;
    cursor: pointer;
    /*position: fixed;*/
}

.tutorialVideoIcon:hover{
  background-image: url(../icons/info_blue.svg);
}

.tutorialVideoIcon:hover .tutorial.toolTip{
  visibility: visible;
  margin-left: 80px;
}

.tutorial{
    bottom: 23px;
    line-height: 10px;
}

.tutorialVideoIcon:hover .codeTutorial.toolTip{
  visibility: visible;
  margin-left: 80px;
}

.codeTutorial{
    top: 25;
    left: 75px;
    line-height: 12px;
}

.pasteModal {
  top: 25px;
  width: max-content;
  line-height: 12px;
}

#multiSelectorContainer {
  height: calc(100% - 112px);
  overflow: scroll;
  overflow-y: auto;
  display: none;
  width: 94%;
  background-color: #9e9e9e17;
}

#smartMaintenance {
  height: calc(100% - 80px);
  overflow: scroll;
  overflow-y: auto;
  display: none;
  width: 94%;
  background-color: #9e9e9e17;
}

table {
  font-family: arial, sans-serif;
  border-collapse: collapse;
  width: 100%;
  border: none;
}

thead {
  width: 100%;
}

thead tr{
  background-color: #b8dab1;
  width: 100%;
}

tbody {
  overflow: scroll;
  overflow-y: auto;
  width: 100%;
}

tr {
  width: 100%;
  border: none;
}

td {
  border: 0px solid #dddddd;
  text-align: left;
  padding: 7px;
  font-size: 11px !important;
  white-space:nowrap;
  text-overflow:ellipsis;
  overflow: hidden;
}

td:focus {
    text-overflow: initial;
}

th {
  border: 1px solid #dddddd;
  text-align: left;
  padding: 7px;
  font-size: 10px !important;
  border-top: none;
  border-left: none;
  position: sticky; 
  top: 0px;
}

tr:nth-child(odd) {
  background-color: #dddddd;
}

#multiSelectorRow {
  /*background-color: #b4d6f1;*/
  background-color: #f29a007a;
  border: 1px solid white;
}

tr td:first-child:before
{
  counter-increment: Serial;      /* Increment the Serial counter */
  content: counter(Serial); /* Display the counter */
  font-size: 11px !important;
}

#row-copy-btn {
  background-image: url(../icons/copy_black.svg);
  height: 12px;
  background-color: #c7deec1a;
  border: none;
  margin-left: 2px;
  margin-top: 3px;
  display: none;
}

#row-copy-btn-SM {
  background-image: url(../icons/copy_black.svg);
  height: 14px;
  width: 14px;
  background-color: #c7deec1a;
  border: none;
  margin-left: 2px;
  margin-top: 3px;
}

#row-edit-btn {
    background-image: url(../icons/edit_black.svg);
    height: 12px;
    background-color: #c7deec1a;
    border: none;
    margin-left: 3px;
    display: none;
}

#row-edit-btn-SM {
  background-image: url(../icons/edit_black.svg);
  height: 14px;
  width: 14px;
  background-color: #c7deec1a;
  border: none;
  margin-left: 3px;
}

#row-edit-btn-SM:hover {
  background-image: url(../icons/edit_blue.svg);
}

#delButton {
  background-image: url(../icons/delete_black.svg);
  overflow: hidden;
  height: 13px;
  width: 13px;
  border: none;
  cursor: pointer;
  margin-left: 3px;
  background-color: #ffffff;
}

tr:nth-child(odd) #delButton {
  background-color: #dddddd;
}

.row-no-header {
  width: 4px;
  white-space:nowrap;
  text-overflow:ellipsis;
  text-align: center;
}

.selectorName-header {
  min-width: 70px;
}

.selector-header {
  max-width: 150px;
}

.headerExportBtn {
  width: 75px;
}

tr td:nth-child(1){
  max-width: 13px;
}
tr td:nth-child(2){
  max-width: 80px;
}
tr td:nth-child(3){
  max-width: 150px;
}
tr td:nth-child(4){
  max-width: 51px;
}
tr td:nth-child(5){
  max-width: 51px;
}
tr td:nth-child(6){
  max-width: 51px;
}
tr td:nth-child(7){
  max-width: 51px;
}
tr td:nth-child(8){
  max-width: 51px;
}
tr td:nth-child(9){
  max-width: 51px;
}
tr td:nth-child(10){
  max-width: 51px;
}

#s-no {
  text-align: center;
}

#row-copy-btn:hover {
  background-image: url(../icons/copy_blue.svg);
}

#row-copy-btn-SM:hover {
  background-image: url(../icons/copy_blue.svg);
}

#row-edit-btn:hover {
  background-image: url(../icons/edit_blue.svg);
}

#row-edit-btn_SM:hover {
  background-image: url(../icons/edit_blue.svg);
}

#delButton:hover {
  background-image: url(../icons/delete_red.svg);
}

.addDriverCommand {
  background-image: url(../icons/addDriverCommand_black.svg);
  overflow: hidden;
  background-color: white;
  height: 14px;
  width: 14px;
  border: none;
  /*margin-left: 11px;
  margin-top: 3px;*/
  cursor: pointer;
  display: inline-block;
  padding-bottom: -1px;
  margin-bottom: 6px;
}

.addDriverCommand:hover .driverCommand.toolTip{
  visibility: visible;
}

.driverCommand{
  top: 27px;
  right: 15px;
  line-height: 10px;
  width: max-content;
}

.addDriverCommand.active {
  background-image: url(../icons/addDriverCommand_blue.svg) !important;
}

.addDriverCommand.inactive {
  background-image: url(../icons/addDriverCommand_black.svg) !important;
}

.addDriverCommand.inactive:hover {
  background-image: url(../icons/addDriverCommand_blue.svg) !important;
}

.p1-label {
  color: #008000;
}

.p2-label {
  color: #008000;
}

.abs-label {
  color: #d25709;
}

.v1-label {
  color: #9133d4;
}

.v2-label {
  color:#c75353;
}

.v3-label {
  color:#3070eff5;
}

.v4-label {
  color:#d05e07;
}

.attributeFilter {
  /*display: "none";*/
  height: 25px;
  overflow: auto;
  background-color: #f29a007a;
  /*border-top: 1px solid #333;*/
}

.chooseAttr {
  outline: none;
  vertical-align: middle;
}

.chooseAttr.user {
  width: calc(100% - 347px);
  height: 17px;
  margin: 3 6px 3px 6px;
  /*background: #e8e7e7;*/
  border: 1px solid #c7c7c7;
  border-radius: 2px;
}

.attrFilterTutorialIcon {
  background-image: url(../icons/info_black.svg);
  overflow: hidden;
  width: 14px;
  height: 14px;
  background-color: #c7deec1a;
  border: none;
  cursor: pointer;
  vertical-align: middle;
}

.attrFilterTutorialLink:hover .attrFilterTutorialIcon {
  background-image: url(../icons/info_blue.svg);
}

.attrFilterTutorialLink:hover .attrFilterTutorial.toolTip {
  visibility: visible;
}

.attrFilterTutorial {
  width: max-content;
  right: 27px;
  top: 76px;
}

label {
  font-family: arial, sans-serif;
  font-size: 12px !important;
  margin: 4px 6px 3px 9px;
}

.pre-command {
  margin-top: -52px;
  padding-bottom: -34px;
  width: calc(100% - 369px);
  height: 17px;
  display: inline-block;
  /*visibility: hidden;*/
  outline: none;
  /*background: #e8e7e7;*/
  border: 1px solid #c7c7c7;
  border-radius: 2px;
    
}

.selectorsHubLogo {
    background-image: url(../icons/selectorsHub_logo.svg);
    overflow: hidden;
    height: 11px;
    width: 10px;
    border: none;
    margin-left: 2px;
    padding-right: 1px;
    cursor: pointer;
    background-color: #8fd4f7e6;
}

.userFormLink {
  width: 31px;
  height: 27px;
  overflow: hidden;
  position: fixed;
  bottom: -2px;
  right: -6px;
  display: inline;
}

.userFormIcon{
  display: block;
  width: -33px;
  height: 90%;
  /*background-image: url(../icons/support.svg);*/
  background-image: url(../icons/userFormIcon.svg);
  border: none;
  display: block;
  width: 77%;
  animation: blink 2s ease-in infinite;
  cursor: pointer;
}

@keyframes blink {
  from, to { opacity: 1 }
  50% { opacity: 0.5 }
}

.tablePlaceholder {
  display: flex;
  justify-content: center;
  align-items: center;
  /*text-align: center;*/
  font-size: 10px !important;
  color: #989696;
  width: 100%;
  height: calc(100% - 50px);
  white-space: pre;
}

.copyToolTip {
  visibility: hidden;
  background-color: #6d6c6a;
  text-align: center;
  border-radius: 6px;
  padding: 2px 3px;
  position: absolute;
  font-size: 9px !important;
  z-index: 1;
  color:white;
}

a {
  text-decoration: none;
  color: black;
}

a:hover {
  color: #ff0230;
  /*color:#0aa0ecf2;*/
}

.show {
  visibility: visible;
  animation: fadeIn 1s;
}

.selectorsGenerator {
  /*height: 125px;*/
  max-height: 125px;
  overflow: auto;
  width: 94%;
  border: 1px solid #aba9a921;
}

.noOfMatch {
  padding: 0px 4px 0px 4px;
  border-radius: 7px;
  color: white;
  font-size: 10px !important;
  margin-left: 6px;
  border: 1px solid white;
  background-color: #9e9e9e17;
}

.matchingNodesContainer{
  display: inline-block;
  width: 26px;
}

.warningIcon {
    background-image: url(../icons/alert.svg);
    height: 13px;
    border: none;
    cursor: pointer;
    background-color: #e4edf31a;
    position: relative;
    top: -3px;
}

.toolTip {
  visibility: hidden;
  background-color: #6d6c6a;
  color: #fff;
  position: absolute;
  z-index: 1;
  text-align: center;
  border-radius: 2px;
  padding: 5px 6px;
  font-size: 11px !important;
}

.alert {
  /*width: 300px;*/
  top: 10px;
  left: 14px;
}

.warningIcon:hover .alert.toolTip {
  visibility: visible;
}

.nodesCount {
  padding: 0px 5px 0px 5px;
  background-color: #eb4444;
  border-radius: 23px;
  color: white;
  font-size: 12px !important;
  margin-left: 2px;
  margin-right: 5px;
}

.nodesCountZero {
  background-color: #e92222;
}

.nodesCountOne {
  background-color: #0cb9a9;
}

.nodesCountDef {
  background-color: #f78f06;
}

.modal {
  display: none; 
  position: fixed; 
  z-index: 500; 
  left: 0;
  top: 0;
  width: 100%; 
  height: 100%; 
  overflow: auto; 
  background-color: rgba(77, 77, 77, 0.7); 
}

.modal { 
  animation-name: zoom;
  animation-duration: 0.6s;
}

.modal>div{
  display: table;
  width: 100%; 
  height: 100%; 
}

.modal-content{
  display:table-cell; 
  vertical-align:middle;
}

@keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}

.modalHeader{
  text-align: center;
  width: 100%;
  height: 40px;
  border-bottom: 1px solid #979797;
  font-size: 26px !important;
  padding-top: 8px;
  font-family: montserrat;
}

.modalTitle{
  text-align: center;
  width: 100%;
  font-size: 14px !important;
  padding-top: 15px;
}

.modalHeader h3{
  font-size: 40px !important;
}

.close {
  margin: auto;
  /* max-width: 1000px; */
  text-align: right;
  width: 85%;
  min-width: 300px;
}

.close span{
  color: rgb(253,254,254);
  font-size: 40px !important;
  /* font-weight: lighter; */
  transition: 0.3s;
}

.close span:hover,
.close span:focus {
  color: rgb(187,187,187);
  text-decoration: none;
  cursor: pointer;
}

.addNewModal {
  display: none;
  position: fixed; 
  z-index: 10;
  left: 0;
  top: 0;
  width: 100%; 
  height: 100%; 
  overflow: auto;
  background-color: rgba(74,74,74,0.7); 
}

.modal-contents {
   background-color: #FFFFFF;
   margin: 0 auto;
   padding: 20px;
   width: 75%;
   min-width: 260px;
   max-width: 500px;
   border-radius: 10px;
}
.modal-title {
   text-align: center;
   margin: 0 auto;
   color: #000000;
   font-family: sans-serif;
   font-size: 18px !important;
   letter-spacing: -0.22px;
   line-height: 29px;
}
.enterNewTitle {
  height: 29px;
  width: -moz-available;  
  width: -webkit-fill-available;
  border: 1px solid #979797;
  background-color: #FFFFFF;
  /* padding-left: 15px; */
  max-width: 100%;
  min-height: 80px;
  border-radius: 7px;
}

.enterNewTitle:focus-visible {
  border: 2px solid #f29a00a8;
  outline: none;
}

.enterLabels {
  height: 29px;
  width: -moz-available;  
  width: -webkit-fill-available;
  border: 1px solid #979797;
  background-color: #FFFFFF;
  /* padding-left: 15px; */
  max-width: 100%;
  padding: 18px 4px;
  margin-bottom: 8px;
  border-radius: 7px;
}

.enterLabels:focus-visible {
  border: 2px solid #f29a00a8;
  outline: none;
}


.new-title-label {
   color: #000000;
   font-family: sans-serif;
   font-size: 14px !important;
   letter-spacing: -0.22px;
   line-height: 40px;
   height: 40px;
}

.modal-footer {
   padding: 30px 5px 5px 5px;
   text-align: right;
   display: flex;
   flex-direction: row;
   justify-content: flex-end;
}

.btn-cancel {
   height: 27px;
   width: 73px;
   border: 1px solid #f29a00a8;
   border-radius: 6px;
   background: transparent;
   color: #f29a00a8;
   font-family: sans-serif;
   font-size: 14px !important;
   letter-spacing: -0.22px;
   line-height: 18px;
   margin-right: 7px;
   cursor: pointer;
}

.vertical-align-center {
  display: table-cell;
  vertical-align: middle;
}

.btn-cancel:focus {
   text-decoration: none;
   outline: none;
   border: 1px solid #1E69CC;
   box-shadow: none;
}

.btn-submit {
  height: 27px;
  width: 73px;
  border-radius: 6px;
  border: 1px solid #f29a00a8;
  background-color: #f29a00a8;
  color: #FFFFFF;
  font-family: sans-serif;
  font-size: 14px !important;
  letter-spacing: -0.22px;
  line-height: 18px;
  cursor: pointer;
}

#commandValidation {
  color: #ff0000;
  font-size: 11px !important;
  margin-bottom: 8px;
  width: 100%;
  text-align: center;
}

#xpathValueAlertMsg {
  visibility: hidden;
  min-width: 250px;
  margin-left: -125px;
  background-color: #333;
  color: #fff;
  text-align: center;
  border-radius: 2px;
  padding: 16px;
  position: fixed;
  z-index: 1;
  left: 45%;
  bottom: 30px;
  font-size: 14px !important;
  height: 13px;
  top: 42%;
}

#xpathValueAlertMsg.show {
  visibility: visible;
  -webkit-animation: fadein 0.5s, fadeout 0.5s 2.5s;
  animation: fadein 0.5s, fadeout 0.5s 2.5s;
}

@-webkit-keyframes fadein {
  from {bottom: 0; opacity: 0;} 
  to {bottom: 30px; opacity: 1;}
}

@keyframes fadein {
  from {bottom: 0; opacity: 0;}
  to {bottom: 30px; opacity: 1;}
}

@-webkit-keyframes fadeout {
  from {bottom: 30px; opacity: 1;} 
  to {bottom: 0; opacity: 0;}
}

@keyframes fadeout {
  from {bottom: 30px; opacity: 1;}
  to {bottom: 0; opacity: 0;}
}

.commandTip {
  height: 15px;
  width: 15px;
  background-image: url(../icons/support_blue.svg);
  border: none;
  display: inline-block;
  cursor: pointer;
  background-color: transparent;
  background-repeat: no-repeat;
  margin-left: 6px;
  position: absolute;
  margin-top: 5px;
}


.commandTip:hover .questionMark.toolTip {
  visibility: visible;
}

.questionMark{
    padding: 5px 6px;
    top: 22px;
    right: 0px;
    width: 300px;
}

[contenteditable="true"].editableContent {
  white-space: nowrap;
  overflow: hidden;
} 
[contenteditable="true"].editableContent br {
  display:none;

}
[contenteditable="true"].editableContent * {
  display:inline;
  white-space:nowrap;
}

.removeOutline {
  outline: none;
}

button:active, button:focus {
  outline: 0;
  border: none;
  -moz-outline-style: none;
  box-shadow: none;
}

.commandTip:hover .info.toolTip{
  visibility: visible;
}

.info{
    top: 46px;
    right: 1px;
    line-height: 10px;
}

.autocomplete-items div {
  padding: 6px;
  cursor: pointer;
  background-color: #fff;
  border-bottom: 1px solid #d4d4d4;
}

/*when hovering an item:*/
.autocomplete-items div:hover {
  background-color: #e9e9e9;
}

/*when navigating through the items using the arrow keys:*/
.autocomplete-active {
  background-color: #1e90ffd9 !important;
  color: white !important;
}

input[type=checkbox] {
     background: #fbf8f8;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 14px;
    width: 14px;
    border: 1px solid white;
    /*border-radius: 8px;*/
}

input[type=checkbox]:checked {
     background-image: url(../icons/checkbox.svg);
}

input {
  color: #4c4a4a;
  font-size: 12px !important;
  /*font-family: sans-serif;*/
}
::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: gray;
  font-size: 11px !important;
}

::-moz-placeholder { /* Firefox 19+ */
  color: gray;
  font-size: 12px !important;
}

.shub-generator {
  /*display: none;*/
}

.configOptions {
  position: fixed;
  display: inline-block;
  height: 94px;
  width: 10px;
  margin-top: 6px;
  margin-left: 96%;
}

.donationLink {
  font-size: 8px !important;
  margin-left: 3px;
}

.configOptions:before, .child {
    display:inline-block;
    vertical-align:middle;
}
.parent:before {
    content:""; /* so that it shows up */
    height:100%; /* so it takes up the full height */
}

.debugBtn {
  background-image: url(../icons/debug_black.svg);
  overflow: hidden;
  background-color: white;
  height: 16px;
  width: 16px;
  border: none;
  cursor: pointer;
  display: inline-block;
  padding-bottom: -1px;
  margin-bottom: 6px;
  margin-left: -1px;
}

.debugBtn.active {
  background-image: url(../icons/debug_blue.svg) !important;
}

.debugBtn.inactive {
  background-image: url(../icons/debug_black.svg) !important;
}

.debugBtn:hover .debug.toolTip{
  visibility: visible;
}

.debug{
    top: 68px;
    right: 15px;
    line-height: 10px;
    width: max-content;
}

.debugBtn.inactive:hover {
  background-image: url(../icons/debug_blue.svg) !important;
}


.attrFilterBtn {
  background-image: url(../icons/attrFilter_black.svg);
  overflow: hidden;
  background-color: white;
  height: 14px;
  width: 14px;
  border: none;
  cursor: pointer;
  display: inline-block;
  padding-bottom: -1px;
  margin-bottom: 6px;
}

.attrFilterBtn.active {
  background-image: url(../icons/attrFilter_blue.svg) !important;
}

.attrFilterBtn.inactive {
  background-image: url(../icons/attrFilter_black.svg) !important;
}

.attrFilterBtn:hover .filter.toolTip{
  visibility: visible;
}

.filter{
  top: 7px;
  right: 15px;
  line-height: 10px;
  width: max-content;
}

.attrFilterBtn.inactive:hover {
  background-image: url(../icons/attrFilter_blue.svg) !important;
}

.footer {
  padding-top: 4px;
  border-top: 0.5px solid #8080806b;
  font-size: 9px !important;
  color: #f29a00;
  text-align: justify;
  width: 100%;
  white-space: nowrap;
  padding-left: 2px;
  padding-bottom: 5px;
  background: #ececec;
  position: absolute; /*this is added to remove transparent background*/
  bottom: -1px;
}

.homeIcon {
    background-image: url(../icons/selectorsHublogo.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.home{
    bottom: 23px;
    line-height: 10px;
}

.homeIcon:hover .home.toolTip{
  visibility: visible;
}

.creatorIcon {
    background-image: url(../icons/creator.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.creator{
    bottom: 23px;
    line-height: 10px;
}

.creatorIcon:hover .creator.toolTip{
  visibility: visible;
}

.downloadIcon {
    background-image: url(../icons/safari.svg);
    /*background-image: url(../icons/download_black.svg);*/
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.downloadIcon:hover{
    background-image: url(../icons/safari.svg);
    /*background-image: url(../icons/download.svg);*/
}

.download{
    bottom: 23px;
    line-height: 10px;
}

.downloadIcon:hover .download.toolTip{
  visibility: visible;
}

.telegramIcon {
    background-image: url(../icons/telegram_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.telegramIcon:hover {
    background-image: url(../icons/telegram_blue.svg);
}

.telegram{
    bottom: 23px;
    line-height: 10px;
}

.telegramIcon:hover .telegram.toolTip{
  visibility: visible;
}

.twitterIcon {
    background-image: url(../icons/twitter_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.twitterIcon:hover {
    background-image: url(../icons/twitter_blue.svg);
}

.twitter{
    bottom: 23px;
    line-height: 10px;
}

.twitterIcon:hover .twitter.toolTip{
  visibility: visible;
}

.linkedinIcon {
    background-image: url(../icons/linkedin_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.linkedinIcon:hover {
    background-image: url(../icons/linkedin_blue.svg);
}

.linkedin{
    bottom: 23px;
    line-height: 10px;
}

.linkedinIcon:hover .linkedin.toolTip{
  visibility: visible;
}

.facebookIcon {
    background-image: url(../icons/facebook_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.facebookIcon:hover {
    background-image: url(../icons/facebook_blue.svg);
}

.facebook{
    bottom: 23px;
    line-height: 10px;
}

.facebookIcon:hover .facebook.toolTip{
  visibility: visible;
}

.instagramIcon {
    background-image: url(../icons/instagram_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.instagramIcon:hover {
    background-image: url(../icons/instagram_blue.svg);
}

.instagram{
    bottom: 23px;
    line-height: 10px;
}

.instagramIcon:hover .instagram.toolTip{
  visibility: visible;
}

.githubIcon {
    background-image: url(../icons/github_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.githubIcon:hover {
    background-image: url(../icons/github.svg);
}    

.github{
    bottom: 23px;
    line-height: 10px;
}

.githubIcon:hover .github.toolTip{
  visibility: visible;
}

.bugasuraIcon {
    background-image: url(../icons/bugasura.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.bugasuraIcon:hover {
    background-image: url(../icons/bugasura.svg);
}    

.bugasura{
    bottom: 23px;
    line-height: 10px;
}

.bugasuraIcon:hover .bugasura.toolTip{
  visibility: visible;
}

.reviewIcon {
    background-image: url(../icons/rating_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.review-link:hover .reviewIcon{
    background-image: url(../icons/rating_blue.svg);
}

.review-link:hover .review.toolTip{
  visibility: visible;
}

.review {
  bottom: 23px;
  line-height: 10px;
}

.sponsorIcon {
    background-image: url(../icons/testProject.svg);
    overflow: hidden;
    background-color: transparent;
    height: 12px;
    width: 12px;
    border: none;
    cursor: pointer;
}


.sponsors-page {
  margin: 0 0 0 10px !important;
}

.sponsor-link {
  margin: 0px 5px 0px 0px !important;
  color: white;
  background: #f44336 !important;
}

.sponsor-link:hover {
  color: black;
}

.sponsor-link:hover .sponsorIcon{
    background-image: url(../icons/testProject.svg);
}

.sponsor-link:hover .sponsor.toolTip{
  visibility: visible;
}

/*.sponsor {
  bottom: 23px;
  line-height: 10px;
}*/

.svgIcon {
  padding: 4px;
}

.videoIcon{
    height: 13px;
    width: 13px;
    background-image: url(../icons/youtube_black.svg);
    border: none;
    cursor: pointer;
    background-color: transparent;
}

.videoIcon:hover {
  background-image: url(../icons/youtube_red.svg);
}

.videoIcon:hover .tutorial.toolTip{
  visibility: visible;
}

.sponsorHeader {
  color: black;
}

/*.sponsorHeader:hover {
  color: #ff0230;
}*/

.sponsorName {
  color: red;
}

.slackIcon{
    height: 13px;
    width: 13px;
    background-image: url(../icons/slack_black.svg);
    border: none;
    cursor: pointer;
    background-color: transparent;
}

.slackIcon:hover {
  background-image: url(../icons/slack-logo.svg);
}

.slack{
    bottom: 23px;
    line-height: 10px;
}

.slackIcon:hover .slack.toolTip{
  visibility: visible;
}

#autoxpath:after {
  background: #6eb771; 
  border-radius: 7px; 
  padding: 0 5px 2px 5px; 
  content: "xpath";
  color: white;
  position: absolute;
  right: 5px;
  font-size: 10px !important;
}

#autocss:after {
  background: #5cc0ec;
  border-radius: 7px; 
  padding: 0 5px 2px 5px; 
  content: "css";
  color:white;
  position: absolute;
  right: 5px;
  font-size: 10px !important;
}

.suggestedMatchingNode {
  background: red;
  border-radius: 8px;
  padding: 1 5 2 5px;
  color: white;
  margin-right: 3px;
  margin-left: -2px;
  font-size: 10px !important;
}

.suggestedMatchingNodeWindows {
  background: red;
  border-radius: 8px;
  padding: 0 5 1 5px;
  color: white;
  margin-right: 3px;
  margin-left: -2px;
  font-size: 10px !important;
}

.node0 {
  background: red;
}

.node1 {
  background: #0cb9a9;
}

.node2 {
  background: #f29a00;
}

.nodeHide {
  display: none;
}

.errorInfo {
  background: #f29a00a8;
  color: black;
  border-radius: 6px;
  padding: 1px 5px 3px 5px;
  font-size: 10px !important;
  margin-left: 3px;
  vertical-align: -webkit-baseline-middle;
  max-width: calc(100% - 280px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
}

.infoPlaceholder {
  justify-content: center;
  font-size: 12px !important;
  color: #797676;
  width: 100%;
  white-space: pre;
  background: white !important;
  height: 1px;
  /*font-family: auto;*/
  vertical-align: center;
  display: none;
}

.infoPlaceholder a:hover{
  color: dodgerblue;
}

.version {
    font-size: 9px !important;
    color: gray;
}   

.faq{
    bottom: 23px;
    line-height: 10px;
}

.faqLink:hover .faq.toolTip{
  visibility: visible;
}

.cheetSheetLink {
  vertical-align: -webkit-baseline-middle;
}

.cheetSheet {
  background-image: url(../icons/information.svg);
  /*overflow: hidden;*/
  background-color: #ffffff;
  height: 17px;
  width: 17px;
  border: none;
  /*margin-top: 2px;*/
  display: inline-block;
  cursor: pointer;
  /*padding-bottom: 5px;*/
  vertical-align: middle;
}

.cheetSheetLink:hover .cheetSheetBtn.toolTip{
  visibility: visible;
}

.cheetSheetBtn{
    top: 52px;
    line-height: 10px;
}

.xpathFunctionIcon{
    background-image: url(../icons/info_black.svg);
    overflow: hidden;
    background-color: #18800000;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
}

.xpathFunctionLink:hover .xpathFunctionIcon{
  background-image: url(../icons/info_blue.svg);
}

.xpathFunctionLink:hover .functionInfo.toolTip{
  visibility: visible;
}

.functionInfo{
    bottom: 23px;
    line-height: 10px;
}

.uiSetting-btn{
  background-image: url(../icons/uiSetting_black.svg);
  height: 15px;
  width: 15px;
  background-color: white;
  border: none;
  /* margin-left: 5px; */
  cursor: pointer;
  /*display: inline-block;*/
  /*display: none;*/
  /*margin-top: 5px;*/
}

.uiSetting-btn:hover {
    background-image: url(../icons/uiSetting_blue.svg) !important;
}

.uiSetting-btn:hover .uiSetting.toolTip{
  visibility: visible;
}

.uiSetting{
    top: 130px;
    right: 15px;
    line-height: 10px;
    width: max-content;
}

.driver-command {
  width: 92%;
  border: 1px solid #ada9a9;
  margin: 4px 6px 4px 6px;
  outline: none;
  /*display: none;*/
}

.xpath-header {
  min-width: 240px;
}

.cssSelector-header {
  min-width: 115px;
}

.driver-command-box {
  display: none;
  background-color: #f29a007a;
}

.copyContainer {
  display: inline;
}

.patronRequest {
  bottom: 0px;
  /*background: #ffdca0;*/
  /*background: #5ecec3;*/
  /*color: white !important;*/
  font-size: 10px !important;
  /*padding-top: 2px;*/
  border-top: 0.5px solid #8080806b;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  position: absolute; 
  background-color: #ffffff;
  /*display: none;*/
  display: flex;
  justify-content: center;
  align-items: center;
}

.patronRequest a {
  color: #8a8585;
  margin-right: 2px;
  margin-left: 2px;
  /*padding: 1px 7px 2px 7px;*/
}

/*.patronRequest a:hover {
  color: white;
}*/

.requestHeader {
  /*color: white;*/
  background: #ffffff;
  padding: 1px 3px 2px 2px;
  border-radius: 2px;
  margin: 0 0px;
}

.closeIcon {
    background-image: url(../icons/close_white.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
    /*position: absolute;*/
    /*top: 4px;*/
    margin-right: 5px;
    margin-left: 5px;
    display: none;
    vertical-align: text-top;
}


.closeIcon:hover {
    background-image: url(../icons/close_black.svg);
}


.ignoreCaseBtn {
  background-image: url(../icons/ignoreCase_black.svg);
  overflow: hidden;
  background-color: white;
  height: 14px;
  width: 14px;
  border: none;
  cursor: pointer;
  display: inline-block;
  margin-left: calc(100% - 58px);
  margin-top: -15px;
}

.ignoreCaseBtn.active {
  background-image: url(../icons/ignoreCase_blue.svg) !important;
}

.ignoreCaseBtn.inactive {
  background-image: url(../icons/ignoreCase_black.svg) !important;
}

.ignoreCaseBtn:hover .ignoreCase.toolTip{
  visibility: visible;
}

.ignoreCase{
    top: 31px;
    right: 70px;
    line-height: 10px;
    width: max-content;
}

.ignoreCaseBtn:hover {
  background-image: url(../icons/ignoreCase_blue.svg) !important;
}

#nestedCode {
  min-width: calc(100% - 90px);
  min-height: 50px;
  font-family: ui-monospace;
  color: #7b7878;
  line-height: 18px;
}

.nestedCodeCopyBtn {
  vertical-align: top;
  margin-top: 20px;
}

.domContentCopyBtn {
  vertical-align: top;
  margin-top: 12px;
  margin-left: 6px;
  display: none;
}

#selectorHeader {
  vertical-align: -webkit-baseline-middle;
}

.selectorsCount {
  vertical-align: -webkit-baseline-middle; 
}

#nestedBlock {
  display: none;
}

/* The review Modal (background) */
.reviewModal {
  display: none;
  position: fixed;
  z-index: 1;
  bottom: 0;
  width: 95%;
}

/* review Modal Content */
.review-modal-content {
  background-color: #fefefe;
  margin: auto;
  border: 1px solid #888;
  width: 100%;
  border-radius: 20px;
  text-align: center;
  font-size: 13px !important;
  padding: 13px;
}

.reviewStars {
  font-size: 20px !important;
}

/* The review modal Close Button */
.reviewClose {
  background-image: url(../icons/close_black.svg);
  overflow: hidden;
  background-color: transparent;
  height: 13px;
  width: 13px;
  border: none;
  cursor: pointer;
  /*position: absolute;*/
  /*top: 4px;*/
  margin-right: 5px;
  margin-left: 5px;
  /*display: none;*/
  vertical-align: text-top;
}

.reviewClose:hover, 
.reviewClose:focus {
  background-image: url(../icons/close_red.svg);
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

#reviewModal a {
  color: red;
}


/* The instruction Modal (background) */
.instructionModal {
  display: none;
  position: fixed;
  z-index: 1;
  bottom: 0;
  width: 95%;
}

/* tweet Modal Content */
.instruction-modal-content {
    /*background-color: #cccccc;*/
    background-color: red;
    margin: auto;
    border: 1px solid #888;
    width: 100%;
    border-radius: 20px;
    text-align: center;
    font-size: 13px !important;
    padding: 13px;
}

.instructionStars {
  font-size: 20px !important;
}

.instructionLink2 {
  color: white;
}

/* The tweet modal Close Button */
.instructionClose {
    background-image: url(../icons/close_white.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
    /*position: absolute;*/
    /*top: 4px;*/
    margin-right: 5px;
    margin-left: 5px;
    /*display: none;*/
    vertical-align: text-top;
}

.instructionClose:hover, 
.instructionClose:focus {
  background-image: url(../icons/close_black.svg);
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

#instructionModal a:hover {
  color: black;
}



/* The tweet Modal (background) */
.tweetModal {
  display: none;
  position: fixed;
  z-index: 1;
  bottom: 0;
  width: 95%;
}

/* tweet Modal Content */
.tweet-modal-content {
    background-color: #fefefe;
    margin: auto;
    border: 1px solid #888;
    width: 100%;
    border-radius: 20px;
    text-align: center;
    font-size: 13px !important;
    padding: 13px;
}

.tweetStars {
  font-size: 20px !important;
}

/* The tweet modal Close Button */
.tweetClose {
    background-image: url(../icons/close_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 13px;
    width: 13px;
    border: none;
    cursor: pointer;
    /*position: absolute;*/
    /*top: 4px;*/
    margin-right: 5px;
    margin-left: 5px;
    /*display: none;*/
    vertical-align: text-top;
}

.tweetClose:hover, 
.tweetClose:focus {
  background-image: url(../icons/close_red.svg);
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

#tweetModal a {
  color: red;
}

/* The Modal (background) */
.patronModal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1; /* Sit on top */
  padding-top: 80px; /* Location of the box */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgb(0,0,0); /* Fallback color */
  background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Modal Content */
.patron-modal-content {
  background-color: #fefefe;
  margin: auto;
  padding: 12px 16px 18px 40px;
  border: 1px solid #888;
  width: 80%;
  border-radius: 25px;
}

/* The Close Button */
.patronClose {
  color: #aaaaaa;
  float: right;
  font-size: 28px !important;
  font-weight: bold;
}

.patronClose:hover, 
.patronClose:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

#patronModal a {
  color: red;
}

#patronModal a:hover{
  font-weight: 900;
}

.plus-btn{
  background-image: url(../icons/plus_black.svg);
  height: 16px;
  width: 16px;
  background-color: white;
  border: none;
  cursor: pointer;
  display: inline-block;
  vertical-align: text-top;
}

.plus-btn:hover {
    background-image: url(../icons/plus_yellow.svg) !important;
}

.plus-btn.active {
    background-image: url(../icons/plus_yellow.svg) !important;
}

.plus-btn:hover .plus.toolTip{
  visibility: visible;
}

.plus{
    top: 8px;
    right: 45px;
    line-height: 10px;
    width: max-content;
}

.savedSelectors {
  display: none;
  position: fixed;
  background-color: rgb(224, 224, 224);
  width: calc(100% - 190px);
  max-height: 78%;
  overflow: hidden scroll;
  z-index: +1;
  font-family: system-ui,sans-serif;
  border: 1px solid #f29a00a8;
  border-radius: 10px;
}

.savedSelectorRow {
  color: black;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 98%;
  overflow: hidden;
  padding: 6px;
  cursor: pointer;
  background-color: #fff;
  border-bottom: 1px solid #d4d4d4;
}

#movingFooter {
  display: inline-block;
  padding-left: 100%;
  animation: marquee 25s linear infinite;
}

.deleteAllSaved {
    background-image: url(../icons/delete_black.svg);
    overflow: hidden;
    background-color: #18800000;
    height: 15px;
    width: 15px;
    border: none;
    margin-left: 24px;
    cursor: pointer;
    display: none;
}

.deleteAllSaved:hover {
    background-image: url(../icons/delete_red.svg);
}

.deleteAllSaved:hover .deleteSaved.toolTip{
  visibility: visible;
  background-color: #EBBA16;
}

.deleteSaved{
    left: 46px;
    line-height: 10px;
    bottom: 8px;
}

.savedSelectorRow.delete{
    text-align: center;
}


#newFeatureLaunch {
  /*color: red;*/
}

.popupHeader {
  /*text-align: center;*/
  font-weight: bold;
  /*color: red;*/
  margin: 0;
  padding: 0;
  margin-bottom: 8px;
}

.countdown-btn {
  border: none;
  border-radius: 20px;
  font-size: 10px;
  background-color: white;
}

.footerCounter {
  color: red;
}

.countdown-btn:hover .countdown.toolTip {
  visibility: visible;
}

.countdown {
  bottom: 23px;
  line-height: 10px;
}

.axes-btn {
  background: #f29a00a8;
  border: none;
  border-radius: 20px;
  vertical-align: -webkit-baseline-middle;
  font-size: 12px;
  cursor: pointer;
}

.axes-btn.inactive {
  background: #c5c3c3;
}

.axes-btn.active {
  background: #f29a00a8;
}

.axes-btn:hover .axes.toolTip{
  visibility: visible;
}

.axes{
    top: 50px;
    line-height: 10px;
    width: max-content;
}

.axesXpathGenerator {
  display: none;
  width: 94%;
  height: calc(100% - 100px);
  overflow: scroll;
  overflow-y: auto;
}

.axesXpath {
  margin: 10 10 20 10px ;
  overflow-wrap: break-word;
}

.lastAxesXpath {
  margin: 10 10 20 10px ;
  overflow-wrap: break-word;
}

.axesContainer {
  display: inline;
  background: #f29a00a8;
  padding: 10px;
  border-radius: 27px;
  margin: 0 0 0px 5px;
}

.axesXpathCopyBtn {
  font-size: 12px !important;
  height: 18px;
  width: 18px;
  /*vertical-align: inherit;*/
}

.lastAxesXpathCopyBtn {
  font-size: 12px !important;
  height: 18px;
  width: 18px;
}

.noOfAxesXpathMatch {
  padding: 0px 4px 0px 4px;
  border-radius: 7px;
  color: white;
  font-size: 12px !important;
  margin-right: 5px;
  border: 1px solid white;
  background-color: #f29a00a8;
}

.noOfLastAxesXpathMatch {
  padding: 0px 4px 0px 4px;
  border-radius: 7px;
  color: white;
  font-size: 12px !important;
  margin-right: 5px;
  border: 1px solid white;
  background-color: #f29a00a8;
}

.axesTutorialIcon {
  background-image: url(../icons/info_black.svg);
  overflow: hidden;
  width: 16px;
  height: 16px;
  background-color: #c7deec1a;
  border: none;
  cursor: pointer;
  vertical-align: text-top;
}

.axesTutorialLink:hover .axesTutorialIcon {
  background-image: url(../icons/info_blue.svg);
}

.axesTutorialLink:hover .axesTutorial.toolTip {
  visibility: visible;
}

.axesTutorial {
  width: max-content;
  width: max-content;
  right: 72px;
}

.parentElement {
  width: 42%;
  border: none;
  margin: 14 7 14 14px;
  padding: 9px;
  border-radius: 20px;
}

.parentElement.active {
  background: #86caff;
  animation: blink 2s ease-in infinite;
}

.childElement {
  width: 42%;
  border: none;
  margin: 14 14 14 7px;
  padding: 9px;
  border-radius: 20px;
}

.childElement.active {
  background: #86caff;
  animation: blink 2s ease-in infinite;
}

.uiConfig {
  display: none;
  overflow: scroll;
  overflow-y: auto;
  height: calc(100% - 100px);
  width: 94%;
}

.choose {
  vertical-align: middle;
}

.uiConfigTutorialIcon {
  background-image: url(../icons/info_black.svg);
  overflow: hidden;
  width: 14px;
  height: 14px;
  background-color: #c7deec1a;
  border: none;
  cursor: pointer;
  vertical-align: middle;
}

.uiConfigTutorialLink:hover .uiConfigTutorialIcon {
  background-image: url(../icons/info_blue.svg);
}

.uiConfigTutorialLink:hover .uiConfigTutorial.toolTip {
  visibility: visible;
}

.uiConfigTutorial {
  width: max-content;
  width: max-content;
  right: 55px;
}

.uiSetting-btn.active {
  background-image: url(../icons/uiSetting_blue.svg);
}

.uiConfig-header {
  background-color: #dddddd;
}

.uiConfig-row {
  background-color: white !important;
}

tr.uiConfig-row td:first-child:before {
    content: none;
}

th.uiConfig-header:nth-child(1) > span {
  margin-left: 28px;
}

th.uiConfig-header:nth-child(2) > span {
  margin-left: 50px;
}

input[type="checkbox"]:not(:checked) {
  border: 1px solid red;
}

#inputToggle {
  margin: 10px;
}

#inputToggle.toggle-btn.active:before {
  content: "";
}

#inputToggle.toggle-btn.inactive:before {
  content: "";
}

.elementInfo {
  background: #eb3030;
  text-align: center;
  display: none;
  height: auto;
  min-height: 23px;
  padding: 2px;
  list-style: none;
}

.elementInfoMsg {
  color: white !important;
  vertical-align: sub;
}

.training {
  color: white;
}

.training:hover {
  color: black !important;
}

.debugTime {
  width: 25px;
  text-align: center;
}

.news-link {
  
}

.savedCommand {
  display: none;
  position: fixed;
  background-color: rgb(224, 224, 224);
  width: calc(100% - 190px);
  max-height: 78%;
  overflow: hidden scroll;
  z-index: +1;
  font-family: system-ui,sans-serif;
  border: 1px solid #f29a00a8;
  border-radius: 10px;
}

.savedCommandRow {
  color: black;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 98%;
  overflow: hidden;
  padding: 6px;
  cursor: pointer;
  background-color: #fff;
  border-bottom: 1px solid #d4d4d4;
}

.testRigorTutorialIcon {
  background-image: url(../icons/info_black.svg);
  overflow: hidden;
  width: 12px;
  height: 12px;
  background-color: #c7deec1a;
  border: none;
  cursor: pointer;
  vertical-align: text-top;
}

.testRigorTutorialLink:hover .testRigorTutorialIcon {
  background-image: url(../icons/info_blue.svg);
}

.testRigorTutorialLink:hover .testRigorTutorial.toolTip {
  visibility: visible;
}

.testRigorTutorial {
  width: max-content;
  left: 46px;
}

.testRigorTutorialLink {
  margin-left: 7px;
}

.requestHeader.newlaunch {
  background-image: url(../icons/new.svg);
  padding: 6px 8px 9px 14px;
}

.user_target {
  text-align: center;
  margin: 10px;
}

.target_link {
  color: red;
}

.maxtaf {
  color: blue;
}

.placeCheck {
  display: none;
}

.upgradeBtn{
  margin-left: 7px;
  border: 1px solid green;
  border-radius: 8px;
  padding: 1px 5px;
  vertical-align: middle;
  color: green;
}

.upgradeBtn:hover .upgrade.toolTip{
  visibility: visible;
}

.upgrade{
  left: 2px;
  top: 81px;
  line-height: 10px;
  width: max-content;
}

@keyframes marquee {
    0%   { transform: translate(0, 0); }
    100% { transform: translate(-100%, 0); }
}

@-moz-document url-prefix() {

    .autocomplete-items div {
      padding: 3px;
      cursor: pointer;
      background-color: #fff;
      border-bottom: 1px solid #d4d4d4;
      font: 14px arial;
    }

    .edit-icon {
      height: 14px;
    }

    .donation-btn {
      margin-top: 0px;
      padding-bottom: 0px;
    }

    .total-match-count {
      margin-left: 46px;
    }
    .footer {
      padding: 3px;
    }
    .footer span {
      vertical-align: sub;
    }
    .review-link {
      vertical-align: sub;
    }
    .reviewIcon {
      vertical-align: bottom;
    }
    /*.svgIcon {
      height: 16px;
      width: 16px;
    }*/

    .editorTitle {
      bottom: 1px;
      margin: 3px 2px 0px 0px;
    }

    #autoxpath:after {
      padding: 1px 5px 2px 5px; 
    }

    #autocss:after {
      padding: 1px 5px 2px 5px; 
    }

    .suggestedMatchingNode {
      padding: 1px 4px 1px 4px;
    }

    .suggestedMatchingNodeWindows {
      padding: 1px 4px 1px 4px; 
    }
    .faqLink {
      vertical-align: sub;
    }

    .cheetSheetLink {
      /*position: absolute;
      margin-top: 2px;*/
      /*margin-left: 5px;*/
    }
    .cheetSheetBtn{
      top: 24px;
      width: max-content;
    }

    .errorInfo {
      padding: 1px 5px 2px 5px;
    }

    .attrFilterBtn {
      height: 14px;
    }

    .addDriverCommand {
      height: 14px;
    }

    .reset-btn {
      height: 14px;
    }

    .toggle-btn { 
      margin-left: calc(100% - 33px);
      margin-top: -19px;
    }

    .warningIcon {
      height: 14px;
      width: 14px;
      top: 1px;
      vertical-align: super;
    }

    .sponsorIcon {
        vertical-align: text-top;
    }

    .sponsor-link {
        vertical-align: sub;
    }

    .copy-btn {
      vertical-align: bottom;
    }

    .patronRequest {
      bottom: 0px;
      padding-top: 3px;
    }

    .autosuggest-toggle-btn {
      margin-top: 3px;
    }

    .toggle-btn {
      margin-top: -18px;
    }

    .ignoreCaseBtn {
      margin-top: -25px;
      height: 14px;
    }

    .multiSelectorRecordBtn{
      height: 14px;
    }

    .debugBtn {
      height: 16px;
      width: 16px;
    }

    .quotes-btn {
      height: 14px;
    }

    .expand-btn {
      height: 13px;
    }

    .plus-btn {
      vertical-align: super;  
    }

    .savedSelectorRow.delete {
      list-style-type: none;
    }

    .testRigorTutorialIcon {
      margin-top: 5px;
      height: 13px;
      width: 13px;
    }

    #selectorsHubEleContainer {
      max-height: 200px;
    }

}

.ads__viewer{
  padding: 3px;
  width: 250px;
  overflow: hidden;
  white-space: nowrap;
  flex: 1;
  display: flex;
  align-items: center;
}
.ads__navigation__btn{
  background-size: 6px !important;
  height: 12px;
  width: 12px;
  cursor: pointer;
}
.navigate__next__btn{
  background: url(../icons/next_grey.svg) center no-repeat;
  margin-left: 10px;
}
.navigate__prev__btn{
  background: url(../icons/prev_grey.svg) center no-repeat;
  margin-right: 10px;
}
.ads__text{
  text-align: left;
  animation: scrollHorizontally 13s linear infinite;
}

.ads__viewer img{
  height: 15px;
  margin: 0 2px;
  margin-bottom: -4px;
  object-fit: fill;
}
.ads__seperator{
  padding: 0 16px;
  font-weight: 600;
}

.ads__container{
  position: relative;
  width: 100%;
  height: 26px;
  display: flex;
  justify-content: center;
}

.ad__template{
  position: absolute;
  top: 0;
  bottom: 0;
  max-width: calc(100% - 20px);
  transform: translateX(3000px);
  border: 1px solid #d5d5d5;
  display: flex;
  align-items: center;
  border-radius: 6px;
  padding: 3px 10px;
  transition: .2s ease;
  cursor: pointer;
  text-decoration: none;
}
.translate__left{
  transform: translateX(0);
}
.hide__left{
  transform: translateX(-3000px);
}
.no__transition{
  transition: none;
}
.status__block{
  background-color: red;
  color: #ffffff;
  font-size: 8px;
  padding: 1px 4px;
  margin-right: 5px;
  margin-left: -11px;
  margin-bottom: -14px;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
}
.ad__template .ad__text{
  color: #ffffff;
  font-size: 11px;
  overflow: hidden;
  padding: 3px 0px;
}
.ad__text div{
  color: black;
  line-height: 20px;
}
.ad__btn{
  background-color: #ffe0a6;
  color:black;
  padding: 2px 25px;
  padding-left: 10px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 10px;
  position: relative;
  margin-left: 10px;
  cursor: pointer;
}
.ad__btn p{
  line-height: 17px;
  margin-top: -1px;
}
.btn__icon{
  position: absolute;
  right: 4px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: #000000;
  border-radius: 50%;
}
.btn__icon img{
  height: 10px;
  width: 17px;
  margin: 0;
  margin-top: 3px;
}
.ads__info__icon{
  position: relative;
  /*background: url(../icons/promoted_grey.svg) center no-repeat;*/
  background: url(../icons/ads_toggle.svg) center no-repeat;
  background-size: 22px;
  width: 22px;
  height: 13px;
  margin: 0 5px;
  cursor: pointer;
  border-radius: 6px;
  border: 1px solid #f29a00a8;
}

.ads__info__tooltip{
  display: none;
  position: absolute;
  bottom: 30px;
  left: -78px;
  background-color: black;
  color: white;
  padding: 5px;
  border-radius: 5px;
  pointer-events: none;
  width: 160px;
  white-space: normal;
}
.ads__info__tooltip::before{
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  content: "";
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-top: 8px solid #16130e;
  border-bottom: 8px solid transparent;
  border-right: 8px solid transparent;
  pointer-events: none;
}
.ads__info__icon:hover .ads__info__tooltip{
  display: block;
}

.sponsor {
  border: 1px solid #d5d5d5;
  border-radius: 2px;
}
.extension__id__wrapper{
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgb(0,0,0, .5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;
}
.extension__id{
  background-color: #ffffff;
  position: relative;
  padding: 1rem 2rem;
  text-align: center;
  font-size: 15px;
  border-radius: 5px;
}
.extension__id h4{
  margin-bottom: 1rem;
  margin-top: 0;
}
.extension__id__container{
  display: flex;
  justify-content: center;
  align-items: center;
}
.extension__id__container input{
  padding: 5px;
  font-size: 12px;
  border: 1px solid #aaaaaa;
  width: 175px;
}
.extension__id__container input:focus-visible{
  border: 1px solid #aaaaaa;
  outline: none;
}
.extension__id__copy{
    padding: 5px;
    cursor: pointer;
    border: 1px solid #aaaaaa;
} 
.extension__id__copy .icon{
  background: url(../icons/copy_black.svg) center no-repeat;
  background-size: 14px;
  width: 15px;
  height: 14px;
}
.extension__id__close{
  background: url(../icons/close_black.svg) center no-repeat;
  background-size: 15px;
  width: 15px;
  height: 15px;
  position: absolute;
  right: 10px;
  top: 10px;
  cursor: pointer;
}

