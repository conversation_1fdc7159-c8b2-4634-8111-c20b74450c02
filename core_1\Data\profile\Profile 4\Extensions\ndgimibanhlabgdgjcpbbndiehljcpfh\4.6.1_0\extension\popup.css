/**
 * Created by sa<PERSON><PERSON> kumar on 25/06/2020.
 */

h3, ul {
	margin: 0;
}

body{
	font-family: "roboto";
  font-size: 13px;
  font-weight: 200;
  line-height: 16px;
  margin: 5px;
  padding: 0px;
  background: #ffa50066;
  line-height: 19px;
}

.content {
    margin: 6px;
}

h4 {
	margin: 5px;
}
ul {
	margin-left: -20px;
	width: 300px;
	list-style-type: inherit;
}

li {
    margin-right: 18px;
}

a {
    text-decoration: none;
    color: black;
}

a:hover {
    color: red;
}

.subMenu {
	list-style-type: circle;
    
}

.logo {
	height: 30px;
  width: 30px;
  margin-left: 177px;
}

.demoPic {
	width: 120px;
  height: 40px;
  margin-top: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-left: 97px;
}

.videoLink {
    margin-right: 10px;
}

.videoIcon{
    height: 18px;
    width: 18px;
    background-image: url(../icons/youtube_black.svg);
    border: none;
    cursor: pointer;
    background-color: #ffebcd;
}


.slackLink {
    margin-right: 10px;
}

.slackLink:hover .slackIcon{
  background-image: url(../icons/slack-logo.svg);
}

.slackIcon{
    height: 17px;
    background-image: url(../icons/slack_black.svg);
    border: none;
    width: 17px;
    cursor: pointer;
    background-color: #ffebcd;
}

.infoBlock {
    background-color: #ffebcd;
    color: black;
    padding: 4px;
    line-height: 18px;
    letter-spacing: 1px;
    overflow: hidden;
    border-radius: 3px;
    border: 1px solid black;
}

.footer {
    text-align: center;
    border-top: 1px solid #ccc;
}

.selectorsHubLogo{
    height: 19px;
    margin-top: 7px;
}

.version {
    font-size: 12px;
    color: gray;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.headerContainer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 100px;
    flex: 2;
}

.recorderButton{
    background-image: url(../icons/tcStudio_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 25px;
    width: 25px;
    border: none;
    margin-right: 8px;
    cursor: pointer;
    background-repeat: no-repeat;
}
  
/*.recorderButton:hover{
    background-image: url(../icons/tcStudio_blue.svg);
}*/

.redBlink {
  background-image: url(../icons/tcStudio_red.svg) !important;
  animation: blink 2s ease-in infinite;
}

.recorderButton:hover .tcStudio.toolTip{
  visibility: visible;
  background-color: #6d6b6b;
}

.toolTip {
  visibility: hidden;
  background-color: #6d6b6b;
  color: #fff;
  text-align: center;
  position: absolute;
  z-index: 1;
  text-align: center;
  border-radius: 2px;
  padding: 6px 6px;
  font-size: 11px;
}
.tcStudio{
    top: 40px;
    right: 1px;
    line-height: 10px;
}

.tutorial{
    top: 20px;
    line-height: 10px;
    width: 140px;
}

.videoIcon:hover .tutorial.toolTip{
  visibility: visible;
}

.videoLink:hover .videoIcon {
  background-image: url(../icons/youtube_red.svg);
}

.slack{
    top: 21px;
    right: 8px;
    width: 130px;
    line-height: 13px;
}

.slackIcon:hover .slack.toolTip{
  visibility: visible;
}


button:active, button:focus {
  outline: 0;
  border: none;
  -moz-outline-style: none;
  box-shadow: none;
}

/*.headerContainer {
    display: none;
}*/


.review-link {
  margin-right: 10px;
}

.review-link:hover .reviewIcon {
    background-image: url(../icons/rating_black.svg);
}

.reviewIcon {
    background-image: url(../icons/rating_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 18px;
    width: 18px;
    border: none;
    /*margin-right: 8px;*/
    cursor: pointer;
    background-repeat: no-repeat;
}

.reviewIcon:hover .review.toolTip{
  visibility: visible;
}

.review{
    top: 25px;
    right: 19px;
    width: 95px;
    line-height: 10px;
}

.changelog {
    color: black;
    font-weight: 600;
    margin-left: 3px;
    margin-right: 8px;
    vertical-align: top;
}

.reviewBlock {
    text-align: center;
}

.supportLinks {
    margin: 1px 0px -3px 0px;
}

.sponsors {
    font-size: 12px; 
}

.box {
    background: #ffebcd;
    padding-bottom: 1px;
    border: 1px solid white;
    border-radius: 4px;
    margin-bottom: 5px;
}



@keyframes zoominoutsinglefeatured {
    0% {
        transform: scale(0.7,0.7);
    }
    50% {
        transform: scale(2.2,2.2);
    }
    100% {
        transform: scale(0.7,0.7);
    }
}

.demoPic {
    animation: zoominoutsinglefeatured 5s infinite ;
}

.donation-btn {
  background-image: url(../icons/donation_black.svg);
  overflow: hidden;
  background-color: transparent;;
  height: 18px;
  width: 18px;
  border: none;
  margin-top: 2px;
  display: inline-block;
  cursor: pointer;
  padding-bottom: 5px;
}

.donationLink:hover .donation-btn{
  background-image: url(../icons/donation_blue.svg);
}

.donationLink:hover .donation.toolTip{
  visibility: visible;
}

.donation{
    top: 91px;
    line-height: 10px;
    margin-left: -168px;
}

.tagline {
  position: fixed;
  top: 28px;
  font-size: 10px;
}

h2 {
  margin-top: 7px;
  color: #444242;
}

.profile {
  color: black;
}

.toggle-btn {
  width: 30px;
  height: 14px;
  border-radius: 20px;
  padding: 2px 3px;
  /*margin: -5px 6px 0px 31px;*/
  display: inline-block;
  cursor: pointer;
  line-height: 30px;
  vertical-align: text-bottom;;
  box-sizing: border-box;
  margin-right: -66px;
  /*display: none;*/
  /*margin-left: calc(100% - 36px);
  margin-top: -15px;*/
}

.toggle-btn:hover .toggle.toolTip{
  visibility: visible;
}

.toggle{
  top: 51px;
  right: 92px;
  line-height: 10px;
}


.toggle-btn.active:before {
  color: black;
  font-size: 8px !important;
  position: fixed;
  top: 26px;
}

.toggle-btn.inactive:before {
  color: black;
  font-size: 8px !important;
  position: absolute;
  top: 26px;
  margin-left: 11px;
}

.toggle-btn.active {
    background-color: #16bb94d1;
}

.toggle-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    /*position: absolute;*/
    transition: transform 0.3s ease;
}

.toggle-btn.active .toggle-circle {
    background-color: #FFF;
    transform: translateX(calc(14px));
}

.toggle-btn.inactive .toggle-circle {
    background-color: #524e4e;
    transform: translateX(0px);
}

.toggle-btn.inactive {
    background-color: #b4bbba;
}

.contextMenuFilter {
    margin: 5px 0 7px 0px;
}

input[type=checkbox] {
  /*background-image: url(../icons/facebook_black.svg);*/
     background: #cecdcd;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 14px;
    width: 14px;
    border: 1px solid white;
    /*border-radius: 8px;*/
    vertical-align: bottom;
    outline: none;
}

input[type=checkbox]:checked {
     background-image: url(../icons/checkbox.svg);
}

.contextMenu {
  background: blanchedalmond;
  margin-bottom: 5px;
  padding-left: 4px;
  border-radius: 4px;
  padding-bottom: 5px;
  margin-top: 1px;
  padding-top: 5px;
}

.contextMenuFilter {
  font-size: 12px;
}

.contextMenuHeader {
    margin-left: 6px;
    margin-right: 5px;
}

.sponsorHeader {
  text-align: center;
  margin-bottom: 6px;
}

.sponsorBlock {
  text-align: center;
}

.sponsorIcon {
    background-image: url(../icons/testProject_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 14px;
    width: 81px;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.sponsor-link:hover .sponsorIcon{
    background-image: url(../icons/testProject_red.svg);
}

.sponsor-link:hover .sponsor.toolTip{
  visibility: visible;
}

.sponsor {
    top: 91px;
    line-height: 10px;
    left: 33px;
}

.telegram-link {
  margin-right: 10px;
}

.telegramIcon {
    background-image: url(../icons/telegram_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 18px;
    width: 18px;
    border: none;
    cursor: pointer;
}

.telegramIcon:hover {
    background-image: url(../icons/telegram_blue.svg);
}

.telegram{
    /*bottom: 23px;*/
    line-height: 10px;
}

.telegramIcon:hover .telegram.toolTip{
  visibility: visible;
}

.git-link {
  margin-right: 10px;
}

.githubIcon {
    background-image: url(../icons/github_black.svg);
    overflow: hidden;
    background-color: transparent;
    height: 18px;
    width: 18px;
    border: none;
    cursor: pointer;
}

.githubIcon:hover {
    background-image: url(../icons/github.svg);
}    

.github{
    /*bottom: 23px;*/
    line-height: 10px;
}

.githubIcon:hover .github.toolTip{
  visibility: visible;
}

.bugasura-link {
  margin-right: 10px;
}

.bugasuraIcon {
    background-image: url(../icons/bugasura.svg);
    overflow: hidden;
    background-color: transparent;
    height: 19px;
    width: 19px;
    border: none;
    cursor: pointer;
}

.bugasuraIcon:hover {
    background-image: url(../icons/bugasura.svg);
}    

.bugasura{
    bottom: 23px;
    line-height: 10px;
}

.bugasuraIcon:hover .bugasura.toolTip{
  visibility: visible;
}

.home-link {
  margin-right: 8px;
}

.homeIcon {
    background-image: url(../icons/selectorsHublogo.svg);
    overflow: hidden;
    background-color: transparent;
    height: 20px;
    width: 20px;
    border: none;
    cursor: pointer;
}

.home{
    line-height: 10px;
}

.homeIcon:hover .home.toolTip{
  visibility: visible;
}

.trainingInfo {
  background: blanchedalmond;
  margin-bottom: 5px;
  padding-left: 4px;
  border-radius: 4px;
  padding-bottom: 5px;
  margin-top: 1px;
  padding-top: 5px;
  text-align: center;
  font-weight: bold;
}

.certificateInfo {
  background: blanchedalmond;
  margin-bottom: 5px;
  padding-left: 4px;
  border-radius: 4px;
  padding-bottom: 5px;
  margin-top: 1px;
  padding-top: 5px;
  text-align: center;
}

.upgradeBtn{
  margin-right: 10px;
  border: 1px solid green;
  border-radius: 8px;
  padding: 1px 5px;
  vertical-align: middle;
  color: green;
  font-weight: bold;
}

.testcasehubButton{
    background-image: url(../icons/testcasehub.svg);
    overflow: hidden;
    background-color: transparent;
    height: 25px;
    width: 25px;
    border: none;
    margin-right: 8px;
    cursor: pointer;
    background-repeat: no-repeat;
}

.testcasehubButton:hover .testcasehub.toolTip{
  visibility: visible;
  background-color: #6d6b6b;
}

.testcasehub{
    top: 40px;
    right: 1px;
    line-height: 10px;
}

.upgradeBtn:hover .upgrade.toolTip{
  visibility: visible;
}

.upgrade{
  right: 1px;
  top: 40px;
  line-height: 10px;
  width: max-content;
}

