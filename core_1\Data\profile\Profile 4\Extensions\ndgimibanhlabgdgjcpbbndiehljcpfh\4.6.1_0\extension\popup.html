<!-- 
  Created by sa<PERSON><PERSON> k<PERSON><PERSON> on 25/06/2020.
 -->


<!doctype html>
<html>
<head>
    <title>SelectorsHub is an editor to write and verify the XPath and cssSelector.</title>
    <link rel="stylesheet" type="text/css" href="popup.css" />
    <script type="text/javascript" src="jquery.js"></script>
    <script type="text/javascript" src="script.js"></script>

</head>
<body>
	<div class="content">
		<div class="header">
			<h2>SelectorsHub</h2>

			<!-- <a class="logo" target="_blank" href="https://www.selectorshub.com/"><img  src="../icons/selectorsHublogo.svg"></a> -->
			<div class="headerContainer">
				<a class="upgradeBtn" target="_blank" href="https://selectorshub.com/selectorshub-pro/">
				<span class="upgrade toolTip">Upgrade to SelectorsHub Pro, just $1/month.</span>Upgrade</a>
				<a class="testcasehubLink" href="https://selectorshub.com/testcasehub/" target="_blank">
					<button id='testcasehub' class="testcasehubButton">
						<span class="testcasehub toolTip">Click to explore TestCaseHub.</span>
					</button>
				</a>
				<a class="tcsDownloadLink" href="https://bit.ly/tcs_chrome" target="_blank">
					<button id='record' class="recorderButton">
						<span class="tcStudio toolTip">Click to install TestCase Studio.</span>
					</button>
				</a>			
			</div>
			<br>
		</div>
		<div class="contextMenu">
			<span class="contextMenuHeader">ContextMenu</span>
			<div class="toggle-btn active">
				<div class="toggle-circle"></div>
				<span class="toggle toolTip">click to disable SelectorsHub</span>
			</div>
			<!-- <div class="contextMenuFilter">
				<label title="Add/Remove Rel XPath from contextMenu."><input class="chooseSelector relXpath" type="checkbox" title="xpath with text or without text." checked />Rel XPath</label>	
				<label title="Add/Remove Rel cssSelector from contextMenu."><input class="chooseSelector cssSelector" type="checkbox" name="id" />Rel cssSelector</label>
				<label title="Add/Remove JS path from contextMenu."><input class="chooseSelector jspath" type="checkbox" name="class"  />JS path</label>
				<label title="Add/Remove abs XPath from contextMenu."><input class="chooseSelector absXpath" type="checkbox" name="name"  />abs XPath</label>
			</div> -->
		</div>
		<span class="tagline">Innovation Inspired Automation</span>	
		<div class="box">
			<ul><b>Instructions <a target="_blank" title="Click to learn how to use SH." href="https://bit.ly/SH_Instructions">(Learn more..) </a>:</b><br>
				<li>After installing SelectorsHub, restart the browser.</li>
				<li>Open DevTools</li>
				<li>On right side of Elements tab, SelectorsHub will be the last tab as shown in below image. If not visible, expand the sidebar.</li>
				<li>If it doesn't work, 
					<ul class="subMenu">
						<li>Open website in new tab.</li>
						<li>Don't try on blank tab. There should be a url in address bar.</li>
						<li>For more details please checkout <a target="_blank" href="http://bit.ly/SH_FAQ">FAQs here.</a></li>
					</ul>
				</li>
			</ul>
			<br>
			<div class="infoBlock">
				<!-- <div class="sponsorBlock" title="SelectorsHub Level">
					<a target="_blank" class="sponsor-link" href="https://testproject.io/?utm_source=selectorshub&utm_medium=blog&utm_campaign=sj">
						Sponsor:
						<span class="sponsor toolTip">A must try, FREE end-to-end test automation tool.</span>
						<button class='sponsorIcon svgIcon'></button>
					</a>
				</div>
				<br> -->
				<a target="_blank" class="donationLink" href="https://bit.ly/sh_donate">
					<div class="reviewBlock">Please donate & support SelectorsHub.
						<button class='donation-btn'>
							<span class="donation toolTip">Click to Donate.</span>
						</button>
					</div>
				</a>
				
				<div class="supportLinks">

					<a target="_blank" class="changelog" href="http://bit.ly/sh_changelog">Changelog</a>

					<a target="_blank" class="home-link" href="https://www.selectorshub.com/" title="Click to open SelectorsHub home page.">
						<button class='homeIcon svgIcon'>
							<!-- <span class="home toolTip">Click to open SelectorsHub home page.</span> -->
						</button>
					</a>

					<a class="videoLink" target="_blank" href="https://www.youtube.com/playlist?list=PLmRg3gEG2XIZofIjkp3h3eE5FR7GyqB86" title="Watch the video tutorial to make best use of SH.">
						<button class='videoIcon'>
							<!-- <span class="tutorial toolTip">Watch this video tutorial to make best use of SH.</span> -->
						</button>
					</a>
					
					<a target="_blank" class="telegram-link" href="https://t.me/joinchat/FICcWBskxzC1kCa9" title="Join Telegram Group.">
						<button class='telegramIcon'>
							<!-- <span class="telegram toolTip">Join Telegram Group.</span> -->
						</button>
					</a>
					
					<a class="slackLink" target="_blank" href="https://bit.ly/SH_Slack" title="Click to join SelectorsHub slack channel.">
							<button class="slackIcon">
								<!-- <span class="slack toolTip">Click to join SelectorsHub slack channel.</span> -->
							</button>
					</a>
					
					<a target="_blank" class="review-link" href="https://bit.ly/3tRfZnn" title="Click to add rating.">
                       	<button class='reviewIcon'>
                            <!-- <span class="review toolTip">Click to add rating.</span> -->
       					</button>
                    </a>
                    
                    <a target="_blank" class="git-link" href="https://bit.ly/3iNlbFJ" title="Raise issue here at github.">
						<button class='githubIcon svgIcon'>
							<!-- <span class="github toolTip">Raise issue here at github.</span> -->
						</button>
					</a>

					<a target="_blank" class="bugasura-link" href="http://bit.ly/sh_bugasura" title="For better tracking, raise issue here at Bugasura.">
						<button class='bugasuraIcon svgIcon'>
							<!-- <span class="bugasura toolTip">For better tracking, raise issue here at Bugasura.</span> -->
						</button>
					</a>

				</div>	
			</div>
		</div>
		<!-- <div class="trainingInfo">
			<a href="https://bit.ly/selectorshub_training" target="_blank">Register for Live Training by SelectorsHub Creator</a>
		</div> -->
		<div class="trainingInfo">
			<a href="https://bit.ly/shub_training_udemy" target="_blank">Advance course on XPath & Automation by Creator</a>
		</div>
		<!-- <div class="certificateInfo">
			<a href="https://bit.ly/sh_certification" target="_blank">Get your SelectorsHub Certificate, Free</a>
		</div> -->
		<br>
		
		<img class="demoPic" src="../icons/defaultTheme.jpg">
		<br>
		
	</div>
	<div class="footer"> 
		<span class="version">v 4.6.1</span>
	</div>

	<span class="sponsors">Offered by: <a class="profile" target="_blank" href="http://bit.ly/sanjay_twitter">Sanjay Kumar.</a> Want to collaborate, please connect <a class="profile" target="_blank" href="http://bit.ly/sanjay_linkedin">here</a>.</span>

	<script src="./popup.js"></script>
    <!-- <script src='../content-script/browser-polyfill.min.js'></script> -->
</body>

</html>