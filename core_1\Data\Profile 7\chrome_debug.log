[14676:2592:0617/210222.112:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[14676:12148:0617/210225.579:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:2592:0617/210225.736:ERROR:device_event_log_impl.cc(215)] [21:02:25.736] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[14676:2592:0617/210225.827:ERROR:device_event_log_impl.cc(215)] [21:02:25.827] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[14676:2592:0617/210225.829:ERROR:device_event_log_impl.cc(215)] [21:02:25.829] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[14676:2592:0617/210225.844:ERROR:device_event_log_impl.cc(215)] [21:02:25.844] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[14676:2592:0617/210227.106:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[14676:2592:0617/210227.107:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[14676:2592:0617/210230.465:INFO:CONSOLE(0)] "[DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) %o", source: https://www.instagram.com/ (0)
[14676:12148:0617/210230.582:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:2592:0617/210231.027:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:2592:0617/210231.028:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:2592:0617/210231.028:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:2592:0617/210231.028:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:9808:0617/210235.595:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:2592:0617/210237.004:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet//followers/ (0)
[14676:2592:0617/210237.020:INFO:CONSOLE(1)] "The page did not request an origin-keyed agent cluster, but was put in one anyway because the origin 'https://www.instagram.com' had previously been placed in an origin-keyed agent cluster. Update your headers to uniformly request origin-keying for all pages on the origin.", source: https://www.instagram.com/make_money_on_the_internet//followers/ (1)
[14676:2592:0617/210238.998:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:2592:0617/210238.998:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:2592:0617/210238.999:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:2592:0617/210238.999:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[14676:14956:0617/210240.614:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:2592:0617/210244.556:WARNING:controller_impl.cc(1190)] Background download complete, client: 6, completion type: 0, file size:265059
[14676:9808:0617/210245.622:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:14956:0617/210250.635:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:9808:0617/210255.654:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[14676:2592:0617/210257.850:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
