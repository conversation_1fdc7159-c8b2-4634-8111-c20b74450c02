[13032:13168:0617/211226.346:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[13032:4060:0617/211228.804:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13032:13168:0617/211231.058:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13032:13168:0617/211231.059:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13032:13168:0617/211231.059:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13032:13168:0617/211231.059:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[13032:13168:0617/211232.340:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211232.341:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211232.341:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211232.341:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211232.629:INFO:CONSOLE(0)] "[DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) %o", source: https://www.instagram.com/ (0)
[13032:14924:0617/211233.814:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211235.202:ERROR:device_event_log_impl.cc(215)] [21:12:35.203] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[13032:13168:0617/211235.267:ERROR:device_event_log_impl.cc(215)] [21:12:35.267] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[13032:13168:0617/211235.269:ERROR:device_event_log_impl.cc(215)] [21:12:35.269] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[13032:13168:0617/211235.272:ERROR:device_event_log_impl.cc(215)] [21:12:35.272] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[13032:13168:0617/211237.150:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet//followers/ (0)
[13032:13168:0617/211237.292:INFO:CONSOLE(1)] "The page did not request an origin-keyed agent cluster, but was put in one anyway because the origin 'https://www.instagram.com' had previously been placed in an origin-keyed agent cluster. Update your headers to uniformly request origin-keying for all pages on the origin.", source: https://www.instagram.com/make_money_on_the_internet//followers/ (1)
[13032:14924:0617/211238.814:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211239.929:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211239.929:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211239.929:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211239.929:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:5764:0617/211243.823:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:8708:0617/211328.919:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14924:0617/211333.927:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:8708:0617/211338.943:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:8708:0617/211343.955:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:5764:0617/211348.963:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14924:0617/211353.981:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:8708:0617/211358.982:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:5764:0617/211403.998:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14924:0617/211409.005:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:8708:0617/211414.025:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211418.251:WARNING:profile_picker_view.cc(119)] ProfilePickerView is created
[13032:14924:0617/211419.032:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14924:0617/211424.044:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:5764:0617/211429.056:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:15072:0617/211434.072:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:5764:0617/211439.074:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211439.872:WARNING:profile_picker_view.cc(781)] The ProfilePickerView is deleted
[13032:13168:0617/211440.884:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[13032:5764:0617/211444.075:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:7860:0617/211449.075:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14292:0617/211454.075:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211454.245:WARNING:controller_impl.cc(1190)] Background download complete, client: 6, completion type: 0, file size:265059
[13032:14924:0617/211459.094:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211501.179:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[13032:15072:0617/211504.110:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211504.965:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13032:13168:0617/211508.238:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13032:13168:0617/211508.239:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[13032:14292:0617/211509.114:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211511.077:INFO:CONSOLE(0)] "[DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) %o", source: https://www.instagram.com/ (0)
[13032:13168:0617/211511.277:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211511.277:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211511.277:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211511.277:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:4792:0617/211514.132:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:5764:0617/211519.149:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14924:0617/211524.170:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:4792:0617/211529.194:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14292:0617/211534.209:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:15072:0617/211539.217:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:4792:0617/211544.231:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:4792:0617/211549.242:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:11120:0617/211554.250:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14604:0617/211559.265:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:9988:0617/211604.266:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211605.776:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[13032:13620:0617/211609.266:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13032:13168:0617/211612.113:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[13032:13168:0617/211613.663:INFO:CONSOLE(0)] "[DOM] Input elements should have autocomplete attributes (suggested: "current-password"): (More info: https://goo.gl/9p2vKq) %o", source: https://www.instagram.com/ (0)
[13032:13168:0617/211613.808:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211613.808:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211613.808:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:13168:0617/211613.808:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13032:14292:0617/211614.278:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:14604:0617/211619.282:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:1860:0617/211624.294:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:4792:0617/211629.310:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:1860:0617/211634.317:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:9988:0617/211639.321:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:1860:0617/211644.331:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13032:13168:0617/211645.779:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13032:13168:0617/211645.780:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13032:13168:0617/211645.781:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/accounts/onetap/?next=%2F (0)
[13032:13168:0617/211648.577:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
