{"NewTabPage": {"PrevNavigationTime": "*****************"}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 107}, "autofill": {"orphan_rows_removed": true, "upload_encoding_seed": "********************************"}, "autogenerated": {"theme": {"color": -1452801}}, "browser": {"default_browser_infobar_last_declined": "*****************", "has_seen_welcome_page": true, "should_reset_check_default_browser": false, "window_placement": {"bottom": 760, "left": 484, "maximized": false, "right": 1536, "top": 52, "work_area_bottom": 824, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "cached_fonts": {"search_results_page": {"fallback": [], "primary": ["<PERSON><PERSON>"]}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_apps_install_state": 3, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "commands": {}, "external_uninstalls": ["lmjegmlicamnimmfhcmpkclmigmmcbeh"], "last_chrome_version": "107.0.5304.88", "theme": {"id": "autogenerated_theme_id"}}, "gaia_cookie": {"changed_time": **********.224707, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "c74f7d59-5b3d-407b-bb26-dedae1decfbb"}}, "in_product_help": {"snoozed_feature": {"IPH_ProfileSwitch": {"is_dismissed": true, "last_show_time": "*****************", "last_snooze_duration": "0", "last_snooze_time": "0", "show_count": 1, "snooze_count": 0}}}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "language_model_counters": {"en": 1, "fr": 2}, "media": {"device_id_salt": "5CBA455E8EE993D054AB780536AEAA9A", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "4WvaNwZfwoH8fDOLSDTjHd8/aoONhUUB5cECqAbKdI279Qc0lKoYSHWdIPG1cT/6tS3Ro3y2Pp6ZB8zvJmYbKw=="}, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}, "last_fetch_attempt": "*****************"}, "predictionmodelfetcher": {"last_fetch_attempt": "13394658521189010", "last_fetch_success": "13394658521552556"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"password_change_success_tracker": {"version": 1}}, "plugins": {"plugins_list": []}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"accessibility_events": {}, "app_banner": {}, "ar": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {"https://www.instagram.com:443,*": {"last_modified": "13394658549623169", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "cookies": {}, "durable_storage": {}, "fedcm_active_session": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {"https://www.instagram.com:443,*": {"last_modified": "13320693671507029", "setting": {"UserDataFieldFilled": true}}}, "geolocation": {}, "get_display_media_set_select_all_screens": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {"https://www.instagram.com:443,*": {"expiration": "13402434377521448", "last_modified": "13394658377521458", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "ppapi_broker": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://www.instagram.com:443,*": {"last_modified": "13394658584218102", "setting": {"lastEngagementTime": 1.3394658584218048e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 13.074312158152969}}}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "107.0.5304.88", "creation_time": "*****************", "exit_type": "Crashed", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.628359, "last_time_password_store_metrics_reported": **********.619084, "managed_user_id": "", "name": "instagram 2", "password_account_storage_settings": {}, "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "***********", "unhandled_sync_password_reuses": {}}, "segmentation_platform": {"segmentation_result": {"chrome_low_user_engagement": {"in_use": false, "segment_id": 16, "segment_rank": 1.0, "selection_time": "*****************"}, "cross_device_user": {"in_use": false, "segment_id": 0, "segment_rank": 0.0, "selection_time": "*****************"}, "shopping_user": {"in_use": false, "segment_id": 0, "segment_rank": 0.0, "selection_time": "*****************"}}}, "sessions": {"event_log": [{"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}, {"crashed": false, "time": "13320689772071952", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13320689796255956", "type": 2, "window_count": 1}, {"crashed": false, "time": "13320689911140026", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13320689985840245", "type": 2, "window_count": 1}, {"crashed": false, "time": "13320689998741573", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 2, "time": "13320693733451552", "type": 2, "window_count": 1}, {"crashed": false, "time": "13320693958428810", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13320694066899324", "type": 2, "window_count": 1}, {"crashed": false, "time": "13320771677794204", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13320772374116454", "type": 2, "window_count": 1}, {"crashed": false, "time": "13320772616835004", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13320772712927854", "type": 2, "window_count": 1}, {"crashed": false, "time": "***********597908", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 0, "time": "13394658377526552", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394658510743740", "type": 0}], "session_data_status": 1}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"requested": false}, "translate_ignored_count_for_language": {"fr": 1}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "unified_consent": {"migration_state": 10}, "updateclientdata": {"apps": {"ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"cohort": "1::", "cohortname": "", "dlrc": 5870, "installdate": 5870, "pf": "4f934a2e-be05-47d9-b9fe-7a8ebd8d90d8"}, "lmjegmlicamnimmfhcmpkclmigmmcbeh": {"cohort": "1::", "cohortname": "", "dlrc": 5870, "installdate": 5870, "pf": "9104bb6b-e039-422a-a87a-fa5fa5d2b8ec"}, "nmmhkkegccagdldgiimedpiccmgmieda": {"cohort": "1::", "cohortname": "", "dlrc": 6742, "installdate": 5870, "pf": "54aa0f51-975c-4c33-b46d-6b62660f192b"}}}, "web_app": {"app_id": {"install_url": {"aghbiahbpaijignceidepookljebhfak": ["https://drive.google.com/drive/installwebapp?usp=chrome_default"], "agimnkijcaahngcdmfeangaknmldooml": ["https://www.youtube.com/s/notifications/manifest/cr_install.html"], "fhihpiojkbmbpdjeoajapmgkhlnakfjf": ["https://docs.google.com/spreadsheets/installwebapp?usp=chrome_default"], "fmgjjmmmlfnkbppncabfkddbjimcfncm": ["https://mail.google.com/mail/installwebapp?usp=chrome_default"], "kefjledonklijopmnomlcbpllchaibag": ["https://docs.google.com/presentation/installwebapp?usp=chrome_default"], "mpnpojknpmmopombnjdcgaaiekajbnjb": ["https://docs.google.com/document/installwebapp?usp=chrome_default"]}}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "extension_ids": {"https://docs.google.com/document/installwebapp?usp=chrome_default": {"extension_id": "mpnpojknpmmopombnjdcgaaiekajbnjb", "install_source": 1, "is_placeholder": false}, "https://docs.google.com/presentation/installwebapp?usp=chrome_default": {"extension_id": "kefjledonklijopmnomlcbpllchaibag", "install_source": 1, "is_placeholder": false}, "https://docs.google.com/spreadsheets/installwebapp?usp=chrome_default": {"extension_id": "fhihpiojkbmbpdjeoajapmgkhlnakfjf", "install_source": 1, "is_placeholder": false}, "https://drive.google.com/drive/installwebapp?usp=chrome_default": {"extension_id": "aghbiahbpaijignceidepookljebhfak", "install_source": 1, "is_placeholder": false}, "https://mail.google.com/mail/installwebapp?usp=chrome_default": {"extension_id": "fmgjjmmmlfnkbppncabfkddbjimcfncm", "install_source": 1, "is_placeholder": false}, "https://www.youtube.com/s/notifications/manifest/cr_install.html": {"extension_id": "agimnkijcaahngcdmfeangaknmldooml", "install_source": 1, "is_placeholder": false}}, "isolation_state": {}, "last_preinstall_synchronize_version": "107", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"borussia dortmund\",\"emilien\",\"audi q3 2025\",\"amir sayoud\",\"bac philo\",\"franco mastantuono real madrid\",\"réseau sfr panne\",\"playstation\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:headertexts\":{\"a\":{\"8\":\"Recherches populaires\"}},\"google:suggestdetail\":[{\"a\":\"Club de football\",\"dc\":\"#8b7600\",\"i\":\"data:image/png;base64,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\\u003d\",\"q\":\"gs_ssp\\u003deJzj4tTP1TcwLI9PMTNg9BJMyi8qLS7OTFRIyS8qyS3NSwEAjvUKMQ\",\"t\":\"Borussia Dortmund\",\"zae\":\"/m/01w_d6\",\"zl\":8},{\"zl\":8},{\"zl\":8},{\"a\":\"Footballeur international algérien\",\"dc\":\"#5f7f33\",\"i\":\"data:image/jpeg;base64,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\\u003d\\u003d\",\"q\":\"gs_ssp\\u003deJzj4tLP1TcwLza2TCsyYPTiTszNLFIoTqzML00BAF1NB-I\",\"t\":\"Amir Sayoud\",\"zae\":\"/m/07s39fr\",\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8}],\"google:suggesteventid\":\"-5177547544805906119\",\"google:suggestrelevance\":[1250,601,600,554,553,552,551,550],\"google:suggestsubtypes\":[[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362]],\"google:suggesttype\":[\"ENTITY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"]}]"}}