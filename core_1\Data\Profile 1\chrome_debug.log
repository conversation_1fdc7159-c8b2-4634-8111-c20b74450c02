[10028:10316:0618/060051.493:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[10028:10316:0618/060054.014:ERROR:device_event_log_impl.cc(215)] [06:00:54.016] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[10028:10316:0618/060054.069:ERROR:device_event_log_impl.cc(215)] [06:00:54.076] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[10028:10316:0618/060054.069:ERROR:device_event_log_impl.cc(215)] [06:00:54.080] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[10028:10316:0618/060054.100:ERROR:device_event_log_impl.cc(215)] [06:00:54.105] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[10028:13980:0618/060055.101:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10028:10316:0618/060055.148:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[10028:10316:0618/060056.910:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.015:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.015:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.032:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.033:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.034:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060057.034:INFO:CONSOLE(1948)] "Rendering was performed in a subtree hidden by content-visibility:hidden.", source: https://static.cdninstagram.com/rsrc.php/v4i1YK4/ya/l/en_US-j/-Z2WkKqtY11Pn4rdEMaE7c8lokS581WVPdPClxq6u8zhwgRmwULA8A1R6_2hX6DpuSxNEkM44K5ZUATgQYQnkRRoV77OGKJKLK5Yx48c8l6FzQUTpj5fGIe3ppR_Jnlk2RN-OO2-z8torVedCGLqWp9LORRt1ROm-7Byo72ZXSLQqPTDATQCms6cOXYMA_9mSUf9NRsNVBIie8AacGFIPw72LLbJAR5EMbaZl94NiDGg9sTuHNUror_6KkNpR-kExaOsU-pF.js (1948)
[10028:10316:0618/060058.249:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060058.249:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060058.249:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060058.249:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060058.622:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060058.624:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060058.624:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060058.624:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:9920:0618/060100.122:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10028:10316:0618/060102.028:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10028:10316:0618/060102.029:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[10028:10316:0618/060102.940:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10028:10316:0618/060102.940:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10028:10316:0618/060102.940:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10028:10316:0618/060102.940:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10028:10316:0618/060102.940:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10028:10316:0618/060102.940:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10028:10316:0618/060102.941:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10028:10316:0618/060102.941:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10028:10316:0618/060102.941:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10028:10316:0618/060102.941:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10028:10316:0618/060102.941:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet (0)
[10028:13392:0618/060105.125:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:10316:0618/060106.031:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060106.031:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060106.031:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060106.031:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10028:10316:0618/060107.108:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060107.108:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060107.109:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060107.109:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10028:10316:0618/060107.493:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"A2SiO3fOg8QZUEi22n5Vc1Y","payload":null,"lid":"7517165437804934865"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4igW-4/yL/l/en_US-j/mGhTvRcr20xPHBALOZJ6U0.js (56)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10028:10316:0618/060109.923:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10028:10316:0618/060109.924:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[10028:10908:0618/060110.132:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060115.145:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060120.150:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060125.158:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060130.176:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060135.185:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060140.190:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060145.189:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:13184:0618/060150.201:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060155.207:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060200.216:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060205.222:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060210.237:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060215.255:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:13184:0618/060220.266:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060225.272:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060230.278:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:13184:0618/060235.291:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:13184:0618/060240.299:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:11228:0618/060245.310:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:3868:0618/060250.323:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10028:10316:0618/060252.859:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
