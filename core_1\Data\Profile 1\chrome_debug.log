[13900:2072:0618/051713.792:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[13900:7732:0618/051715.245:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:2072:0618/051717.112:ERROR:device_event_log_impl.cc(215)] [05:17:17.112] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[13900:2072:0618/051717.247:ERROR:device_event_log_impl.cc(215)] [05:17:17.247] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[13900:2072:0618/051717.356:ERROR:device_event_log_impl.cc(215)] [05:17:17.356] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[13900:2072:0618/051717.371:ERROR:device_event_log_impl.cc(215)] [05:17:17.371] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13900:2072:0618/051718.037:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13900:2072:0618/051718.038:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13900:2072:0618/051718.038:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13900:2072:0618/051718.038:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[13900:12916:0618/051720.446:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:2072:0618/051720.802:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051720.802:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051720.802:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051720.802:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051722.696:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051722.701:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051722.701:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051722.702:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051723.877:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"AW-sRoTkj0GFErUgUfXg5Yr","payload":null,"lid":"7517154166857254248"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4iGlR4/yK/l/en_US-j/mGhTvRcr20x.js (56)
[13900:2072:0618/051725.016:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13900:2072:0618/051725.017:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13900:2072:0618/051725.018:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet/followers/ (0)
[13900:12916:0618/051725.446:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:2072:0618/051727.516:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051727.516:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051727.516:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051727.516:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13900:2072:0618/051728.950:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051728.950:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051728.951:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051728.951:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13900:2072:0618/051729.558:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"Ae_nTdRsL6jNL2i2z2BVam5","payload":null,"lid":"7517154192142278905"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4igW-4/yL/l/en_US-j/mGhTvRcr20xPHBALOZJ6U0.js (56)
[13900:15272:0618/051730.454:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13900:2072:0618/051731.975:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[13900:12916:0618/051735.469:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:7732:0618/051740.489:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:5928:0618/051745.493:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:7732:0618/051750.507:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:12916:0618/051755.511:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:12916:0618/051800.522:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:5928:0618/051805.536:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:12916:0618/051810.549:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:7732:0618/051815.558:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13900:2072:0618/051817.628:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
