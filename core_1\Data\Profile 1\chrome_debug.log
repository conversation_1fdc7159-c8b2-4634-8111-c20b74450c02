[9736:12928:0618/053847.680:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[9736:13852:0618/053850.742:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[9736:12928:0618/053851.108:ERROR:device_event_log_impl.cc(215)] [05:38:51.109] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[9736:12928:0618/053851.179:ERROR:device_event_log_impl.cc(215)] [05:38:51.187] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[9736:12928:0618/053851.179:ERROR:device_event_log_impl.cc(215)] [05:38:51.189] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[9736:12928:0618/053851.179:ERROR:device_event_log_impl.cc(215)] [05:38:51.189] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[9736:12928:0618/053851.179:ERROR:device_event_log_impl.cc(215)] [05:38:51.190] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[9736:12928:0618/053851.861:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[9736:12928:0618/053851.861:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[9736:12928:0618/053851.861:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[9736:12928:0618/053851.862:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[9736:12928:0618/053851.862:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[9736:12928:0618/053851.862:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[9736:12928:0618/053851.862:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[9736:12928:0618/053851.863:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[9736:12928:0618/053851.863:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[9736:12928:0618/053851.864:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[9736:12928:0618/053851.865:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[9736:12928:0618/053854.555:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053854.556:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053854.556:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053854.556:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053855.465:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053855.467:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053855.467:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053855.468:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:9728:0618/053855.752:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[9736:12928:0618/053855.977:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"AJ9K8w8CYDHuqM66kWKlic8","payload":null,"lid":"7517159720671282557"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4iGlR4/yK/l/en_US-j/mGhTvRcr20x.js (56)
[9736:12928:0618/053858.529:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[9736:12928:0618/053858.529:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[9736:12928:0618/053858.530:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[9736:12928:0618/053859.325:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[9736:12928:0618/053859.325:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[9736:12928:0618/053859.325:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[9736:12928:0618/053859.325:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[9736:12928:0618/053859.326:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[9736:12928:0618/053859.326:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[9736:12928:0618/053859.326:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[9736:12928:0618/053859.326:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[9736:12928:0618/053859.326:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[9736:12928:0618/053859.326:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[9736:12928:0618/053859.328:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet (0)
[9736:4820:0618/053900.760:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[9736:12928:0618/053901.260:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053901.262:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053901.263:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053901.263:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[9736:12928:0618/053901.827:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053901.827:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053901.828:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053901.828:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[9736:12928:0618/053902.309:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"AL4QDcjDnuJ6DfaqiQHS3P5","payload":null,"lid":"7517159746228969846"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4igW-4/yL/l/en_US-j/mGhTvRcr20xPHBALOZJ6U0.js (56)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[9736:12928:0618/053904.741:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[9736:4820:0618/053905.761:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[9736:12928:0618/053909.161:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
