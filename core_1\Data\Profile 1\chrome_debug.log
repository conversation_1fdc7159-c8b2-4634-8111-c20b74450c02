[13672:5452:0618/074241.660:WARNING:backend_impl.cc(1816)] Destroying invalid entry.
[13672:13420:0618/074242.460:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[13672:8568:0618/074243.906:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:13420:0618/074245.821:ERROR:device_event_log_impl.cc(215)] [07:42:45.822] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[13672:13420:0618/074245.972:ERROR:device_event_log_impl.cc(215)] [07:42:45.973] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[13672:13420:0618/074245.978:ERROR:device_event_log_impl.cc(215)] [07:42:45.978] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[13672:13420:0618/074245.981:ERROR:device_event_log_impl.cc(215)] [07:42:45.981] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[13672:13420:0618/074246.527:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13672:13420:0618/074246.527:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13672:13420:0618/074246.527:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13672:13420:0618/074246.527:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13672:13420:0618/074246.527:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13672:13420:0618/074246.528:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13672:13420:0618/074246.528:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13672:13420:0618/074246.528:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13672:13420:0618/074246.528:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13672:13420:0618/074246.528:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13672:13420:0618/074246.530:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[13672:10944:0618/074248.911:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:13420:0618/074250.864:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074250.864:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074250.865:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074250.865:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074251.911:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:13420:0618/074251.916:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iDCL4/yS/l/en_US-j/z80PR0fJi5SYToNMJR0yaIujfqykAm8FENcUA_yH0EsMa167-8sMsBEdHUv-61ab9b7z1eGnB5DLhmGhTvRcr20xs7rhmczSQJVJg7dUf4vTiot1tl2FasLqqxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKORt1ROm-7Byo.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:13420:0618/074251.916:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y5/l/en_US-j/-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiOqycWx_YsZCLb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5KMQLNwx3hXoH8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:13420:0618/074251.916:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/y2/l/en_US-j/tI6aM7dVi2WroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:5064:0618/074253.912:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13672:13420:0618/074254.214:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13672:13420:0618/074254.215:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet (0)
[13672:13420:0618/074256.099:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074256.099:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074256.099:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074256.099:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[13672:13420:0618/074258.345:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:13420:0618/074258.347:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iDCL4/yS/l/en_US-j/z80PR0fJi5SYToNMJR0yaIujfqykAm8FENcUA_yH0EsMa167-8sMsBEdHUv-61ab9b7z1eGnB5DLhmGhTvRcr20xs7rhmczSQJVJg7dUf4vTiot1tl2FasLqqxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKORt1ROm-7Byo.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:13420:0618/074258.347:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y5/l/en_US-j/-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiOqycWx_YsZCLb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5KMQLNwx3hXoH8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:13420:0618/074258.347:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/y2/l/en_US-j/tI6aM7dVi2WroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[13672:7444:0618/074258.932:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:13420:0618/074259.266:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"Amwvq0jcQsH8gaL7DsGtRR9","payload":null,"lid":"7517191688392187295"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4ii0T4/yk/l/en_US-j/mGhTvRcr20xPHBALOZJ6U0zcJSD6dwwKNXAXEI-aBzlk.js (56)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[13672:13420:0618/074301.451:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[13672:10944:0618/074303.932:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074308.941:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074313.951:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074318.956:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074323.961:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074328.980:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074333.996:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074339.004:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074344.007:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074349.021:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074354.035:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074359.051:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074404.060:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074409.064:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074414.080:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074419.090:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074424.108:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074429.125:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074434.128:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074439.134:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074444.135:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074449.138:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074454.143:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074459.159:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074504.172:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074509.187:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074514.195:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074519.208:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074524.220:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074529.221:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074534.235:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074539.246:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074544.262:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074549.267:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074554.276:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074559.294:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074604.302:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074609.314:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:10944:0618/074614.320:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074619.330:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074624.341:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074629.348:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:7444:0618/074634.352:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:8568:0618/074639.360:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[13672:13420:0618/074643.483:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
