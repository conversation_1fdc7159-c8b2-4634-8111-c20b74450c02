[10852:7156:0618/061405.022:WARNING:CONSOLE(1)] "Unrecognized feature: '
'.", source: chrome://resources/polymer/v3_0/polymer/polymer_bundled.min.js (1)
[10852:5012:0618/061407.206:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:7156:0618/061408.247:ERROR:device_event_log_impl.cc(215)] [06:14:08.246] Bluetooth: bluetooth_adapter_winrt.cc:1205 Getting Radio failed. Chrome will be unable to change the power state by itself.
[10852:7156:0618/061408.319:ERROR:device_event_log_impl.cc(215)] [06:14:08.319] USB: usb_device_handle_win.cc:1048 Failed to read descriptor from node connection: A device attached to the system is not functioning. (0x1F)
[10852:7156:0618/061408.326:ERROR:device_event_log_impl.cc(215)] [06:14:08.325] Bluetooth: bluetooth_adapter_winrt.cc:1283 OnPoweredRadioAdded(), Number of Powered Radios: 1
[10852:7156:0618/061408.327:ERROR:device_event_log_impl.cc(215)] [06:14:08.327] Bluetooth: bluetooth_adapter_winrt.cc:1298 OnPoweredRadiosEnumerated(), Number of Powered Radios: 1
[10852:7156:0618/061409.151:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10852:7156:0618/061409.151:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10852:7156:0618/061409.151:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10852:7156:0618/061409.151:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10852:7156:0618/061409.151:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10852:7156:0618/061409.152:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10852:7156:0618/061409.152:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10852:7156:0618/061409.152:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10852:7156:0618/061409.152:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10852:7156:0618/061409.152:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10852:7156:0618/061409.152:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/ (0)
[10852:7156:0618/061411.837:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061411.838:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061411.838:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061411.838:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:14908:0618/061412.208:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:7156:0618/061412.505:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061412.508:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061412.508:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061412.508:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061413.224:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"A6oA5cvllwV_0oKwa18hsbB","payload":null,"lid":"7517168812707526505"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4iGlR4/yK/l/en_US-j/mGhTvRcr20x.js (56)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10852:7156:0618/061415.601:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10852:7156:0618/061415.602:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10852:7156:0618/061415.602:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10852:7156:0618/061415.602:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10852:7156:0618/061415.602:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[10852:7156:0618/061416.531:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10852:7156:0618/061416.531:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10852:7156:0618/061416.531:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10852:7156:0618/061416.531:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10852:7156:0618/061416.531:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10852:7156:0618/061416.531:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10852:7156:0618/061416.532:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10852:7156:0618/061416.532:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10852:7156:0618/061416.533:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10852:7156:0618/061416.533:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10852:7156:0618/061416.534:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.instagram.com/make_money_on_the_internet (0)
[10852:1112:0618/061417.209:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:7156:0618/061418.571:INFO:CONSOLE(249)] "
%cStop! font-family:helvetica; font-size:20px; font-size:50px; font-weight:bold; color:red; -webkit-text-stroke:1px black;", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061418.571:INFO:CONSOLE(249)] "
%cThis is a browser feature intended for developers. If someone told you to copy-paste something here to enable an Instagram feature or "hack" someone's account, it is a scam and will give them access to your Instagram account. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061418.572:INFO:CONSOLE(249)] "
%cSee https://www.facebook.com/selfxss for more information. font-family:helvetica; font-size:20px; ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061418.572:INFO:CONSOLE(249)] "
%c ", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (249)
[10852:7156:0618/061419.126:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061419.127:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iYPg4/yK/l/en_US-j/z80PR0fJi5SEsWsFfmabM4ujfqykAm8FEA05IsPR9C6Wa167-8sMsBE8lokS581WVPdPClxq6u8zhmGhTvRcr20xs7rhmczSQJVKChQ5nVrtFgxNEkM44K5ZUV77OGKJKLK5BZQ3N5EXN4eWEyt5QK9BmZ5mJqDHf_Xe9pR_Jnlk2RN-D9UAt8osFeekzYuE0-uNKO.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061419.127:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4irpR4/y2/l/en_US-j/Rt1ROm-7Byo-5fHen0szzfMA_9mSUf9NRqDEtoJclSgKc1ZNDmKXGu0hxMchKuOD36yeH-Am-vdm2JnAxeHFjQ1GIrxZA-U_LZqSynBmykitPWPSEmqR_2N6tar4GoveLLUO6UyhQ-pHud4V3eBKOOgzwRfMf7dulja4Ey9e4kj-9CnGCA4h6Ocwu6-eLjl6f9jjma30wNRQUyFiO9ulKZtJhWZTb60wJ3d0E80uSGL4bdIcTaHJ1YFWet12W42b0RqZJrwtQzpHz5l0_XlWTi4a_ERIvMKRprcxKoKq5H8-RCA1rTTaK95q9O99MfVJOC_7GcTSmr.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061419.127:INFO:CONSOLE(67)] "Refused to prefetch content from 'https://static.cdninstagram.com/rsrc.php/v4iD6c4/yt/l/en_US-j/tI6aM7dVi2WAQQzmqmwwNIroLOVitXqIYaCkAxl4CCdo35604unMSu-3zjOyJ5n9f5TLG6NWWDcy8mpkb9Pwkar-DxccMwfu2pPz7Eo_QTG8hF.js' because it violates the following Content Security Policy directive: "default-src *.facebook.com *.fbcdn.net *.instagram.com blob: 'wasm-unsafe-eval'". Note that 'prefetch-src' was not explicitly set, so 'default-src' is used as a fallback.
", source: https://static.cdninstagram.com/rsrc.php/v4/ys/r/66u5BEDF2Va.js (67)
[10852:7156:0618/061419.689:INFO:CONSOLE(56)] "ErrorUtils caught an error:

DTSG response is not valid: {"__ar":1,"error":1357004,"errorSummary":"Sorry, something went wrong","errorDescription":"Please try closing and re-opening your browser window.","isNotCritical":1,"rid":"A0G3ebLBnU-WmxzYtN4PrVZ","payload":null,"lid":"7517168838126846548"}'

Subsequent non-fatal errors won't be logged; see https://fburl.com/debugjs. [object Object]", source: https://static.cdninstagram.com/rsrc.php/v4igW-4/yL/l/en_US-j/mGhTvRcr20xPHBALOZJ6U0.js (56)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'browsing-topics'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'unload'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[10852:7156:0618/061422.185:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://www.facebook.com/instagram/login_sync/ (0)
[10852:4068:0618/061422.210:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061427.225:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061432.234:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061437.235:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061442.248:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061447.255:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061452.255:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061457.267:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061502.283:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061507.293:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061512.300:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061517.321:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061522.334:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061527.343:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061532.358:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061537.372:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061542.382:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:4068:0618/061547.388:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061552.396:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061557.418:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061602.424:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:9624:0618/061607.436:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:1112:0618/061612.445:WARNING:page_discarding_helper.cc(126)] Urgently discarding multiple pages with target (kb): 0
[10852:7156:0618/061615.196:WARNING:pref_notifier_impl.cc(41)] Pref observer for media_router.cast_allow_all_ips found at shutdown.
