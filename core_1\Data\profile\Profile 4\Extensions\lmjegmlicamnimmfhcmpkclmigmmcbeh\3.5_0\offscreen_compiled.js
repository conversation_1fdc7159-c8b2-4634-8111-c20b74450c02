/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var m;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ca(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var da=ca(this);function p(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}
p("Symbol",function(a){function b(f){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c(d+(f||"")+"_"+e++,f)}function c(f,g){this.h=f;ba(this,"description",{configurable:!0,writable:!0,value:g})}if(a)return a;c.prototype.toString=function(){return this.h};var d="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",e=0;return b});
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ea(aa(this))}})}return a});function ea(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
function fa(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}}var ha="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},ia;if("function"==typeof Object.setPrototypeOf)ia=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ia=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var na=ia;function q(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.N=b.prototype}function oa(){this.v=!1;this.j=null;this.o=void 0;this.h=1;this.s=this.i=0;this.l=null}function pa(a){if(a.v)throw new TypeError("Generator is already running");a.v=!0}oa.prototype.m=function(a){this.o=a};
function qa(a,b){a.l={ha:b,wa:!0};a.h=a.i||a.s}oa.prototype.return=function(a){this.l={return:a};this.h=this.s};function r(a,b,c){a.h=c;return{value:b}}function ra(a,b){a.h=b;a.i=0}function sa(a){a.i=0;var b=a.l.ha;a.l=null;return b}function ta(a){this.h=new oa;this.i=a}function ua(a,b){pa(a.h);var c=a.h.j;if(c)return va(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.h.return);a.h.return(b);return xa(a)}
function va(a,b,c,d){try{var e=b.call(a.h.j,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.h.v=!1,e;var f=e.value}catch(g){return a.h.j=null,qa(a.h,g),xa(a)}a.h.j=null;d.call(a.h,f);return xa(a)}function xa(a){for(;a.h.h;)try{var b=a.i(a.h);if(b)return a.h.v=!1,{value:b.value,done:!1}}catch(c){a.h.o=void 0,qa(a.h,c)}a.h.v=!1;if(a.h.l){b=a.h.l;a.h.l=null;if(b.wa)throw b.ha;return{value:b.return,done:!0}}return{value:void 0,done:!0}}
function ya(a){this.next=function(b){pa(a.h);a.h.j?b=va(a,a.h.j.next,b,a.h.m):(a.h.m(b),b=xa(a));return b};this.throw=function(b){pa(a.h);a.h.j?b=va(a,a.h.j["throw"],b,a.h.m):(qa(a.h,b),b=xa(a));return b};this.return=function(b){return ua(a,b)};this[Symbol.iterator]=function(){return this}}function za(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})}
function Aa(a){return za(new ya(new ta(a)))}
p("Promise",function(a){function b(g){this.h=0;this.j=void 0;this.i=[];this.m=!1;var h=this.l();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.h=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.i=function(g){if(null==this.h){this.h=[];var h=this;this.j(function(){h.o()})}this.h.push(g)};var e=da.setTimeout;c.prototype.j=function(g){e(g,0)};c.prototype.o=function(){for(;this.h&&this.h.length;){var g=this.h;this.h=[];for(var h=0;h<g.length;++h){var k=
g[h];g[h]=null;try{k()}catch(l){this.l(l)}}}this.h=null};c.prototype.l=function(g){this.j(function(){throw g;})};b.prototype.l=function(){function g(l){return function(n){k||(k=!0,l.call(h,n))}}var h=this,k=!1;return{resolve:g(this.G),reject:g(this.o)}};b.prototype.G=function(g){if(g===this)this.o(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.K(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?this.F(g):this.v(g)}};
b.prototype.F=function(g){var h=void 0;try{h=g.then}catch(k){this.o(k);return}"function"==typeof h?this.M(h,g):this.v(g)};b.prototype.o=function(g){this.s(2,g)};b.prototype.v=function(g){this.s(1,g)};b.prototype.s=function(g,h){if(0!=this.h)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.h);this.h=g;this.j=h;2===this.h&&this.H();this.D()};b.prototype.H=function(){var g=this;e(function(){if(g.C()){var h=da.console;"undefined"!==typeof h&&h.error(g.j)}},1)};b.prototype.C=
function(){if(this.m)return!1;var g=da.CustomEvent,h=da.Event,k=da.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=da.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.j;return k(g)};b.prototype.D=function(){if(null!=this.i){for(var g=0;g<this.i.length;++g)f.i(this.i[g]);this.i=null}};var f=new c;
b.prototype.K=function(g){var h=this.l();g.X(h.resolve,h.reject)};b.prototype.M=function(g,h){var k=this.l();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(g,h){function k(A,x){return"function"==typeof A?function(C){try{l(A(C))}catch(ja){n(ja)}}:x}var l,n,y=new b(function(A,x){l=A;n=x});this.X(k(g,l),k(h,n));return y};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.X=function(g,h){function k(){switch(l.h){case 1:g(l.j);break;case 2:h(l.j);break;
default:throw Error("Unexpected state: "+l.h);}}var l=this;null==this.i?f.i(k):this.i.push(k);this.m=!0};b.resolve=d;b.reject=function(g){return new b(function(h,k){k(g)})};b.race=function(g){return new b(function(h,k){for(var l=fa(g),n=l.next();!n.done;n=l.next())d(n.value).X(h,k)})};b.all=function(g){var h=fa(g),k=h.next();return k.done?d([]):new b(function(l,n){function y(C){return function(ja){A[C]=ja;x--;0==x&&l(A)}}var A=[],x=0;do A.push(void 0),x++,d(k.value).X(y(A.length-1),n),k=h.next();
while(!k.done)})};return b});function t(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
p("WeakMap",function(a){function b(k){this.h=(h+=Math.random()+1).toString();if(k){k=fa(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}}function c(){}function d(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function e(k){if(!t(k,g)){var l=new c;ba(k,g,{value:l})}}function f(k){var l=Object[k];l&&(Object[k]=function(n){if(n instanceof c)return n;Object.isExtensible(n)&&e(n);return l(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),
n=new a([[k,2],[l,3]]);if(2!=n.get(k)||3!=n.get(l))return!1;n.delete(k);n.set(l,4);return!n.has(k)&&4==n.get(l)}catch(y){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(k,l){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!t(k,g))throw Error("WeakMap key fail: "+k);k[g][this.h]=l;return this};b.prototype.get=function(k){return d(k)&&t(k,g)?k[g][this.h]:void 0};b.prototype.has=function(k){return d(k)&&t(k,
g)&&t(k[g],this.h)};b.prototype.delete=function(k){return d(k)&&t(k,g)&&t(k[g],this.h)?delete k[g][this.h]:!1};return b});
p("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,k){var l=h.h;return ea(function(){if(l){for(;l.head!=h.h;)l=l.previous;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})}function d(h,k){var l=k&&typeof k;"object"==l||"function"==l?f.has(k)?l=f.get(k):(l=""+ ++g,f.set(k,l)):l="p_"+k;var n=h.i[l];if(n&&t(h.i,l))for(h=0;h<n.length;h++){var y=n[h];if(k!==k&&y.key!==y.key||k===y.key)return{id:l,list:n,index:h,entry:y}}return{id:l,
list:n,index:-1,entry:void 0}}function e(h){this.i={};this.h=b();this.size=0;if(h){h=fa(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(fa([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),n=l.next();if(n.done||n.value[0]!=h||"s"!=n.value[1])return!1;n=l.next();return n.done||4!=
n.value[0].x||"t"!=n.value[1]||!l.next().done?!1:!0}catch(y){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this.i[l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this.h,previous:this.h.previous,head:this.h,key:h,value:k},l.list.push(l.entry),this.h.previous.next=l.entry,this.h.previous=l.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.i[h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this.i={};this.h=this.h.previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,
function(h){return h.value})};e.prototype.forEach=function(h,k){for(var l=this.entries(),n;!(n=l.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});p("Number.isNaN",function(a){return a?a:function(b){return"number"===typeof b&&isNaN(b)}});
function Ba(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e}p("Array.prototype.entries",function(a){return a?a:function(){return Ba(this,function(b,c){return[b,c]})}});
p("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});p("Array.prototype.keys",function(a){return a?a:function(){return Ba(this,function(b){return b})}});
p("Array.prototype.values",function(a){return a?a:function(){return Ba(this,function(b,c){return c})}});p("Array.prototype.fill",function(a){return a?a:function(b,c,d){var e=this.length||0;0>c&&(c=Math.max(0,e+c));if(null==d||d>e)d=e;d=Number(d);0>d&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});function u(a){return a?a:Array.prototype.fill}p("Int8Array.prototype.fill",u);p("Uint8Array.prototype.fill",u);p("Uint8ClampedArray.prototype.fill",u);
p("Int16Array.prototype.fill",u);p("Uint16Array.prototype.fill",u);p("Int32Array.prototype.fill",u);p("Uint32Array.prototype.fill",u);p("Float32Array.prototype.fill",u);p("Float64Array.prototype.fill",u);p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)t(b,d)&&c.push([d,b[d]]);return c}});p("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});var v=this||self;
function Ca(a){var b=typeof a;b="object"!=b?b:a?Array.isArray(a)?"array":b:"null";return"array"==b||"object"==b&&"number"==typeof a.length}function Da(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}function Ea(a,b,c){return a.call.apply(a.bind,arguments)}
function Fa(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function w(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?w=Ea:w=Fa;return w.apply(null,arguments)}
function Ga(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function z(a,b){function c(){}c.prototype=b.prototype;a.N=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ba=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}}function Ha(a){return a};function Ia(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,Ia);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}z(Ia,Error);Ia.prototype.name="CustomError";var Ja;function Ka(a){v.setTimeout(function(){throw a;},0)};function La(){var a=v.navigator;return a&&(a=a.userAgent)?a:""}function B(a){return-1!=La().indexOf(a)};function Ma(){return B("Firefox")||B("FxiOS")}function Na(){return(B("Chrome")||B("CriOS"))&&!B("Edge")||B("Silk")};function Oa(){return B("iPhone")&&!B("iPod")&&!B("iPad")};var Pa=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},Qa=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)},Ra=Array.prototype.map?function(a,b){return Array.prototype.map.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e="string"===typeof a?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d};function Sa(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};function Ta(a){Ta[" "](a);return a}Ta[" "]=function(){};function Ua(a,b,c){return Object.prototype.hasOwnProperty.call(a,b)?a[b]:a[b]=c(b)};var Va=B("Opera"),Wa=B("Trident")||B("MSIE"),Xa=B("Edge"),Ya=B("Gecko")&&!(-1!=La().toLowerCase().indexOf("webkit")&&!B("Edge"))&&!(B("Trident")||B("MSIE"))&&!B("Edge"),Za=-1!=La().toLowerCase().indexOf("webkit")&&!B("Edge"),$a;
a:{var ab="",bb=function(){var a=La();if(Ya)return/rv:([^\);]+)(\)|;)/.exec(a);if(Xa)return/Edge\/([\d\.]+)/.exec(a);if(Wa)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(Za)return/WebKit\/(\S+)/.exec(a);if(Va)return/(?:Version)[ \/]?(\S+)/.exec(a)}();bb&&(ab=bb?bb[1]:"");if(Wa){var cb,db=v.document;cb=db?db.documentMode:void 0;if(null!=cb&&cb>parseFloat(ab)){$a=String(cb);break a}}$a=ab}var eb=$a;var fb=Ma(),gb=Oa()||B("iPod"),hb=B("iPad"),ib=B("Android")&&!(Na()||Ma()||B("Opera")||B("Silk")),jb=Na(),kb=B("Safari")&&!(Na()||B("Coast")||B("Opera")||B("Edge")||B("Edg/")||B("OPR")||Ma()||B("Silk")||B("Android"))&&!(Oa()||B("iPad")||B("iPod"));var lb={},mb=null;
function nb(a){var b;void 0===b&&(b=0);if(!mb){mb={};for(var c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),d=["+/=","+/","-_=","-_.","-_"],e=0;5>e;e++){var f=c.concat(d[e].split(""));lb[e]=f;for(var g=0;g<f.length;g++){var h=f[g];void 0===mb[h]&&(mb[h]=g)}}}b=lb[b];c=Array(Math.floor(a.length/3));d=b[64]||"";for(e=f=0;f<a.length-2;f+=3){var k=a[f],l=a[f+1];h=a[f+2];g=b[k>>2];k=b[(k&3)<<4|l>>4];l=b[(l&15)<<2|h>>6];h=b[h&63];c[e++]=g+k+l+h}g=0;h=d;switch(a.length-f){case 2:g=
a[f+1],h=b[(g&15)<<2]||d;case 1:a=a[f],c[e]=b[a>>2]+b[(a&3)<<4|g>>4]+h+d}return c.join("")};var ob="undefined"!==typeof Uint8Array,pb={};var qb;function rb(a){if(pb!==pb)throw Error("illegal external caller");this.da=a;if(null!=a&&0===a.length)throw Error("ByteString should be constructed with non-empty values");};var D="function"===typeof Symbol&&"symbol"===typeof Symbol()?Symbol():void 0;function E(a,b){if(D)return a[D]|=b;if(void 0!==a.J)return a.J|=b;Object.defineProperties(a,{J:{value:b,configurable:!0,writable:!0,enumerable:!1}});return b}function sb(a,b){var c=F(a);(c&b)!==b&&(Object.isFrozen(a)&&(a=Array.prototype.slice.call(a)),G(a,c|b));return a}function F(a){var b;D?b=a[D]:b=a.J;return null==b?0:b}
function G(a,b){D?a[D]=b:void 0!==a.J?a.J=b:Object.defineProperties(a,{J:{value:b,configurable:!0,writable:!0,enumerable:!1}})}function tb(a){E(a,1);return a}function H(a){return!!(F(a)&2)}function ub(a,b){G(b,(a|0)&-51)}function vb(a,b){G(b,(a|18)&-41)};var wb={};function xb(a){return null!==a&&"object"===typeof a&&!Array.isArray(a)&&a.constructor===Object}var yb,zb=[];G(zb,23);yb=Object.freeze(zb);function Ab(a){if(H(a.A))throw Error("Cannot mutate an immutable Message");}function Bb(a){var b=a.length;(b=b?a[b-1]:void 0)&&xb(b)?b.g=1:(b={},a.push((b.g=1,b)))};var Cb;function Db(a){switch(typeof a){case "number":return isFinite(a)?a:String(a);case "object":if(a)if(Array.isArray(a)){if(0!==(F(a)&128))return a=Array.prototype.slice.call(a),Bb(a),a}else{if(ob&&null!=a&&a instanceof Uint8Array)return nb(a);if(a instanceof rb){var b=a.da;return null==b?"":"string"===typeof b?b:a.da=nb(b)}}}return a};function Eb(a,b,c,d){if(null!=a){if(Array.isArray(a))a=Fb(a,b,c,void 0!==d);else if(xb(a)){var e={},f;for(f in a)e[f]=Eb(a[f],b,c,d);a=e}else a=b(a,d);return a}}function Fb(a,b,c,d){var e=F(a);d=d?!!(e&16):void 0;a=Array.prototype.slice.call(a);for(var f=0;f<a.length;f++)a[f]=Eb(a[f],b,c,d);c(e,a);return a}function Gb(a){a.Y===wb?a=Fb(a.A,Gb,Hb):a instanceof rb?(a=a.da||"",a="string"===typeof a?a:new Uint8Array(a)):a=ob&&null!=a&&a instanceof Uint8Array?new Uint8Array(a):a;return a}
function Ib(a){return a.Y===wb?a.toJSON():Db(a)}function Hb(a,b){a&128&&Bb(b)};function Jb(a){var b=a.i+a.O;return a.I||(a.I=a.A[b]={})}function I(a,b,c){return-1===b?null:b>=a.i?a.I?a.I[b]:void 0:c&&a.I&&(c=a.I[b],null!=c)?c:a.A[b+a.O]}function J(a,b,c,d){Ab(a);return K(a,b,c,d)}function K(a,b,c,d){a.j&&(a.j=void 0);if(b>=a.i||d)return Jb(a)[b]=c,a;a.A[b+a.O]=c;(c=a.I)&&b in c&&delete c[b];return a}
function Kb(a,b,c,d,e){var f=I(a,b,d);Array.isArray(f)||(f=yb);var g=F(f);g&1||tb(f);if(e)g&2||E(f,2),c&1||Object.freeze(f);else{e=!(c&2);var h=g&2;c&1||!h?e&&g&16&&!h&&(a=f,D?a[D]&&(a[D]&=-17):void 0!==a.J&&(a.J&=-17)):(f=tb(Array.prototype.slice.call(f)),K(a,b,f,d))}return f}function Lb(a,b){a=I(a,b);return null==a?a:!!a}function Mb(a,b){for(var c=0,d=0;d<b.length;d++){var e=b[d];null!=I(a,e)&&(0!==c&&K(a,c,void 0,!1),c=e)}return c}
function L(a,b,c,d){var e=d=void 0===d?!1:d,f=I(a,c,e);var g=!1;var h=null==f||"object"!==typeof f||(g=Array.isArray(f))||f.Y!==wb?g?new b(f):void 0:f;h!==f&&null!=h&&(K(a,c,h,e),E(h.A,F(a.A)&18));b=h;if(null==b)return b;H(a.A)||(e=Nb(b),e!==b&&(b=e,K(a,c,b,d)));return b}
function Ob(a,b,c){var d=H(a.A);a.h||(a.h={});var e=a.h[c];var f=Kb(a,c,3,void 0,d);if(e)d||(f=Object.isFrozen(e),d&&!f?Object.freeze(e):!d&&f&&(e=Array.prototype.slice.call(e),a.h[c]=e));else{var g=f;e=[];var h=!!(F(a.A)&16);f=H(g);var k=g;!d&&f&&(g=Array.prototype.slice.call(g));for(var l=f,n=0;n<g.length;n++){var y=g[n];var A=b,x=!1;x=void 0===x?!1:x;y=Array.isArray(y)?new A(y):x?new A:void 0;if(void 0!==y){A=y.A;var C=x=F(A);f&&(C|=2);h&&(C|=16);C!=x&&G(A,C);A=C;l||(l=!!(2&A));e.push(y)}}a.h[c]=
e;h=F(g);b=h|33;b=l?b&-9:b|8;h!=b&&(l=g,Object.isFrozen(l)&&(l=Array.prototype.slice.call(l)),G(l,b),g=l);k!==g&&K(a,c,g);(d||d&&f)&&E(e,2);d&&Object.freeze(e)}a=Kb(a,c,3,void 0,d);if(!(d||F(a)&8)){for(d=0;d<e.length;d++)c=e[d],f=Nb(c),c!==f&&(e[d]=f,a[d]=f.A);E(a,8)}return e}function Pb(a,b,c){Ab(a);null==c&&(c=void 0);return K(a,b,c)}function Qb(a,b,c){var d=Rb;Ab(a);null==c&&(c=void 0);Ab(a);(d=Mb(a,d))&&d!==b&&null!=c&&K(a,d,void 0,!1);return K(a,b,c)}
function Sb(a,b,c,d){Ab(a);var e=null==c?yb:tb([]);if(null!=c){for(var f=!!c.length,g=0;g<c.length;g++){var h=c[g];f=f&&!H(h.A);e[g]=h.A}e=sb(e,(f?8:0)|1);a.h||(a.h={});a.h[b]=c}else a.h&&(a.h[b]=void 0);return K(a,b,e,d)}function Tb(a,b,c){var d=Mb(a,Rb)===c;return L(a,b,d?c:-1)};function Ub(a,b,c){c=void 0===c?vb:c;if(null!=a){if(ob&&a instanceof Uint8Array)return a.length?new rb(new Uint8Array(a)):qb||(qb=new rb(null));if(Array.isArray(a)){var d=F(a);if(d&2)return a;if(b&&!(d&32)&&(d&16||0===d))return G(a,d|2),a;a=Fb(a,Ub,d&4?vb:c,!0);b=F(a);b&4&&b&2&&Object.freeze(a);return a}return a.Y===wb?Vb(a):a}}function Wb(a,b,c,d,e,f,g){(a=a.h&&a.h[c])?(d=F(a),d&2?d=a:(f=Ra(a,Vb),vb(d,f),Object.freeze(f),d=f),Sb(b,c,d,e)):J(b,c,Ub(d,f,g),e)}
function Vb(a){if(H(a.A))return a;a=Xb(a,!0);E(a.A,2);return a}
function Xb(a,b){var c=a.A,d=[];E(d,16);var e=a.constructor.messageId;e&&d.push(e);e=a.I;if(e){d.length=c.length;d.fill(void 0,d.length,c.length);var f={};d[d.length-1]=f}0!==(F(c)&128)&&Bb(d);b=b||H(a.A)?vb:ub;var g=a.constructor;Cb=d;d=new g(d);Cb=void 0;a.la&&(d.la=a.la.slice());g=!!(F(c)&16);for(var h=e?c.length-1:c.length,k=0;k<h;k++)Wb(a,d,k-a.O,c[k],!1,g,b);if(e)for(var l in e)c=e[l],h=+l,Number.isNaN(h)?f[h]=c:Wb(a,d,h,c,!0,g,b);return d}
function Nb(a){if(!H(a.A))return a;var b=Xb(a,!1);b.j=a;return b};function M(a,b,c){null==a&&(a=Cb);Cb=void 0;var d=this.constructor.h||0,e=0<d,f=this.constructor.messageId,g=!1;if(null==a){a=f?[f]:[];var h=!0;G(a,48)}else{if(!Array.isArray(a))throw Error();if(f&&f!==a[0])throw Error();var k=E(a,0),l=k;if(h=0!==(16&l))(g=0!==(32&l))||(l|=32);if(e)if(128&l)d=0;else{if(0<a.length){var n=a[a.length-1];if(xb(n)&&"g"in n){d=0;l|=128;delete n.g;var y=!0,A;for(A in n){y=!1;break}y&&a.pop()}}}else if(128&l)throw Error();k!==l&&G(a,l)}this.O=(f?0:-1)-d;this.h=void 0;this.A=
a;a:{f=this.A.length;d=f-1;if(f&&(f=this.A[d],xb(f))){this.I=f;this.i=d-this.O;break a}void 0!==b&&-1<b?(this.i=Math.max(b,d+1-this.O),this.I=void 0):this.i=Number.MAX_VALUE}if(!e&&this.I&&"g"in this.I)throw Error('Unexpected "g" flag in sparse object of message that is not a group type.');if(c){b=h&&!g&&!0;e=this.i;var x;for(h=0;h<c.length;h++)g=c[h],g<e?(g+=this.O,(d=a[g])?Yb(d,b):a[g]=yb):(x||(x=Jb(this)),(d=x[g])?Yb(d,b):x[g]=yb)}}M.prototype.toJSON=function(){return Fb(this.A,Ib,Hb)};
function Yb(a,b){if(Array.isArray(a)){var c=F(a),d=1;!b||c&2||(d|=16);(c&d)!==d&&G(a,c|d)}}M.prototype.Y=wb;M.prototype.toString=function(){return this.A.toString()};function Zb(a,b){var c=$b;this.i=a;this.h=b;this.j=c};function $b(a){return L(a,this.h,this.i,!0)};function ac(a){M.call(this,a)}q(ac,M);function bc(a){M.call(this,a)}q(bc,M);bc.prototype.setEnabled=function(a){return J(this,2,a)};function cc(a){M.call(this,a)}q(cc,M);cc.prototype.setEnabled=function(a){return J(this,2,a)};function dc(a){M.call(this,a,-1,ec)}q(dc,M);var ec=[3];function fc(a){M.call(this,a,-1,gc)}q(fc,M);var gc=[3];function hc(a){M.call(this,a,-1,ic)}q(hc,M);function jc(a,b){return Sb(a,2,b)}var ic=[2];function kc(a){M.call(this,a)}q(kc,M);function lc(a,b){return J(a,2,b)}function mc(a,b){return Pb(a,3,b)};function nc(a){M.call(this,a)}q(nc,M);function oc(a){var b=new nc;return J(b,3,a)}function pc(a,b){return Qb(a,5,b)}var Rb=[4,5,6,7,8];function N(){}N.prototype.m=function(){return this.j||(Object.defineProperties(this,{j:{value:qc=qc+1|0,enumerable:!1}}),this.j)};N.prototype.toString=function(){var a=O(rc(P(sc(this))))+"@";var b=(this.m()>>>0).toString(16);return a+O(b)};N.prototype.u=["java.lang.Object",0];function tc(){}q(tc,N);tc.prototype.i=function(a){this.h=a;uc(a,this)};function vc(a){wc(a.h)&&(Error.captureStackTrace?Error.captureStackTrace(xc(a.h,wc,yc)):xc(a.h,wc,yc).stack=Error().stack)}tc.prototype.toString=function(){var a=rc(P(sc(this))),b=this.o;return null==b?a:O(a)+": "+O(b)};tc.prototype.u=["java.lang.Throwable",0];function zc(){}q(zc,tc);zc.prototype.u=["java.lang.Exception",0];function Ac(){}q(Ac,zc);Ac.prototype.u=["java.lang.RuntimeException",0];function Bc(){}q(Bc,Ac);Bc.prototype.u=["java.lang.IndexOutOfBoundsException",0];function sc(a){return a.constructor};function Cc(){}q(Cc,N);Cc.prototype.u=["java.lang.Number",0];function Dc(){}q(Dc,Ac);Dc.prototype.i=function(a){Ac.prototype.i.call(this,Object.is(this.l,"__noinit__")?a:this.l)};Dc.prototype.u=["java.lang.JsException",0];function Ec(){}q(Ec,Dc);Ec.prototype.u=["java.lang.NullPointerException",0];function Fc(){}q(Fc,Ac);Fc.prototype.u=["java.util.NoSuchElementException",0];function Gc(){}q(Gc,Ac);Gc.prototype.u=["java.lang.ClassCastException",0];function Hc(){}q(Hc,Cc);Hc.prototype.u=["java.lang.Double",0];function Ic(){}q(Ic,N);Ic.prototype.u=["java.lang.Boolean",0];var qc=0;function Jc(){}Jc.prototype.u=["<native function>",1];function Kc(){}q(Kc,N);Kc.prototype.u=["<native object>",0];function Lc(a){var b=typeof a;if(null==b)throw a=new Ec,vc(a),a.l="__noinit__",a.i(new TypeError(a)),a.h;switch(b){case "number":return P(Hc);case "boolean":return P(Ic);case "string":return P(Mc);case "function":return P(Jc)}if(a instanceof N)a=P(sc(a));else if(Array.isArray(a))a=(a=a.za)?P(a.Da,a.Ca):P(N,1);else if(null!=a)a=P(Kc);else throw new TypeError("null.getClass()");return a};function xc(a,b,c){if(null!=a&&!b(a))throw a=O(rc(Lc(a)))+" cannot be cast to "+O(rc(P(c))),b=new Gc,b.o=a,vc(b),b.i(Error(b)),b.h;return a};function Nc(){}q(Nc,Bc);Nc.prototype.u=["java.lang.StringIndexOutOfBoundsException",0];function Mc(){}q(Mc,N);function O(a){return null==a?"null":a.toString()}Mc.prototype.u=["java.lang.String",0];function Oc(a,b){this.h=a;this.i=b}q(Oc,N);function rc(a){return 0!=a.i?O(Pc("[",a.i))+O(3==a.h.prototype.u[1]?a.h.prototype.u[2]:"L"+O(a.h.prototype.u[0])+";"):a.h.prototype.u[0]}function Qc(a,b){b=a.lastIndexOf(b)+1|0;var c=a.length+1|0;if(0>b||b>=c)throw a=new Nc,a.o="Index: "+b+", Size: "+c,vc(a),a.i(Error(a)),a.h;return a.substr(b)}Oc.prototype.toString=function(){return String(0==this.i&&1==this.h.prototype.u[1]?"interface ":0==this.i&&3==this.h.prototype.u[1]?"":"class ")+O(rc(this))};
function Pc(a,b){for(var c="",d=0;d<b;d=d+1|0)c=O(c)+O(a);return c}Oc.prototype.u=["java.lang.Class",0];function P(a,b){var c=b||0;return Ua(a.prototype,"$$class/"+c,function(){return new Oc(a,c)})};function uc(a,b){if(a instanceof Object)try{a.Aa=b,Object.defineProperties(a,{cause:{get:function(){return b.v&&b.v.h}}})}catch(c){}};function yc(){}function wc(a){return a instanceof Error}yc.prototype.u=["Error",0];function Rc(a){a&&"function"==typeof a.S&&a.S()};function Sc(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];Ca(d)?Sc.apply(null,d):Rc(d)}};function Q(){this.v=this.v;this.o=this.o}Q.prototype.v=!1;Q.prototype.ma=function(){return this.v};Q.prototype.S=function(){this.v||(this.v=!0,this.B())};function R(a,b){Tc(a,Ga(Rc,b))}function Tc(a,b,c){a.v?void 0!==c?b.call(c):b():(a.o||(a.o=[]),a.o.push(void 0!==c?w(b,c):b))}Q.prototype.B=function(){if(this.o)for(;this.o.length;)this.o.shift()()};function Uc(){Q.call(this);this.h=null}q(Uc,Q);function Vc(a,b,c){a.ma()||(Wc(a),a.h=new Image,a.h.onload=function(){c&&c(!0);Wc(a)},a.h.onerror=function(){c&&c(!1);Wc(a)},a.h.src=b)}function Wc(a){if(a.h)try{a.h.onload=null,a.h.onerror=null,a.h=null}catch(b){}}Uc.prototype.B=function(){Wc(this)};function Xc(a,b){this.type=a;this.h=this.target=b;this.defaultPrevented=!1}Xc.prototype.j=function(){this.defaultPrevented=!0};function Yc(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,Yc));a.stack||(a.stack=Zc(Yc));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function $c(a,b){a=Yc(a);if(b)for(var c in b){var d=a,e=c,f=b[c];d.__closure__error__context__984382||(d.__closure__error__context__984382={});d.__closure__error__context__984382[e]=f}return a}
function Zc(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||Zc),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=ad(a||arguments.callee.caller,[]));return b}
function ad(a,b){var c=[];if(0<=Pa(b,a))c.push("[...circular reference...]");else if(a&&50>b.length){c.push(bd(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){0<e&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=bd(f))?f:"[fn]";break;default:f=typeof f}40<f.length&&(f=f.slice(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(ad(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")}function bd(a){if(cd[a])return cd[a];a=String(a);if(!cd[a]){var b=/function\s+([^\(]+)/m.exec(a);cd[a]=b?b[1]:"[Anonymous]"}return cd[a]}var cd={};var dd=function(){if(!v.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{v.addEventListener("test",function(){},b),v.removeEventListener("test",function(){},b)}catch(c){}return a}();function ed(a,b){Xc.call(this,a?a.type:"");this.relatedTarget=this.h=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.h=b;if(b=a.relatedTarget){if(Ya){a:{try{Ta(b.nodeName);var e=!0;break a}catch(f){}e=
!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=
a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:fd[a.pointerType]||"";this.state=a.state;this.i=a;a.defaultPrevented&&ed.N.j.call(this)}}z(ed,Xc);var fd={2:"touch",3:"pen",4:"mouse"};ed.prototype.j=function(){ed.N.j.call(this);var a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};var gd="closure_listenable_"+(1E6*Math.random()|0);function hd(a){return!(!a||!a[gd])};var id=0;function jd(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.P=e;this.key=++id;this.removed=this.W=!1}function kd(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.P=null};function ld(a,b,c){for(var d in a)b.call(c,a[d],d,a)}var md="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function nd(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<md.length;f++)c=md[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function od(a){this.src=a;this.h={};this.i=0}od.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.h[f];a||(a=this.h[f]=[],this.i++);var g=pd(a,b,d,e);-1<g?(b=a[g],c||(b.W=!1)):(b=new jd(b,this.src,f,!!d,e),b.W=c,a.push(b));return b};od.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.h))return!1;var e=this.h[a];b=pd(e,b,c,d);return-1<b?(kd(e[b]),Array.prototype.splice.call(e,b,1),0==e.length&&(delete this.h[a],this.i--),!0):!1};
function qd(a,b){var c=b.type;if(c in a.h){var d=a.h[c],e=Pa(d,b),f;(f=0<=e)&&Array.prototype.splice.call(d,e,1);f&&(kd(b),0==a.h[c].length&&(delete a.h[c],a.i--))}}od.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.h)if(!a||c==a){for(var d=this.h[c],e=0;e<d.length;e++)++b,kd(d[e]);delete this.h[c];this.i--}return b};function rd(a,b,c,d,e){a=a.h[b.toString()];b=-1;a&&(b=pd(a,c,d,e));return-1<b?a[b]:null}
function pd(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.P==d)return e}return-1};var sd="closure_lm_"+(1E6*Math.random()|0),td={},ud=0;function vd(a,b,c,d,e){if(d&&d.once)return wd(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)vd(a,b[f],c,d,e);return null}c=xd(c);return hd(a)?a.listen(b,c,Da(d)?!!d.capture:!!d,e):yd(a,b,c,!1,d,e)}
function yd(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=Da(e)?!!e.capture:!!e,h=zd(a);h||(a[sd]=h=new od(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=Ad();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)dd||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(Bd(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");ud++;return c}
function Ad(){function a(c){return b.call(a.src,a.listener,c)}var b=Cd;return a}function wd(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)wd(a,b[f],c,d,e);return null}c=xd(c);return hd(a)?a.i.add(String(b),c,!0,Da(d)?!!d.capture:!!d,e):yd(a,b,c,!0,d,e)}function Dd(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)Dd(a,b[f],c,d,e);else d=Da(d)?!!d.capture:!!d,c=xd(c),hd(a)?a.i.remove(String(b),c,d,e):a&&(a=zd(a))&&(b=rd(a,b,c,d,e))&&Ed(b)}
function Ed(a){if("number"!==typeof a&&a&&!a.removed){var b=a.src;if(hd(b))qd(b.i,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(Bd(c),d):b.addListener&&b.removeListener&&b.removeListener(d);ud--;(c=zd(b))?(qd(c,a),0==c.i&&(c.src=null,b[sd]=null)):kd(a)}}}function Bd(a){return a in td?td[a]:td[a]="on"+a}function Cd(a,b){if(a.removed)a=!0;else{b=new ed(b,this);var c=a.listener,d=a.P||a.src;a.W&&Ed(a);a=c.call(d,b)}return a}
function zd(a){a=a[sd];return a instanceof od?a:null}var Fd="__closure_events_fn_"+(1E9*Math.random()>>>0);function xd(a){if("function"===typeof a)return a;a[Fd]||(a[Fd]=function(b){return a.handleEvent(b)});return a[Fd]};function S(){Q.call(this);this.i=new od(this);this.M=this;this.s=null}z(S,Q);S.prototype[gd]=!0;S.prototype.removeEventListener=function(a,b,c,d){Dd(this,a,b,c,d)};
function Gd(a,b){var c,d=a.s;if(d)for(c=[];d;d=d.s)c.push(d);a=a.M;d=b.type||b;if("string"===typeof b)b=new Xc(b,a);else if(b instanceof Xc)b.target=b.target||a;else{var e=b;b=new Xc(d,a);nd(b,e)}e=!0;if(c)for(var f=c.length-1;0<=f;f--){var g=b.h=c[f];e=Hd(g,d,!0,b)&&e}g=b.h=a;e=Hd(g,d,!0,b)&&e;e=Hd(g,d,!1,b)&&e;if(c)for(f=0;f<c.length;f++)g=b.h=c[f],e=Hd(g,d,!1,b)&&e}S.prototype.B=function(){S.N.B.call(this);this.i&&this.i.removeAll(void 0);this.s=null};
S.prototype.listen=function(a,b,c,d){return this.i.add(String(a),b,!1,c,d)};function Hd(a,b,c,d){b=a.i.h[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,k=g.P||g.src;g.W&&qd(a.i,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented};function Id(a,b){this.j=a;this.l=b;this.i=0;this.h=null}Id.prototype.get=function(){if(0<this.i){this.i--;var a=this.h;this.h=a.next;a.next=null}else a=this.j();return a};function Jd(a,b){a.l(b);100>a.i&&(a.i++,b.next=a.h,a.h=b)};function Kd(){};var Ld;function Md(a,b){this.h=a===Nd&&b||"";this.i=Od}Md.prototype.ka=!0;Md.prototype.ja=function(){return this.h};var Od={},Nd={};function Pd(a,b){this.h=b===Qd?a:""}Pd.prototype.toString=function(){return this.h+""};Pd.prototype.ka=!0;Pd.prototype.ja=function(){return this.h.toString()};function Rd(a){return a instanceof Pd&&a.constructor===Pd?a.h:"type_error:TrustedResourceUrl"}var Qd={};
function Sd(a){if(void 0===Ld){var b=null;var c=v.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:Ha,createScript:Ha,createScriptURL:Ha})}catch(d){v.console&&v.console.error(d.message)}Ld=b}else Ld=b}a=(b=Ld)?b.createScriptURL(a):a;return new Pd(a,Qd)};function Td(a,b){ld(b,function(c,d){c&&"object"==typeof c&&c.ka&&(c=c.ja());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:Ud.hasOwnProperty(d)?a.setAttribute(Ud[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})}
var Ud={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};function Vd(a){return a.parentWindow||a.defaultView}
function Wd(a,b,c){function d(h){h&&b.appendChild("string"===typeof h?a.createTextNode(h):h)}for(var e=2;e<c.length;e++){var f=c[e];if(!Ca(f)||Da(f)&&0<f.nodeType)d(f);else{a:{if(f&&"number"==typeof f.length){if(Da(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}Qa(g?Sa(f):f,d)}}}function Xd(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)}
function Yd(){this.h=v.document||document}Yd.prototype.setProperties=Td;Yd.prototype.i=function(a,b,c){var d=this.h,e=arguments,f=e[1],g=Xd(d,String(e[0]));f&&("string"===typeof f?g.className=f:Array.isArray(f)?g.className=f.join(" "):Td(g,f));2<e.length&&Wd(d,g,e);return g};Yd.prototype.getChildren=function(a){return void 0!=a.children?a.children:Array.prototype.filter.call(a.childNodes,function(b){return 1==b.nodeType})};
Yd.prototype.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var Zd;
function $d(){var a=v.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!B("Presto")&&(a=function(){var e=Xd(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=w(function(k){if(("*"==h||k.origin==h)&&k.data==g)this.port1.onmessage()},this);
f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!B("Trident")&&!B("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.ga;c.ga=null;e()}};return function(e){d.next={ga:e};d=d.next;b.port2.postMessage(0)}}return function(e){v.setTimeout(e,0)}};function ae(){this.i=this.h=null}ae.prototype.add=function(a,b){var c=be.get();c.set(a,b);this.i?this.i.next=c:this.h=c;this.i=c};ae.prototype.remove=function(){var a=null;this.h&&(a=this.h,this.h=this.h.next,this.h||(this.i=null),a.next=null);return a};var be=new Id(function(){return new ce},function(a){return a.reset()});function ce(){this.next=this.h=this.i=null}ce.prototype.set=function(a,b){this.i=a;this.h=b;this.next=null};ce.prototype.reset=function(){this.next=this.h=this.i=null};var de,ee=!1,fe=new ae;function ge(a,b){de||he();ee||(de(),ee=!0);fe.add(a,b)}function he(){if(v.Promise&&v.Promise.resolve){var a=v.Promise.resolve(void 0);de=function(){a.then(ie)}}else de=function(){var b=ie;"function"!==typeof v.setImmediate||v.Window&&v.Window.prototype&&!B("Edge")&&v.Window.prototype.setImmediate==v.setImmediate?(Zd||(Zd=$d()),Zd(b)):v.setImmediate(b)}}function ie(){for(var a;a=fe.remove();){try{a.i.call(a.h)}catch(b){Ka(b)}Jd(be,a)}ee=!1};function T(a){this.h=0;this.m=void 0;this.l=this.i=this.j=null;this.o=this.v=!1;if(a!=Kd)try{var b=this;a.call(void 0,function(c){je(b,2,c)},function(c){je(b,3,c)})}catch(c){je(this,3,c)}}function ke(){this.next=this.j=this.i=this.o=this.h=null;this.l=!1}ke.prototype.reset=function(){this.j=this.i=this.o=this.h=null;this.l=!1};var le=new Id(function(){return new ke},function(a){a.reset()});function me(a,b,c){var d=le.get();d.o=a;d.i=b;d.j=c;return d}
function ne(a){if(a instanceof T)return a;var b=new T(Kd);je(b,2,a);return b}function oe(){var a,b,c=new T(function(d,e){a=d;b=e});return new pe(c,a,b)}T.prototype.then=function(a,b,c){return qe(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};T.prototype.$goog_Thenable=!0;function re(a,b){b=me(b,b);b.l=!0;se(a,b)}function U(a,b){return qe(a,null,b)}T.prototype.cancel=function(a){if(0==this.h){var b=new te(a);ge(function(){ue(this,b)},this)}};
function ue(a,b){if(0==a.h)if(a.j){var c=a.j;if(c.i){for(var d=0,e=null,f=null,g=c.i;g&&(g.l||(d++,g.h==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.h&&1==d?ue(c,b):(f?(d=f,d.next==c.l&&(c.l=d),d.next=d.next.next):ve(c),we(c,e,3,b)))}a.j=null}else je(a,3,b)}function se(a,b){a.i||2!=a.h&&3!=a.h||xe(a);a.l?a.l.next=b:a.i=b;a.l=b}
function qe(a,b,c,d){var e=me(null,null,null);e.h=new T(function(f,g){e.o=b?function(h){try{var k=b.call(d,h);f(k)}catch(l){g(l)}}:f;e.i=c?function(h){try{var k=c.call(d,h);void 0===k&&h instanceof te?g(h):f(k)}catch(l){g(l)}}:g});e.h.j=a;se(a,e);return e.h}T.prototype.D=function(a){this.h=0;je(this,2,a)};T.prototype.C=function(a){this.h=0;je(this,3,a)};
function je(a,b,c){if(0==a.h){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.h=1;a:{var d=c,e=a.D,f=a.C;if(d instanceof T){se(d,me(e||Kd,f||null,a));var g=!0}else{if(d)try{var h=!!d.$goog_Thenable}catch(l){h=!1}else h=!1;if(h)d.then(e,f,a),g=!0;else{if(Da(d))try{var k=d.then;if("function"===typeof k){ye(d,k,e,f,a);g=!0;break a}}catch(l){f.call(a,l);g=!0;break a}g=!1}}}g||(a.m=c,a.h=b,a.j=null,xe(a),3!=b||c instanceof te||ze(a,c))}}
function ye(a,b,c,d,e){function f(k){h||(h=!0,d.call(e,k))}function g(k){h||(h=!0,c.call(e,k))}var h=!1;try{b.call(a,g,f)}catch(k){f(k)}}function xe(a){a.v||(a.v=!0,ge(a.s,a))}function ve(a){var b=null;a.i&&(b=a.i,a.i=b.next,b.next=null);a.i||(a.l=null);return b}T.prototype.s=function(){for(var a;a=ve(this);)we(this,a,this.h,this.m);this.v=!1};
function we(a,b,c,d){if(3==c&&b.i&&!b.l)for(;a&&a.o;a=a.j)a.o=!1;if(b.h)b.h.j=null,Ae(b,c,d);else try{b.l?b.o.call(b.j):Ae(b,c,d)}catch(e){Be.call(null,e)}Jd(le,b)}function Ae(a,b,c){2==b?a.o.call(a.j,c):a.i&&a.i.call(a.j,c)}function ze(a,b){a.o=!0;ge(function(){a.o&&Be.call(null,b)})}var Be=Ka;function te(a){Ia.call(this,a)}z(te,Ia);te.prototype.name="cancel";function pe(a,b,c){this.promise=a;this.resolve=b;this.reject=c};function Ce(a,b){S.call(this);this.j=a||1;this.h=b||v;this.l=w(this.ya,this);this.m=Date.now()}z(Ce,S);m=Ce.prototype;m.V=!1;m.L=null;m.ya=function(){if(this.V){var a=Date.now()-this.m;0<a&&a<.8*this.j?this.L=this.h.setTimeout(this.l,this.j-a):(this.L&&(this.h.clearTimeout(this.L),this.L=null),Gd(this,"tick"),this.V&&(this.stop(),this.start()))}};m.start=function(){this.V=!0;this.L||(this.L=this.h.setTimeout(this.l,this.j),this.m=Date.now())};
m.stop=function(){this.V=!1;this.L&&(this.h.clearTimeout(this.L),this.L=null)};m.B=function(){Ce.N.B.call(this);this.stop();delete this.h};function De(a,b){if("function"!==typeof a)if(a&&"function"==typeof a.handleEvent)a=w(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:v.setTimeout(a,b||0)}
function Ee(){var a=null;return U(new T(function(b,c){a=De(function(){b(void 0)},3E4);-1==a&&c(Error("Failed to schedule timer."))}),function(b){v.clearTimeout(a);throw b;})};function Fe(a,b,c){Q.call(this);this.h=a;this.j=b||0;this.i=c;this.l=w(this.na,this)}z(Fe,Q);m=Fe.prototype;m.R=0;m.B=function(){Fe.N.B.call(this);this.stop();delete this.h;delete this.i};m.start=function(a){this.stop();this.R=De(this.l,void 0!==a?a:this.j)};m.stop=function(){this.isActive()&&v.clearTimeout(this.R);this.R=0};m.isActive=function(){return 0!=this.R};m.na=function(){this.R=0;this.h&&this.h.call(this.i)};function Ge(){Q.call(this);this.h=null}q(Ge,Q);Ge.prototype.B=function(){Rc(this.h)};function V(a){Q.call(this);this.i=a;this.h={}}z(V,Q);var He=[];V.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(He[0]=b.toString()),b=He);for(var e=0;e<b.length;e++){var f=vd(a,b[e],c||this.handleEvent,d||!1,this.i||this);if(!f)break;this.h[f.key]=f}return this};
function Ie(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)Ie(a,b,c[g],d,e,f);else d=d||a.handleEvent,e=Da(e)?!!e.capture:!!e,f=f||a.i||a,d=xd(d),e=!!e,c=hd(b)?rd(b.i,String(c),d,e,f):b?(b=zd(b))?rd(b,c,d,e,f):null:null,c&&(Ed(c),delete a.h[c.key])}V.prototype.removeAll=function(){ld(this.h,function(a,b){this.h.hasOwnProperty(b)&&Ed(a)},this);this.h={}};V.prototype.B=function(){V.N.B.call(this);this.removeAll()};
V.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function Je(){S.call(this);this.h=new V(this);this.h.listen(window,["online","offline"],this.j)}z(Je,S);Je.prototype.j=function(){Gd(this,navigator.onLine?"online":"offline")};Je.prototype.B=function(){Je.N.B.call(this);this.h.S();this.h=null};function Ke(a,b){this.name=a;this.value=b}Ke.prototype.toString=function(){return this.name};var Le=new Ke("OFF",Infinity),Me=new Ke("SEVERE",1E3),Ne=new Ke("WARNING",900),Oe=new Ke("INFO",800),Pe=new Ke("CONFIG",700),Qe=new Ke("FINE",500);function Re(){this.clear()}var Se;Re.prototype.clear=function(){};function Te(a,b,c){this.h=void 0;this.reset(a||Le,b,c,void 0,void 0)}Te.prototype.reset=function(a,b,c,d){this.j=d||Date.now();this.l=a;this.o=b;this.i=c;this.h=void 0};Te.prototype.getMessage=function(){return this.o};
function Ue(a,b){this.h=null;this.j=[];this.i=(void 0===b?null:b)||null;this.o=[];this.l={h:function(){return a}}}function Ve(a){return a.h?a.h:a.i?Ve(a.i):Le}function We(a,b){for(;a;)a.j.forEach(function(c){c(b)}),a=a.i}function Xe(){this.entries={};var a=new Ue("");a.h=Pe;this.entries[""]=a}var Ye;function Ze(a,b){var c=a.entries[b];if(c)return c;c=Ze(a,b.slice(0,Math.max(b.lastIndexOf("."),0)));var d=new Ue(b,c);a.entries[b]=d;c.o.push(d);return d}function $e(){Ye||(Ye=new Xe);return Ye}
function af(a){return Ze($e(),a).l}function bf(a,b,c,d){var e;if(e=a)if(e=a&&b){e=b.value;var f=a?Ve(Ze($e(),a.h())):Le;e=e>=f.value}e&&(b=b||Le,e=Ze($e(),a.h()),"function"===typeof c&&(c=c()),Se||(Se=new Re),a=new Te(b,c,a.h()),a.h=d,We(e,a))}function W(a,b,c){a&&bf(a,Me,b,c)}function X(a,b,c){a&&bf(a,Oe,b,c)}function cf(a,b){a&&bf(a,Qe,b)};var df=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function ef(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function ff(a,b,c){c=null!=c?"="+encodeURIComponent(String(c)):"";if(b+=c){c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;a=a[0]+(a[1]?"?"+a[1]:"")+a[2]}return a}function gf(a,b,c){for(;0<=(b=a.indexOf("zx",b))&&b<c;){var d=a.charCodeAt(b-1);if(38==d||63==d)if(d=a.charCodeAt(b+2),!d||61==d||38==d||35==d)return b;b+=3}return-1}var hf=/#|$/,jf=/[?&]($|#)/;
function kf(a){for(var b=Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36),c=a.search(hf),d=0,e,f=[];0<=(e=gf(a,d,c));)f.push(a.substring(d,e)),d=Math.min(a.indexOf("&",e)+1||c,c);f.push(a.slice(d));a=f.join("").replace(jf,"$1");return ff(a,"zx",b)};function lf(a,b,c,d,e){S.call(this);var f=this;this.j=af("apps.common.net.OfflineObserver");bf(this.j,Pe,"new apps.common.net.OfflineObserver("+a+")");this.C=new V(this);this.Z=a;this.l=new Fe(function(){return mf(f,!1)},1E4);this.K=c||12E4;this.m=new Je;this.h=b;this.H=Date.now();this.D=d||new Ge;this.G=e||new Uc;this.C.listen(this.m,"online",this.F).listen(this.m,"offline",this.F);this.aa=oe();nf(this,!0,!0)}q(lf,S);
function nf(a,b,c){var d=Date.now(),e=a.h?25E3:1E4;if(b||!a.h)b=c?0:e,a.H=d-b;else{b=e;c=a.H+e;for(var f=1;c<d&&b<a.K;)b=Math.min(a.K,e*Math.pow(1.2,f++)),c+=b}d=a.D;a=w(a.U,a);e=b;Rc(d.h);d.h=new Fe(a,e);d.h.start()}lf.prototype.U=function(){nf(this);of(this)};lf.prototype.F=function(){X(this.j,"OnlineHandler transitioned "+(navigator.onLine?"ONLINE":"OFFLINE"));nf(this,!0,!0)};function of(a){cf(a.j,"Performing image ping.");a.l.stop();a.l.start();Vc(a.G,kf(a.Z),function(b){return mf(a,b)})}
function mf(a,b){cf(a.j,"Image ping "+(b?"succeeded":"failed"));a.l.stop();cf(a.j,"notifyTrustedNetworkResult("+b+")");a.aa.resolve();b!=a.h?(X(a.j,"OfflineObserver transitioned "+(b?"ONLINE":"OFFLINE")),a.h=b,Gd(a,b?"a":"b"),b=!0):b=!1;nf(a,b)}lf.prototype.B=function(){this.l.stop();Sc(this.D,this.l,this.m,this.C,this.G);S.prototype.B.call(this)};function pf(){this.h=!1}q(pf,N);m=pf.prototype;m.S=function(){if(!this.h){this.h=!0;this.ba();var a=P(sc(this));Qc(Qc(O(a.h.prototype.u[0])+O(Pc("[]",a.i)),"."),"$")}};m.ma=function(){return this.h};m.ba=function(){if(this.l){var a=new qf;a.h=this.l;var b=new rf;a=a.h;b.h=-1;for(b.i=a;(b.h+1|0)<b.i.length;){a=b;if(!((a.h+1|0)<a.i.length))throw b=new Fc,vc(b),b.i(Error(b)),b.h;var c=a.h=a.h+1|0;a.i[c].S()}this.l.length=0}};m.toString=function(){return N.prototype.toString.call(this)||""};
m.u=["com.google.apps.xplat.disposable.Disposable",0];function sf(){this.h=!1}q(sf,pf);sf.prototype.u=["com.google.apps.docs.xplat.disposable.Disposable",0];function tf(){this.h=0}q(tf,N);tf.prototype.u=["com.google.gwt.corp.collections.AbstractJsArray$Iter",0];function rf(){this.h=0}q(rf,tf);rf.prototype.u=["com.google.gwt.corp.collections.JsArray$Iter",0];function qf(){}q(qf,N);qf.prototype.u=["com.google.gwt.corp.collections.JsArray$1",0];function uf(){}function vf(a){return a instanceof Array}uf.prototype.u=["Array",0];function wf(){}q(wf,N);function xf(a){return a instanceof wf}wf.prototype.u=["com.google.apps.docsshared.xplat.observable.EventObserverTracker$ObservableObserverPair",0];function yf(){this.h=!1;this.i=xc([],vf,uf)}q(yf,sf);function zf(a,b,c){a=a.i;c=b.h(c);var d=new wf;d.h=b;d.i=c;a.push(d)}yf.prototype.ba=function(){this.removeAll();sf.prototype.ba.call(this)};yf.prototype.removeAll=function(){for(var a=xc(this.i.pop(),xf,wf);a;)a.h.j(a.i)&&a.h.i(a.i),a=xc(this.i.pop(),xf,wf)};yf.prototype.u=["com.google.apps.docsshared.xplat.observable.EventObserverTracker",0];function Af(a,b,c,d){c=void 0===c?[]:c;d=void 0===d?[]:d;Q.call(this);var e=this;this.j=b||this;this.l=a;this.i=new yf;R(this,this.i);this.h=new V(this);R(this,this.h);this.m=c;this.s=d;a instanceof lf?(this.h.listen(a,"a",function(){return Bf(e)}),this.h.listen(a,"b",function(){return Cf(e)})):(zf(this.i,this.l.fa(),function(){return Bf(e)}),zf(this.i,this.l.ea(),function(){return Cf(e)}))}q(Af,Q);function Df(a,b){a.m.push(b)}function Bf(a){a.m.forEach(function(b){try{b.call(a.j)}catch(c){}})}
function Cf(a){a.s.forEach(function(b){try{b.call(a.j)}catch(c){}})};function Ef(a){M.call(this,a)}q(Ef,M);function Ff(){S.call(this);this.h=3;this.j=null;this.l=!1;this.m=oe()}q(Ff,S);function Gf(a,b,c){if((1==b||2==b)&&!c)throw Error("Eligibility must be provided when transitioning to ENABLED or DISABLED. New state: "+b);c&&(a.j=c);a.h=b;a.m.resolve(null);Gd(a,"c")}function Hf(a){if(!a.j)return!1;a=I(a.j,1);return 1==a||6==a};function If(a){M.call(this,a)}q(If,M);If.prototype.getTitle=function(){return I(this,2)};If.prototype.setTitle=function(a){return J(this,2,a)};function Jf(a){M.call(this,a)}q(Jf,M);function Kf(a){var b=new Jf;return J(b,1,a)};function Lf(a,b){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);this.xa=b}q(Lf,Error);function Mf(a,b,c){Xc.call(this,"broadcast-message",a);this.i=b;this.data=c}q(Mf,Xc);function Nf(a){M.call(this,a,1)}q(Nf,M);function Of(a){M.call(this,a)}q(Of,M);Of.prototype.getMessage=function(){return L(this,Nf,2)};function Pf(a,b){S.call(this);this.l=!1;this.h=null;this.j=new V(this);R(this,this.j);this.m=a;this.D=b}q(Pf,S);Pf.prototype.connect=function(){this.l||(this.l=!0,this.h=this.m.h,this.j.listen(this.h,"message",this.C.bind(this)),this.h.start())};Pf.prototype.C=function(a){var b=a.i;null!=b.data[1]?(b=new Of(b.data),a=I(b,3),b=b.getMessage()):(a=this.D,b=new Nf(b.data));Gd(this,new Mf(this,a,b))};Pf.prototype.B=function(){if(this.h){var a=new Of;a=J(a,1,0);this.h.postMessage(Fb(a.A,Gb,Hb));this.h.close()}S.prototype.B.call(this)};function Qf(a,b,c){Q.call(this);this.m=a;this.j=b;this.l=c||null;this.h=[];this.i=new V(this);R(this,this.i);this.i.listen(this.m,"broadcast-message",this.s)}q(Qf,Q);Qf.prototype.listen=function(a,b){this.h.push(new Rf(a,b));return this};Qf.prototype.s=function(a){for(var b=0;b<this.h.length;b++){var c=this.h[b];if(!this.j||this.j==a.i){var d=c.h.j(a.data);null!=d&&c.P.call(this.l,d,a.i)}}};function Rf(a,b){this.h=a;this.P=b};function Sf(a){M.call(this,a)}q(Sf,M);var Tf=new Zb(102041228,Sf);function Uf(a){M.call(this,a)}q(Uf,M);var Vf=new Zb(108529910,Uf);function Wf(a){M.call(this,a)}q(Wf,M);var Xf=new Zb(122453513,Wf);function Yf(a){M.call(this,a)}q(Yf,M);function Zf(a){M.call(this,a)}q(Zf,M);function $f(a,b){Q.call(this);this.l=a;this.D=this.l.h;this.M=b;this.F=new Af(this.l,this,[this.G],[this.H]);R(this,this.F);this.C=this.j=0;this.i=this.m=2E4;this.h=new Fe(this.K,0,this);R(this,this.h);this.s=!1}q($f,Q);function ag(a){if(15<=a.j)return a.h.stop(),!1;if(!a.D&&!a.l.h&&3<=a.C)return!0;bg(a);return!0}function bg(a){15<=a.j||a.s||a.h.isActive()||a.h.start(a.i)}
$f.prototype.K=function(){this.M();this.D||this.l.h||this.C++;this.j++;144E5!=this.i&&(this.i=Math.min(this.i+(50+50*Math.random())/100*this.m,144E5),this.m*=2)};$f.prototype.G=function(){15<=this.j||(this.i=2E4*Math.random(),this.m=2E4,bg(this))};$f.prototype.H=function(){this.h.stop()};function cg(a,b,c,d,e,f,g,h,k,l){k=void 0===k?!1:k;l=void 0===l?!1:l;Q.call(this);var n=this;this.j=af("docs.offline.clients.BaseOfflineManager");this.h=a;this.K=oe();dg(this);this.D=this.s=null;this.Z=g;this.H=new Af(g,this);R(this,this.H);this.i=b;this.M=h||null;this.U=!1;this.l=null;k&&(this.l=new $f(this.Z,function(){return eg(n)}),R(this,this.l));this.C=oe();this.F=oe();this.G=new V(this);R(this,this.G);this.pa="user_"+c;this.ea=d;this.oa=e;this.fa=f;this.qa=!1;this.m=l?new Ce(25E3):null;l&&
this.G.listen(this.m,"tick",function(){return fg(n)})}q(cg,Q);m=cg.prototype;m.start=function(){var a=this;this.U||(this.U=!0,gg(this).then(function(){a.C.resolve();if(a.l){var b=a.l;b.h.stop();b.s=!0}},function(b){b=Yc(b);var c=new Lf(b.message,a.l?a.F.promise:void 0);$c(c,{originalStackTrace:b.stack});$c(c,{offlineManagerInitializationFailure:!0,isRetry:!1});a.C.reject(c);a.l?ag(a.l):a.i.m.resolve(c)}));return this.C.promise};
function gg(a){X(a.j,"Starting offline manager");return hg(a.h).then(function(){return ig(a.h)}).then(function(b){b.connect();a.K.resolve(b);return jg(a)}).then(function(){Df(a.H,function(){kg(a)});a.qa=!0})}function eg(a){a.h.reset();U(gg(a).then(function(){var b=a.l;b.h.stop();b.s=!0;a.F.resolve(a.l.j)}),function(){if(!ag(a.l)){var b=$c("OfflineService start retries exhausted",{offlineManagerInitializationFailure:!0,isRetry:!0});a.F.reject(b)}})}
function lg(a){if(2!=a.i.h)throw Error("Cannot enable offline when it is not disabled. Current state:"+a.i.h);X(a.j,"Enabling offline.");Gf(a.i,4);return mg(a.h,12).then(function(b){var c=I(b,1);return ne().then(function(){return 1==c||2==c?ng(a,1):og(a)}).then(function(){return b})})}
function jg(a){return pg(a.h,6E4).then(function(b){switch(I(b,1)){case 1:return U(qg(a.h),function(c){a.M&&a.M.h(Yc(c))}).then(function(){return ng(a,1)}).then(function(){kg(a)});case 2:return og(a);case 4:return X(a.j,"Offline is enabled for another user: "+I(b,2)),X(a.j,"Offline is disabled."),ng(a,2);case 3:return b=I(b,3),null!=b?rg(a,b).then(function(c){return c?ng(a,1):og(a)}):og(a);case 5:return sg(a).then(function(c){return c?ng(a,1):og(a)})}})}
function og(a){X(a.j,"Offline is disabled.");return ng(a,2)}m.va=function(a){var b=this;1==this.i.h&&!this.i.l&&0==I(a,2)&&U(pg(this.h).then(function(c){var d=b.i;d.l=Lb(c,5);Gd(d,"c")}),function(c){W(b.j,"Error querying for offline state after task finished event.",c)})};function rg(a,b){if(!a.ea)return ne(!1);X(a.j,"Offline is not enabled. Trying to auto enable with reason: "+b);return mg(a.h,b).then(function(c){switch(I(c,1)){case 1:return!0;case 2:return!0;default:return!1}})}
function sg(a){if(!a.oa)return ne(!1);X(a.j,"Trying to recover offline due to db corruption");return mg(a.h,2).then(function(b){switch(I(b,1)){case 1:case 2:return!0;default:return!1}})}function dg(a){var b;a.K.promise.then(function(c){b=c;return a.ia()}).then(function(c){a.s=new Qf(b,c,a);R(a,a.s);a.D=new Qf(b,null,a);R(a,a.D);a.s.listen(Vf,a.ua).listen(Tf,a.ta).listen(Xf,a.va);a.D.listen(Vf,a.sa).listen(Tf,a.ra)})}
m.ua=function(){var a=this;U(ng(this,1),function(b){X(a.j,"error in handle offline enabled from event bus",b)})};m.ta=function(){var a=this;U(og(this),function(b){X(a.j,"error in handle offline disabled from event bus",b)})};m.sa=function(){tg(this)};m.ra=function(){tg(this)};
function ng(a,b){return ug(a.h).then(function(c){Gf(a.i,b,c)}).then(function(){return pg(a.h).then(function(c){var d=a.i;d.l=!!Lb(c,5);Gd(d,"c")})}).then(function(){if(a.m)switch(b){case 1:a.m.start();fg(a);break;case 2:a.m.stop()}})}function vg(a,b,c){return wg(a.h,b,c).then(function(){})}function tg(a){ug(a.h).then(function(b){var c=a.i;c.j=b;Gd(c,"c")})}function kg(a){a.fa&&1==a.i.h&&U(xg(a.h),function(b){W(a.j,"Error executing pending tasks",b)})}m.ia=function(){return ne(this.pa)};
function fg(a){U(yg(a.h),function(b){W(a.j,"Got an error so stopping the SW heartbeat.",Yc(b));a.m&&a.m.stop()})};function zg(a,b,c){Q.call(this);this.s=null!=c?a.bind(c):a;this.m=b;this.l=null;this.i=!1;this.j=0;this.h=null}q(zg,Q);zg.prototype.stop=function(){this.h&&(v.clearTimeout(this.h),this.h=null,this.i=!1,this.l=null)};zg.prototype.pause=function(){this.j++};zg.prototype.resume=function(){this.j--;this.j||!this.i||this.h||(this.i=!1,Ag(this))};zg.prototype.B=function(){Q.prototype.B.call(this);this.stop()};
function Ag(a){a.h=De(function(){a.h=null;a.i&&!a.j&&(a.i=!1,Ag(a))},a.m);var b=a.l;a.l=null;a.s.apply(null,b)};function Bg(){}Bg.prototype.next=function(){return Cg};var Cg={done:!0,value:void 0};Bg.prototype.ca=function(){return this};function Dg(a){if(a instanceof Bg)return a;if("function"==typeof a.ca)return a.ca(!1);if(Ca(a)){var b=0,c=new Bg;c.next=function(){for(;;){if(b>=a.length)return Cg;if(b in a)return{value:a[b++],done:!1};b++}};return c}throw Error("Not implemented");};function Eg(a){this.h={};if(a)for(var b=0;b<a.length;b++)this.h[Fg(a[b])]=null;for(var c in Object.prototype);}var Gg={};function Fg(a){return a in Gg||32==String(a).charCodeAt(0)?" "+a:a}function Hg(a){return 32==a.charCodeAt(0)?a.slice(1):a}m=Eg.prototype;m.add=function(a){this.h[Fg(a)]=null};m.clear=function(){this.h={}};m.contains=function(a){return Fg(a)in this.h};m.has=function(a){return this.contains(a)};m.forEach=function(a,b){for(var c in this.h)a.call(b,Hg(c),void 0,this)};
m.values=Object.keys?function(){return Object.keys(this.h).map(Hg,this)}:function(){var a=[],b;for(b in this.h)a.push(Hg(b));return a};m.T=function(){return this.values()};m.remove=function(a){a=Fg(a);a in this.h?(delete this.h[a],a=!0):a=!1;return a};m.ca=function(){return Dg(this.T())};function Ig(a,b,c){S.call(this);this.h=af("docs.offline.clients.DocumentAvailabilityTracker");this.m=b;this.j=new Eg;this.l=new Eg;this.C=new zg(this.D,void 0!=c?c:3E3,this);R(this,this.C)}q(Ig,S);
function Jg(a,b){0!=b.length&&U(Kg(a.m,b).then(function(c){for(var d=[],e=new Eg(b),f=0;f<c.length;f++){var g=c[f],h=I(g,1);Lb(g,8)&&(d.push(h),e.remove(h))}c=a.j;for(f=0;f<d.length;f++)c.h[Fg(d[f])]=null;d=a.j;for(var k in e.h)delete d.h[k];Gd(a,"d");cf(a.h,"Updated cache based on "+b.length+" document updates.")}),function(c){c=new Lg("Failed to process document updates: "+b.length+" documents.",Yc(c));W(a.h,"DocumentAvailabilityTracker encountered background error:",c)})}
Ig.prototype.D=function(){var a=this.l.T();this.l.clear();Jg(this,a)};function Lg(a){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack)}q(Lg,Error);function Mg(a){this.h=a};function Ng(a){M.call(this,a,-1,Og)}q(Ng,M);function Pg(a,b){if(null==b)a=J(a,1,yb);else{var c=F(b);if(!(c&4)){if(c&2||Object.isFrozen(b))b=Array.prototype.slice.call(b);for(var d=0;d<b.length;d++)b[d]=b[d];G(b,c|5)}a=J(a,1,b)}return a}var Og=[1];function Qg(a){M.call(this,a)}q(Qg,M);function Rg(a,b){return J(a,1,b)};function Sg(a){M.call(this,a,-1,Tg)}q(Sg,M);function Ug(a,b){return Sb(a,1,b)}function Vg(a,b){return J(a,2,b)}function Wg(a,b){return J(a,3,b)}var Tg=[1];function Xg(a){M.call(this,a)}q(Xg,M);function Y(a){var b=new Xg;return J(b,1,a)};function Yg(a){M.call(this,a,-1,Zg)}q(Yg,M);var Zg=[1];function $g(a){M.call(this,a)}q($g,M);function ah(a){M.call(this,a)}q(ah,M);function bh(a){M.call(this,a)}q(bh,M);function ch(a){M.call(this,a)}q(ch,M);function dh(a){M.call(this,a)}q(dh,M);function eh(a){M.call(this,a)}q(eh,M);function fh(a){M.call(this,a)}q(fh,M);function gh(a){this.j=this.s=this.l="";this.m=null;this.o=this.i="";this.v=!1;var b;a instanceof gh?(this.v=a.v,hh(this,a.l),this.s=a.s,ih(this,a.j),jh(this,a.m),this.i=a.i,kh(this,lh(a.h)),this.o=a.o):a&&(b=String(a).match(df))?(this.v=!1,hh(this,b[1]||"",!0),this.s=mh(b[2]||""),ih(this,b[3]||"",!0),jh(this,b[4]),this.i=mh(b[5]||"",!0),kh(this,b[6]||"",!0),this.o=mh(b[7]||"")):(this.v=!1,this.h=new nh(null,this.v))}
gh.prototype.toString=function(){var a=[],b=this.l;b&&a.push(oh(b,ph,!0),":");var c=this.j;if(c||"file"==b)a.push("//"),(b=this.s)&&a.push(oh(b,ph,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.m,null!=c&&a.push(":",String(c));if(c=this.i)this.j&&"/"!=c.charAt(0)&&a.push("/"),a.push(oh(c,"/"==c.charAt(0)?qh:rh,!0));(c=this.h.toString())&&a.push("?",c);(c=this.o)&&a.push("#",oh(c,sh));return a.join("")};
gh.prototype.resolve=function(a){var b=new gh(this),c=!!a.l;c?hh(b,a.l):c=!!a.s;c?b.s=a.s:c=!!a.j;c?ih(b,a.j):c=null!=a.m;var d=a.i;if(c)jh(b,a.m);else if(c=!!a.i){if("/"!=d.charAt(0))if(this.j&&!this.i)d="/"+d;else{var e=b.i.lastIndexOf("/");-1!=e&&(d=b.i.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==f.length&&
""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.i=d:c=""!==a.h.toString();c?kh(b,lh(a.h)):c=!!a.o;c&&(b.o=a.o);return b};function hh(a,b,c){a.l=c?mh(b,!0):b;a.l&&(a.l=a.l.replace(/:$/,""));return a}function ih(a,b,c){a.j=c?mh(b,!0):b;return a}function jh(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.m=b}else a.m=null;return a}function kh(a,b,c){b instanceof nh?(a.h=b,th(a.h,a.v)):(c||(b=oh(b,uh)),a.h=new nh(b,a.v))}
function mh(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function oh(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,vh),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function vh(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var ph=/[#\/\?@]/g,rh=/[#\?:]/g,qh=/[#\?]/g,uh=/[#\?@]/g,sh=/#/g;function nh(a,b){this.i=this.h=null;this.j=a||null;this.l=!!b}
function wh(a){a.h||(a.h=new Map,a.i=0,a.j&&ef(a.j,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}m=nh.prototype;m.add=function(a,b){wh(this);this.j=null;a=xh(this,a);var c=this.h.get(a);c||this.h.set(a,c=[]);c.push(b);this.i+=1;return this};m.remove=function(a){wh(this);a=xh(this,a);return this.h.has(a)?(this.j=null,this.i-=this.h.get(a).length,this.h.delete(a)):!1};m.clear=function(){this.h=this.j=null;this.i=0};function yh(a,b){wh(a);b=xh(a,b);return a.h.has(b)}
m.forEach=function(a,b){wh(this);this.h.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};m.T=function(a){wh(this);var b=[];if("string"===typeof a)yh(this,a)&&(b=b.concat(this.h.get(xh(this,a))));else{a=Array.from(this.h.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};m.set=function(a,b){wh(this);this.j=null;a=xh(this,a);yh(this,a)&&(this.i-=this.h.get(a).length);this.h.set(a,[b]);this.i+=1;return this};
m.get=function(a,b){if(!a)return b;a=this.T(a);return 0<a.length?String(a[0]):b};m.toString=function(){if(this.j)return this.j;if(!this.h)return"";for(var a=[],b=Array.from(this.h.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.T(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.j=a.join("&")};function lh(a){var b=new nh;b.j=a.j;a.h&&(b.h=new Map(a.h),b.i=a.i);return b}
function xh(a,b){b=String(b);a.l&&(b=b.toLowerCase());return b}function th(a,b){b&&!a.l&&(wh(a),a.j=null,a.h.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(this.remove(d),this.remove(e),0<c.length&&(this.j=null,this.h.set(xh(this,e),Sa(c)),this.i+=c.length))},a));a.l=b};function zh(a){return(a=a.exec(La()))?a[1]:""}(function(){if(fb)return zh(/Firefox\/([0-9.]+)/);if(Wa||Xa||Va)return eb;if(jb){if(Oa()||B("iPad")||B("iPod")||B("Macintosh")){var a=zh(/CriOS\/([0-9.]+)/);if(a)return a}return zh(/Chrome\/([0-9.]+)/)}if(kb&&!(Oa()||B("iPad")||B("iPod")))return zh(/Version\/([0-9.]+)/);if(gb||hb){if(a=/Version\/(\S+).*Mobile\/(\S+)/.exec(La()))return a[1]+"."+a[2]}else if(ib)return(a=zh(/Android\s+([0-9.]+)/))?a:zh(/Version\/([0-9.]+)/);return""})();/*

 SPDX-License-Identifier: Apache-2.0
*/
function Ah(a,b,c,d,e){Q.call(this);this.i=oe();e||(e=Ja||(Ja=new Yd));this.l=e;this.j=af("docs.offline.iframeapi.IframeClientImpl");this.m=new V(this);R(this,this.m);this.C=a;this.H=d;a=new gh(b);a.h.set("ouid",this.C);e=new gh(v.location.href);"true"!=e.h.get("Debug")&&"true"!=e.h.get("debug")&&"pretty"!=e.h.get("debug")&&"DU"!=e.h.get("jsmode")||a.h.set("Debug","true");a.o="cd="+c;if(/^[\s\xa0]*$/.test(a.j))throw Error("Url contains invalid domain: "+b);this.F=jh(ih(hh(new gh,a.l),a.j),a.m).toString();
b=ff(a.toString(),"sa",""+d);this.G=Sd(null===b?"null":void 0===b?"undefined":b);this.h=null;this.s=!1}q(Ah,Q);function hg(a){a.m.listen(Vd(a.l.h),"message",a.D);Bh(a);Ee().then(function(){a.s||(Ch(a),Dh(a),a.i.reject(Error("Iframe initialization timed out")))});return a.i.promise.then()}Ah.prototype.reset=function(){Dh(this);this.i=oe()};function yg(a){var b=Y(12);return Z(a,b).then(function(){})}
function ig(a){var b=Y(1);return Z(a,b).then(function(c){c=new Pf(new Mg(c.port),"user_"+a.C);R(a,c);return c})}function Kg(a,b){var c=Y(2);b=Pg(new Ng,b);Pb(c,2,b);return Z(a,c).then(function(d){return Ob(L(d.h,Yg,3),If,1)})}function Eh(a){var b=Y(3);return Z(a,b).then(function(c){return Ob(L(c.h,Yg,3),If,1)})}function pg(a,b){var c=Y(4);return Z(a,c,b).then(function(d){return L(L(d.h,dh,4),Zf,1)})}function ug(a){var b=Y(10);return Z(a,b).then(function(c){return L(L(c.h,ch,7),Ef,1)})}
function wg(a,b,c){var d=void 0===d?!1:d;var e=Y(5);b=Wg(Vg(Ug(new Sg,b),c),d);Pb(e,3,b);return Z(a,e).then(function(f){return(f=L(f.h,eh,8))&&L(f,Yf,1)||null})}function mg(a,b){var c=Y(6);b=Rg(new Qg,b);Pb(c,4,b);return Z(a,c).then(function(d){return L(L(d.h,$g,5),Ef,1)})}function qg(a){var b=Y(9);return Z(a,b).then(function(){})}function xg(a){var b=Y(11);return Z(a,b).then(function(){})}function Fh(a){var b=Y(13);return Z(a,b).then(function(c){c=L(c.h,bh,9);return I(c,1)})}
function Z(a,b,c){c=void 0===c?3E4:c;return U(a.i.promise.then(function(d){X(a.j,"Sending message to the docs offline iframe api. Type:"+I(b,1));var e=new MessageChannel;d.postMessage(Fb(b.A,Gb,Hb),[e.port2]);var f=oe();e.port1.onmessage=function(h){h=new Gh(h);h.h&&L(h.h,ah,2)?(h=L(h.h,ah,2),f.reject(Error("Iframe api request of type "+I(b,1)+" failed (error type "+I(h,1)+"): "+I(h,2)))):f.resolve(h)};var g=De(function(){f.reject(Error("Iframe api request of type "+I(b,1)+" timed out."))},c);re(f.promise,
function(){e.port1.close();v.clearTimeout(g)});return f.promise}),function(d){throw $c(d,{iframeRequest_docsExtensionManifestVersion:String(v._docs_chrome_extension_manifest_version||2),iframeRequest_sourceApplication:a.H.toString(),iframeRequest_requestType:I(b,1).toString()});})}
Ah.prototype.D=function(a){(a=a.i)&&a.origin==this.F&&a.source==this.h.contentWindow&&(this.s?W(this.j,"Docs offline iframe already connected to this client."):(a.ports&&a.ports.length&&a.ports[0]?(a=a.ports[0],Tc(this,a.close,a),this.i.resolve(a),this.s=!0,X(this.j,"Docs Offline iframe connected to this client.")):W(this.j,"Docs offline frame api sent a message with no port."),Ch(this)))};function Ch(a){Ie(a.m,Vd(a.l.h),"message",a.D)}
function Bh(a){a.h=a.l.i("IFRAME",{style:"display:none"});a.h.src=Rd(a.G).toString();a.l.h.body.appendChild(a.h)}function Dh(a){if(a.h){var b=a.h;b&&b.parentNode&&b.parentNode.removeChild(b);a.h=null}}Ah.prototype.B=function(){Dh(this);Q.prototype.B.call(this)};function Gh(a){this.h=a.data?new fh(a.data):null;this.port=a.ports&&a.ports.length&&a.ports[0]?a.ports[0]:null};function Hh(a,b,c,d,e){b=new Ah(a,Rd(b).toString(),c,d);c=new Ff;cg.call(this,b,c,a,!1,!1,!0,e,void 0,!0);R(this,b);R(this,c);this.aa=new Ig(c,b);R(this,this.aa)}q(Hh,cg);Hh.prototype.ia=function(){return Fh(this.h)};var Ih=af("switchblade.offline.client"),Jh=new Md(Nd,"https://docs.google.com/offline/iframeapi"),Kh=Sd(Jh instanceof Md&&Jh.constructor===Md&&Jh.i===Od?Jh.h:"type_error:Const"),Lh=new Map([["application/vnd.google-apps.document","kix"],["application/vnd.google-apps.drawing","drawing"],["application/vnd.google-apps.presentation","punch"],["application/vnd.google-apps.spreadsheet","ritz"]]);function Mh(){var a=void 0===a?Kh:a;this.i=this.h=null;this.j=a}
Mh.prototype.onMessage=function(a,b){var c=this,d,e,f,g,h;return Aa(function(k){switch(k.h){case 1:try{d=new nc(a)}catch(l){return b.disconnect(),k.return()}e=I(d,2);if(!e)return b.disconnect(),k.return();k.i=2;return r(k,Nh(c,e),4);case 4:ra(k,3);break;case 2:return f=sa(k),W(Ih,"Failed to connect to offline service: "+f),b.disconnect(),k.return();case 3:return k.i=5,r(k,Oh(c,d),7);case 7:g=k.o;J(g,2,e);null!=I(d,1)&&J(g,1,I(d,1));b.postMessage(Fb(g.A,Gb,Hb));ra(k,0);break;case 5:h=sa(k),W(Ih,"Failed to handle message: "+
h),b.disconnect(),k.h=0}})};function Nh(a,b){var c,d,e;return Aa(function(f){switch(f.h){case 1:if(a.h)return f.return();a.i=new lf("https://ssl.gstatic.com/docs/common/netcheck.gif",!0);a.h=new Hh(b,a.j,4,11,a.i);f.i=2;return r(f,a.h.start(),4);case 4:ra(f,0);break;case 2:c=sa(f);W(Ih,"DocsOffline initialization error: "+c);d=c.xa;if(!d)throw c;f.i=5;return r(f,d,7);case 7:ra(f,0);break;case 5:throw e=sa(f),e;}})}
function Oh(a,b){var c,d,e;return Aa(function(f){if(1==f.h){if(7===Mb(b,Rb))return f.return(Ph(a,b));c=a.h.i;1==c.h?(f.h=2,f=void 0):f=Hf(c)?f.return(Promise.resolve(oc(23))):r(f,ug(a.h.h),3);return f}if(2!=f.h)switch(d=f.o,e=I(d,1),e){case 1:case 2:case 6:return f.return(Promise.reject(Error("Invalid eligibility reason: "+e)));case 0:return f.return(Promise.resolve(oc(22)));case 3:return f.return(Promise.resolve(oc(100)));case 4:return f.return(Promise.resolve(oc(17)));case 5:return f.return(Promise.resolve(oc(36)));
case 7:return f.return(Promise.resolve(oc(38)))}switch(Mb(b,Rb)){case 4:return f.return(Qh(a,b));case 6:return f.return(Rh(a,b));default:return f.return(Promise.reject(Error("Unhandled message case: "+Mb(b,Rb))))}})}
function Sh(a,b){var c,d;return Aa(function(e){switch(e.h){case 1:return e.i=2,0<b.length?r(e,Kg(a.h.h,b),7):r(e,Eh(a.h.h),6);case 6:c=e.o;e.h=5;break;case 7:c=e.o;case 5:ra(e,3);break;case 2:return sa(e),e.return(Promise.resolve(pc(new nc,lc(new kc,!1))));case 3:return d=[],c.forEach(function(f){var g=Lb(f,7)?2:3;if(0<b.length||2===g){var h=d,k=h.push,l=new ac;g=J(l,2,g);f=I(f,1);f=J(g,1,null==f?void 0:f);k.call(h,f)}}),e.return(Promise.resolve(pc(new nc,lc(mc(new kc,jc(new hc,d)),!0))))}})}
function Ph(a,b){var c,d,e,f;return Aa(function(g){switch(g.h){case 1:c=new cc;d=a.h.i;if(!Lb(Tb(b,bc,7),2)){c.setEnabled(1==d.h);J(c,3,1);g.h=2;break}if(1==d.h){c.setEnabled(!0);J(c,3,3);J(c,4,2);g.h=2;break}if(!Hf(d)){c.setEnabled(!1);if(2==d.h){J(c,3,2);var h=I(d.j,1);J(c,4,null==h?void 0:h)}else J(c,3,0);g.h=2;break}g.i=5;return r(g,lg(a.h),7);case 7:e=g.o;f=I(e,1);1===f?c.setEnabled(!0):(2===f?(c.setEnabled(!0),J(c,3,3)):(c.setEnabled(!1),J(c,3,2)),J(c,4,f));ra(g,2);break;case 5:sa(g),c.setEnabled(1==
d.h),J(c,3,0);case 2:h=g.return;var k=Promise,l=k.resolve;var n=new nc;n=Qb(n,8,c);return h.call(g,l.call(k,n))}})}
function Qh(a,b){var c;return Aa(function(d){c=Tb(b,fc,4);var e=d.return,f=H(c.A),g=Kb(c,3,1,!1,f),h=F(g);if(!(h&4)){Object.isFrozen(g)&&(g=tb(g.slice()),K(c,3,g,!1));for(var k=0,l=0;k<g.length;k++){var n=g[k];null!=n&&(g[l++]=n)}l<k&&(g.length=l);E(g,5);f&&(E(g,2),Object.freeze(g))}!f&&(h&2||Object.isFrozen(g))&&(g=Array.prototype.slice.call(g),E(g,5),f=g,f=null==f?yb:sb(f,1),J(c,3,f,!1));return e.call(d,Sh(a,g))})}
function Rh(a,b){var c,d,e,f,g,h,k,l,n,y,A;return Aa(function(x){switch(x.h){case 1:c=[];d=[];e=[];f=fa(Ob(Tb(b,dc,6),ac,3));for(g=f.next();!g.done;g=f.next()){h=g.value;k=I(h,1);l=I(h,2);var C=I(h,4);n=C?Lh.has(C)?Lh.get(C):"unknown":"unknown";if(2===l){C=d;var ja=C.push;var wa=Kf(k);wa=J(wa,2,n);ja.call(C,wa)}else 3===l&&(C=e,ja=C.push,wa=Kf(k),wa=J(wa,2,n),ja.call(C,wa));c.push(k)}y=[];0<d.length&&y.push(vg(a.h,d,!0));0<e.length&&y.push(vg(a.h,e,!1));x.i=2;return r(x,Promise.all(y),4);case 4:ra(x,
3);break;case 2:return A=sa(x),W(Ih,"Failed to change pin state. "+A),x.return(Promise.resolve(pc(new nc,lc(new kc,!1))));case 3:return x.return(Sh(a,c))}})};var Th=af("switchblade.offline.offscreen");function Uh(a){if("com.google.drive.offscreenproxy"===a.name){a.onDisconnect.addListener(function(){cf(Th,"Extension message port disconnected: "+(chrome.runtime.lastError.message||""))});var b=new Mh;a.onMessage.addListener(function(c){cf(Th,"Offscreen request: "+JSON.stringify(c));b.onMessage(c,a)})}};function Vh(){this.h=Date.now()}var Wh=null;Vh.prototype.set=function(a){this.h=a};Vh.prototype.reset=function(){this.set(Date.now())};Vh.prototype.get=function(){return this.h};function Xh(a){this.l=a||"";Wh||(Wh=new Vh);this.o=Wh}Xh.prototype.h=!0;Xh.prototype.i=!0;Xh.prototype.j=!1;function Yh(a){return 10>a?"0"+a:String(a)}function Zh(a){Xh.call(this,a)}z(Zh,Xh);
function $h(a,b){var c=[];c.push(a.l," ");if(a.i){var d=new Date(b.j);c.push("[",Yh(d.getFullYear()-2E3)+Yh(d.getMonth()+1)+Yh(d.getDate())+" "+Yh(d.getHours())+":"+Yh(d.getMinutes())+":"+Yh(d.getSeconds())+"."+Yh(Math.floor(d.getMilliseconds()/10)),"] ")}d=c.push;var e=a.o.get();e=(b.j-e)/1E3;var f=e.toFixed(3),g=0;if(1>e)g=2;else for(;100>e;)g++,e*=10;for(;0<g--;)f=" "+f;d.call(c,"[",f,"s] ");c.push("[",b.i,"] ");c.push(b.getMessage());a.j&&(b=b.h,void 0!==b&&c.push("\n",b instanceof Error?b.message:
String(b)));a.h&&c.push("\n");return c.join("")};function ai(){this.o=w(this.j,this);this.h=new Zh;this.h.i=!1;this.h.j=!1;this.i=this.h.h=!1;this.l={}}ai.prototype.j=function(a){function b(f){if(f){if(f.value>=Me.value)return"error";if(f.value>=Ne.value)return"warn";if(f.value>=Pe.value)return"log"}return"debug"}if(!this.l[a.i]){var c=$h(this.h,a),d=bi;if(d){var e=b(a.l);ci(d,e,c,a.h)}}};var bi=v.console;function ci(a,b,c,d){if(a[b])a[b](c,void 0===d?"":d);else a.log(c,void 0===d?"":d)};var di=af("switchblade.offline");di&&(Ze($e(),di.h()).h=Oe);var ei=new ai;if(1!=ei.i){var fi=Ze($e(),"").l,gi=ei.o;fi&&Ze($e(),fi.h()).j.push(gi);ei.i=!0}chrome.runtime.onConnect.addListener(function(a){Uh(a)});
