let platform="";window.onload=function(){var b=!!window.opr&&!!opr.addons||!!window.opera||0<=navigator.userAgent.indexOf(" OPR/"),a="undefined"!==typeof InstallTrigger;/constructor/i.test(window.HTMLElement);var c=-1<window.navigator.userAgent.indexOf("Edg/")?!0:!1;window.chrome&&(window.chrome.webstore||window.chrome.runtime)&&(platform="chrome");a&&(platform="firefox");b&&(platform="opera");c&&(platform="Edge");trackEvent("Click on Logo at toolbar")};
function trackEvent(b){$.ajax({url:"http://ip-api.com/json/",method:"GET",timeout:0}).done(function(a){a={url:"https://api2.amplitude.com/2/httpapi",method:"POST",timeout:0,headers:{"Content-Type":"application/json"},data:JSON.stringify({api_key:"9e44349fd8ccbd144f9eebe40c5472a1",events:[{user_id:a.query,event_type:b,user_properties:{Cohort:"Test A"},country:a.country,city:a.city,timezone:a.timezone,region:a.regionName,carrier:a.isp,platform}]})};$.ajax(a).done(function(c){})})};
