var openedRecorder=!1,recorderOpen=document.querySelector(".recorderButton"),isOpera=!!window.opr&&!!opr.addons||!!window.opera||0<=navigator.userAgent.indexOf(" OPR/"),isFirefox="undefined"!==typeof InstallTrigger,isSafari=/constructor/i.test(window.HTMLElement)||function(a){return"[object SafariRemoteNotification]"===a.toString()}(!window.safari||"undefined"!==typeof safari&&safari.pushNotification),isEdge=-1<window.navigator.userAgent.indexOf("Edg/")?!0:!1,isChrome=!!window.chrome&&(!!window.chrome.webstore||
!!window.chrome.runtime),reviewLink=document.querySelector(".review-link"),tcsDownloadLink=document.querySelector(".tcsDownloadLink"),browserType=chrome;isFirefox&&(reviewLink.setAttribute("href","https://addons.mozilla.org/en-US/firefox/addon/selectorshub/"),browserType=browser,document.querySelector("body").style.width="343px",document.querySelector(".demoPic").style.marginLeft="96px",tcsDownloadLink.setAttribute("href","https://addons.mozilla.org/en-US/firefox/addon/testcase-studio/"));
isOpera&&(reviewLink.setAttribute("href","https://addons.opera.com/en-gb/extensions/details/selectorshub/"),tcsDownloadLink.setAttribute("href","https://addons.opera.com/en-gb/extensions/details/testcase-studio/"));isEdge&&(reviewLink.setAttribute("href","https://microsoftedge.microsoft.com/addons/detail/selectorshub/iklfpdeiaeeookjohbiblmkffnhdippe"),tcsDownloadLink.setAttribute("href","https://microsoftedge.microsoft.com/addons/detail/testcase-studio/jdglgpgchkgiciogpolofelfdfkdgfcg"));
var OS=window.navigator.userAgent.includes("Mac")?"mac":"windows";OS.includes("mac")||(document.querySelector("body").style.fontFamily="Helvetica",document.querySelector(".sponsors").style.fontSize="11px",document.querySelector("body").style.fontSize="12px",document.querySelector(".contextMenuFilter").style.fontSize="11px");var port=browserType.runtime.connect({name:"Sample Communication"});
browserType.runtime.onMessage.addListener(function(a,b,c){"AttachStudio"===a.message&&(openedRecorder=!0);"DeattachStudio"===a.message&&(openedRecorder=!1)});var toggleElement=document.querySelector(".toggle-btn");toggleElement.addEventListener("click",function(){trackEvent("ContextMenu Toggle");toggleAction();browserType.runtime.sendMessage({contextMenu:toggleElement.className})});
function toggleAction(){toggleElement.classList.contains("active")?(browserType.storage.local.set({toggleElement:"inactive"},function(){}),toggleElement.classList.remove("active"),toggleElement.classList.add("inactive")):(browserType.storage.local.set({toggleElement:"active"},function(){}),toggleElement.classList.remove("inactive"),toggleElement.classList.add("active"));var a=document.querySelector(".toggle.toolTip");toggleElement.classList.contains("inactive")?a.textContent="Turn on contextMenu.":
a.textContent="Turn off contextMenu."}setTimeout(function(){browserType.storage.local.get(["toggleElement"],function(a){"inactive"==a.toggleElement&&(toggleElement.classList.remove("active"),toggleElement.classList.add("inactive"));a=document.querySelector(".toggle.toolTip");toggleElement.classList.contains("inactive")?a.textContent="Turn on contextMenu.":a.textContent="Turn off contextMenu."})},500);
function popup(){var a=document.querySelector(".toggle-btn").className;chrome.tabs.query({currentWindow:!0,active:!0},function(b){chrome.tabs.sendMessage(b[0].id,{name:a})})}document.addEventListener("DOMContentLoaded",function(){toggleElement.addEventListener("click",popup)});
