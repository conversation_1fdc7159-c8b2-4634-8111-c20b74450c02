#!/usr/bin/env python3
"""
Test script to verify dynamic followers extraction (no fixed limits)
"""

import os
import time
from datetime import datetime

def test_dynamic_extraction():
    """Test that extraction works dynamically without fixed limits"""
    
    print("🧪 Testing Dynamic Followers Extraction")
    print("=" * 50)
    print("This test verifies that the code:")
    print("✅ Extracts ALL available followers (no fixed limit)")
    print("✅ Stops automatically when no more followers found")
    print("✅ Handles accounts of any size dynamically")
    print("=" * 50)
    
    # Monitor extraction in real-time
    if not os.path.exists("followers_data.txt"):
        print("⏳ Waiting for extraction to start...")
        print("💡 Run 'streamlit run instasniper.py' in another terminal")
        
        # Wait for file to be created
        while not os.path.exists("followers_data.txt"):
            time.sleep(2)
            print(".", end="", flush=True)
        print("\n✅ Extraction started!")
    
    print("\n📊 Monitoring extraction progress...")
    print("Time\t\tFollowers\tRate\t\tStatus")
    print("-" * 60)
    
    last_count = 0
    stable_count = 0
    start_time = time.time()
    last_update_time = start_time
    
    while True:
        try:
            with open("followers_data.txt", "r", encoding='utf-8') as f:
                lines = f.readlines()
                current_count = len([line.strip() for line in lines if line.strip()])
            
            current_time = time.time()
            elapsed = current_time - start_time
            
            if current_count != last_count:
                # New followers found
                time_since_last = current_time - last_update_time
                rate = (current_count - last_count) / time_since_last if time_since_last > 0 else 0
                
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"{timestamp}\t{current_count}\t\t{rate:.1f}/s\t\tExtracting...")
                
                last_count = current_count
                last_update_time = current_time
                stable_count = 0
            else:
                # No new followers
                stable_count += 1
                
                if stable_count == 6:  # 30 seconds of no change
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    print(f"{timestamp}\t{current_count}\t\t0.0/s\t\tStable (30s)")
                elif stable_count == 12:  # 60 seconds of no change
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    print(f"{timestamp}\t{current_count}\t\t0.0/s\t\tLikely finished")
                elif stable_count >= 18:  # 90 seconds of no change
                    print("\n🎉 EXTRACTION APPEARS TO BE COMPLETE!")
                    print(f"📊 Final count: {current_count} followers")
                    print(f"⏱️  Total time: {elapsed:.1f} seconds")
                    print(f"🚀 Average rate: {current_count/elapsed:.2f} followers/second")
                    break
            
            time.sleep(5)  # Check every 5 seconds
            
        except KeyboardInterrupt:
            print(f"\n👋 Monitoring stopped by user")
            print(f"📊 Last count: {current_count} followers")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(5)
    
    # Final analysis
    print("\n" + "=" * 50)
    print("📈 FINAL ANALYSIS")
    print("=" * 50)
    
    try:
        with open("followers_data.txt", "r", encoding='utf-8') as f:
            lines = f.readlines()
        
        followers = [line.strip() for line in lines if line.strip()]
        unique_followers = list(set(followers))
        
        print(f"📊 Total followers extracted: {len(unique_followers)}")
        print(f"🔄 Duplicates removed: {len(followers) - len(unique_followers)}")
        print(f"📁 File size: {os.path.getsize('followers_data.txt')} bytes")
        
        # Categorize by size
        if len(unique_followers) >= 50000:
            print("🏆 MASSIVE ACCOUNT: 50,000+ followers!")
        elif len(unique_followers) >= 10000:
            print("🎉 LARGE ACCOUNT: 10,000+ followers!")
        elif len(unique_followers) >= 5000:
            print("🚀 MEDIUM-LARGE ACCOUNT: 5,000+ followers!")
        elif len(unique_followers) >= 1000:
            print("✅ MEDIUM ACCOUNT: 1,000+ followers!")
        elif len(unique_followers) >= 100:
            print("📈 SMALL-MEDIUM ACCOUNT: 100+ followers!")
        else:
            print("📊 SMALL ACCOUNT: Under 100 followers")
        
        print(f"\n✅ SUCCESS: Code worked dynamically without fixed limits!")
        print(f"✅ SUCCESS: Extracted all available followers!")
        
    except Exception as e:
        print(f"❌ Error in final analysis: {e}")

if __name__ == "__main__":
    test_dynamic_extraction()
