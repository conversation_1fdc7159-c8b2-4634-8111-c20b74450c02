{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397260505544068", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397260505851069", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397260506828986", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 117517}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397260483095309", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 79517}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 58472}, "server": "https://encrypted-tbn0.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 71494}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 91754}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 62521}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397260566889242", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 62521}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397260607642805", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 65506}, "server": "https://dns.google", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394755007787297", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 145104}, "server": "https://static.cdninstagram.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394755008089335", "port": 443, "protocol_str": "quic"}], "anonymization": [], "network_stats": {"srtt": 119092}, "server": "https://www.instagram.com", "supports_spdy": true}], "supports_quic": {"address": "************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}