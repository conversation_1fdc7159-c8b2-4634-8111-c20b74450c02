#!/usr/bin/env python3
"""
Test script to verify followers extraction improvements
"""

import os
import time

def monitor_followers_file():
    """Monitor the followers_data.txt file for changes"""
    
    print("🔍 Monitoring followers extraction...")
    print("This script will monitor followers_data.txt and show progress")
    print("Run 'streamlit run instasniper.py' in another terminal")
    print("-" * 60)
    
    last_count = 0
    start_time = time.time()
    
    while True:
        try:
            if os.path.exists("followers_data.txt"):
                with open("followers_data.txt", "r", encoding='utf-8') as f:
                    lines = f.readlines()
                    current_count = len([line.strip() for line in lines if line.strip()])
                
                if current_count != last_count:
                    elapsed = time.time() - start_time
                    rate = current_count / elapsed if elapsed > 0 else 0
                    
                    print(f"📊 Followers extracted: {current_count}")
                    print(f"⏱️  Time elapsed: {elapsed:.1f}s")
                    print(f"🚀 Rate: {rate:.2f} followers/second")
                    print(f"📈 Progress: {current_count}/1419 ({current_count/1419*100:.1f}%)")
                    
                    if current_count >= 1419:
                        print("🎉 All followers extracted!")
                        break
                    elif current_count >= 1000:
                        print("✅ Reached 1000 followers (free version limit)")
                    
                    print("-" * 40)
                    last_count = current_count
            else:
                print("⏳ Waiting for followers_data.txt to be created...")
            
            time.sleep(5)  # Check every 5 seconds
            
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(5)

def analyze_current_file():
    """Analyze the current followers_data.txt file"""
    
    if not os.path.exists("followers_data.txt"):
        print("❌ followers_data.txt not found")
        return
    
    print("📊 Analyzing current followers_data.txt...")
    
    try:
        with open("followers_data.txt", "r", encoding='utf-8') as f:
            lines = f.readlines()
        
        # Clean and analyze data
        followers = [line.strip() for line in lines if line.strip()]
        unique_followers = list(set(followers))
        
        print(f"📈 Total lines: {len(lines)}")
        print(f"👥 Valid followers: {len(followers)}")
        print(f"🔄 Unique followers: {len(unique_followers)}")
        print(f"📊 Duplicates: {len(followers) - len(unique_followers)}")
        
        if len(followers) > 0:
            print(f"📝 Sample followers:")
            for i, follower in enumerate(followers[:10]):
                print(f"   {i+1}. {follower}")
            
            if len(followers) > 10:
                print(f"   ... and {len(followers) - 10} more")
        
        # Progress towards target
        target = 1419
        progress = len(unique_followers) / target * 100
        print(f"🎯 Progress: {len(unique_followers)}/{target} ({progress:.1f}%)")
        
        if len(unique_followers) < 50:
            print("⚠️  Very few followers extracted. Possible issues:")
            print("   - Scrolling not working properly")
            print("   - XPath selectors need adjustment")
            print("   - Account might be private")
            print("   - Rate limiting by Instagram")
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

if __name__ == "__main__":
    print("🧪 Followers Extraction Test Tool")
    print("1. Monitor live extraction")
    print("2. Analyze current file")
    
    choice = input("\nChoose option (1 or 2): ").strip()
    
    if choice == "1":
        monitor_followers_file()
    elif choice == "2":
        analyze_current_file()
    else:
        print("Invalid choice. Analyzing current file...")
        analyze_current_file()
