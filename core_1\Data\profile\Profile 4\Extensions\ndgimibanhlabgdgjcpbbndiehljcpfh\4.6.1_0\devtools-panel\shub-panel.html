<html>
<head>
    <title>SelectorsHub</title>
    <meta http-equiv="content-type" content="text/plain; charset=UTF-8"/>
	<style rel="stylesheet" id="animation-style"></style>
    <link rel="stylesheet" id="cssFile" type="text/css" href="lightTheme.css" /> 
    <script type="text/javascript" src="../extension/jquery.js"></script>
</head>
<body>
    <!-- <form role="form" id="register_form">
        <h2 id="form_title">Please Register To Continue</h2>
        <input type="email" name="email" id="email" placeholder="enter your email">
        <input type="text" name="company" placeholder="company" id="company">
        <input type="password" name="password" id="password" placeholder="enter your password">
        <input type="submit" name="register" id="submit_btn" value="Register">
        <br>
        <div id="registration_footer">
            <a id="registration_help" target="_blank" href="https://bit.ly/SH_Slack" title="Connect here for immediate support.">Need Help</a>
            <span> | </span>
            <a id="registration_tutorial" target="_blank" href="https://youtu.be/a3PzXjvYR28" title="Click to learn how to use SelectorsHub.">
                Tutorial
            </a>
        </div>
        <div class="user_target">Help us to achieve <a class="target_link" href="https://bit.ly/chrome_shub" target="_blank">200K users</a> or <a class="target_link" href="https://bit.ly/SH_Patron" target="_blank">100 Patrons</a>  & we will remove this registration.</div>
    </form> -->
	<div class="container">
		
	
	<div class="editorHeader">
		<div class="header1">
			<span class="editorContent" title="Click to see all saved selector's value.">
				<button class="edit-icon"></button>
				<span class="editorTitle">XPath/cssSel..</span>
				<!-- <span class="savedValues toolTip">Click to access saved selector's values.</span> -->
			</span>
			<ul class='savedSelectors'>
				<li class="savedSelectorRow delete">
					<span>No value is saved.</span>
					<button class="deleteAllSaved" title="Click to delete all saved values.">
				    </button>
				</li>
			</ul>
			<div class="selector-editor-div">
				<!-- <input type="search" class="selectors-input jsSelector"  value="" autocorrect="off" autocapitalize="off" placeholder="Type // for xpath and for cssSelector type # or . or tagname......Click on ➕ icon to save value ➡️" autocomplete="off"  spellcheck="false"/> -->
				<input id="searchbox1" type="search" name="selector-search-box" class="selectors-input jsSelector"  value="" autocorrect="off" autocapitalize="off" placeholder="Write & verify XPath & CSS Selector here......Click on ➕ icon to save value ➡️" autocomplete="off"  spellcheck="false"/>
			</div>
			<button class="plus-btn">
			    <span class="plus toolTip">Save selector in list.</span>
			</button>		
	    </div>
	    <div class="configContainer">
	    	<button class="axes-btn inactive">Axes
	    		<span class="axes toolTip">Click to generate Axes based XPath.</span>
	    	</button>
	    	<!-- <a class="donationLink" target="_blank" href="http://bit.ly/sh_donate">
	    		<span class="donation toolTip">Please donate & support SelectorsHub.</span>
	    		<button class="donation-btn"></button>
		    </a> -->
  			
		    <div class="autosuggest-toggle-btn active" id="autosuggestToggle">
    			<div class="autosuggest-toggle-circle"></div>
    			<span class="autosuggest-toggle toolTip">Turn off auto suggestion while typing.</span>
  			</div>

			<div class="total-match-count jsTotalMatchCount hideMatchCountMsg"></div>
			<span class="errorInfo hideMatchCountMsg"></span>
			<a class="cheetSheetLink hideMatchCountMsg" target="_blank" href="https://bit.ly/sh_courses_recordings">
				<span class="cheetSheetBtn toolTip">To learn more about these concepts, take this advance course.</span>
				<button class="cheetSheet"></button>
			</a>
			<button class="ignoreCaseBtn">
		    	<span class="ignoreCase toolTip">Click to make Selectors Case Insensitive.</span>
		    </button>
		    <!-- <div class="toggle-btn inactive" data-placeholder="1.0"> -->
		    <div class="toggle-btn active">
    			<div class="toggle-circle"></div>
    			<span class="toggle toolTip">click to disable SelectorsHub</span>
  			</div>
	    </div>
	</div>
	<span class="copyToolTip">Selector value is copied in clipboard</span>
	
	<div class="shub-generator">
		<div class="driver-command-box">
		    <input id="searchbox2" type="search" class="driver-command" value='driver.findElement(By.xpath("xpathvalue"))' placeholder="Enter the driver command and keep xpathvalue as keyword in the command." autocomplete="off" autocorrect="off" spellcheck="false" />
		    <ul class='savedCommand'>
		    	<li class="savedCommandRow" title="Click to select this command">driver.findElement(By.xpath("xpathvalue"))</li>
		    	<li class="savedCommandRow" title="Click to select this command">cy.get("xpathvalue")</li>
		    	<li class="savedCommandRow" title="Click to select this command">@FindBy(xpath="xpathvalue") @CacheLookup private WebElement selectorname;</li>
		    	<li class="savedCommandRow" title="Click to select this command">driver.find_element(*(By.XPATH,"xpathvalue"))</li>
		    	<li class="savedCommandRow" title="Click to select this command">await page.locator('xpath=xpathvalue')</li>
		    	<li class="savedCommandRow" title="Click to select this command">FindByXpath("xpathvalue")</li>
			</ul>

		    <a href="http://bit.ly/SH_Code" target="_blank" title="click to watch detailed tutorial">
			    <button class="commandTip">
					<span class="questionMark toolTip">Replace xpath or cssSelector in command by xpathvalue keyword. For ex- FindBy(xpath="xpathvalue") or if xpath is not inside double quotes then FindBy(xpath=xpathValue).</span>
				</button>	
			</a>
	    </div>
		<div class="attributeFilter">
			<a class="upgradeBtn" target="_blank" href="https://bit.ly/shub_pro">
				<span class="upgrade toolTip">Click to take 1 month free trial of Pro version.</span>Upgrade</a>
			<input id="searchbox3" class="chooseAttr user" type="search" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" name="unique" placeholder="Enter attribute name here, not xpath">
			<label><input class="chooseAttr textXpath" type="checkbox" title="xpath with text or without text." checked>text</label>	
			<label><input class="chooseAttr id" type="checkbox" name="id" checked />id</label>
			<label><input class="chooseAttr class" type="checkbox" name="class" checked />class</label>
			<label><input class="chooseAttr name" type="checkbox" name="name" checked />name</label>
			<label class="placeCheck"><input class="chooseAttr placeholder" type="checkbox" name="placeholder" checked />placeholder</label>
			<a class="attrFilterTutorialLink" target="_blank" href="https://youtu.be/yti5o3ILxlU">
				<button class="attrFilterTutorialIcon">
					<span class="attrFilterTutorial toolTip">Click to learn how to use attribute filter feature.</span>
				</button>
		  	</a>
		</div>
		
		<div class="configOptions">
		    <button class="attrFilterBtn active">
	    	<span class="filter toolTip">Set attribute.</span>
		    </button>
		    <button class="addDriverCommand inactive">
		    	<span class="driverCommand toolTip">Set driver command to append on selectors.</span>
		    </button>
		    <button class="multiSelectorRecordBtn grey">
		    	<span class="capture toolTip">Click to generate Locators Page & Multiple Selectors.</span>
		    </button>
		    <button id='record' class="testCaseStudioBtn">
		    	<span class="tcStudio toolTip">Click to open TestCase Studio.</span>
			</button>	
			<button class="debugBtn inactive">
		    	<span class="debug toolTip">Turn on Debugger.</span>
		    </button>		
		    <button id='smartFix' class="smartFix">
		    	<span class="resetSmart toolTip">XPath Healing: Click to verify all the XPaths of script.</span>
			</button>	
			<button class="quotes-btn inactive">
		    	<span class="quotes toolTip">Click to generate selectors with double quote.</span>
		    </button>											
		    <button class="expand-btn inactive">
		    	<span class="expand toolTip">Click to expand selectors block to see all generated selectors.</span>
		    </button>
		    <button class="uiSetting-btn inactive">
		    	<span class="uiSetting toolTip">Click to customize UI.</span>
		    </button>
		    <button class="reset-btn">
		    	<span class="reset toolTip">Click to reset all setting to the default.</span>
		    </button>
		</div>

		<ul class='selectorsGenerator'>
			<li class="elementInfo">
				<span type="text" class="elementInfoMsg" data-command="xpath" title="click to Register for the Technical Boot Camp.">Alert: This element is not interactable through selenium(automation) as it is not visible in UI. Try any near by element.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a></span>
	    	</li>
			<li class="selectorsRow suggestedXpath">
				<div class="copyContainer suggestedCopyBtn" title="Click to copy suggested selector value">
					<button class="copy-btn box suggestedCopyBtn"></button>
					<mark class="noOfMatch suggestedXpath"></mark><span class="typeOfLocator box suggestedCopyBtn">SH Selector</span>
				</div>
			    <span type="text" class="box valueSelector suggestedXpath suggestedEditBtn" data-command="xpath" title="click to edit SH XPath."></span>
	    	</li>
			<li class="selectorsRow iframeXpath">
				<div class="copyContainer iframeCopyBtn" title="click to copy iframe XPath">
					<button class="copy-btn box iframeCopyBtn"></button>
					<mark class="noOfMatch iframeXpath"></mark><span  class="typeOfLocator box iframeCopyBtn">iframeXPath</span>
				</div>
			    <span type="text" class="box valueSelector iframeXpath iframeEditBtn" data-command="xpath" title="click to edit iframe XPath."></span>
		    </li>
			<li class="selectorsRow cssSelector">
				<div class="copyContainer cssCopyBtn" title="click to copy relative cssSelector">
					<button class="copy-btn box cssCopyBtn"></button>
					<mark class="noOfMatch cssSelector"></mark><span  class="typeOfLocator box cssCopyBtn" title="Click to copy cssSelector.">
				    Rel cssSelector</span>
			    </div>
			    <span type="text" class="box valueSelector css cssEditBtn" data-command="cssSelector" title="Click to edit cssSelector."></span>
		    </li>
			<li class="selectorsRow relXpath">
				<div class="copyContainer relXpathCopyBtn" title="click to copy relative XPath">
					<button class="copy-btn box relXpathCopyBtn"></button>
					<mark class="noOfMatch relXpath"></mark><span  class="typeOfLocator box relXpathCopyBtn">Rel XPath
						<button class="warningIcon" title="">
						<span class="alert toolTip"></span>
					</button></span>
				</div>
			    <span type="text" class="box valueSelector rel relXpathEditBtn" data-command="xpath" title="Click to edit Rel XPath."></span>
			    
		    </li>
		    <li class="selectorsRow indexXpath">
				<div class="copyContainer indexXpathCopyBtn" title="click to copy index XPath">
					<button class="copy-btn box indexXpathCopyBtn"></button>
					<mark class="noOfMatch indexXpath"></mark><span  class="typeOfLocator box indexXpathCopyBtn">index XPath</span>
				</div>
			    <span type="text" class="box valueSelector indexXpath indexXpathEditBtn" data-command="indexXpath" title="Click to edit indexXpath."></span>
	    	</li>
	    	<li class="selectorsRow testRigorPath">
				<div class="copyContainer testRigorPathCopyBtn" title="click to copy testRigor Path">
					<button class="copy-btn box testRigorPathCopyBtn"></button>
					<!-- <mark class="noOfMatch testRigorPath"></mark> -->
					<a class="testRigorTutorialLink" target="_blank" href="https://bit.ly/testrigorpath_tutorial">
						<button class="testRigorTutorialIcon">
							<span class="testRigorTutorial toolTip">Click to learn what is testRigor Path and how to use it in automation.</span>
						</button>
				  	</a>
					<span  class="typeOfLocator box testRigorPathCopyBtn">testRigor Path</span>
					
				</div>
			    <span type="text" class="box valueSelector testRigorPath testRigorPathEditBtn" data-command="testRigorPath" title="Click to edit testRigorPath."></span>
	    	</li>
	    	<li class="selectorsRow jQuery">
		     	<div class="copyContainer jQueryCopyBtn" title="click to copy jQuery">
			     	<button class="copy-btn box jQueryCopyBtn"></button>
					<mark class="noOfMatch jQuery"></mark><span  class="typeOfLocator box jQueryCopyBtn">jQuery</span>
				</div>
			    <span type="text" class="box valueSelector jQuery jQueryEditBtn" data-command="jQuery" title="Click to edit jQuery."></span>
		    </li>
		     <li class="selectorsRow jsPath">
		     	<div class="copyContainer jsPathCopyBtn" title="click to copy jsPath">
			     	<button class="copy-btn box jsPathCopyBtn"></button>
					<mark class="noOfMatch jsPath"></mark><span  class="typeOfLocator box jsPathCopyBtn">JS Path</span>
				</div>
			    <span type="text" class="box valueSelector jsPath jsPathEditBtn" data-command="jsPath" title="Click to edit JS Path."></span>
		    </li>
			<li class="selectorsRow id">
				<div class="copyContainer idCopyBtn" title="click to copy id">
					<button class="copy-btn box idCopyBtn"></button>
					<mark class="noOfMatch id"></mark><span  class="typeOfLocator box idCopyBtn">id</span>
				</div>
			    <span type="text" class="box valueSelector id idEditBtn" data-command="id" title="Click to edit id."></span>
	    	</li>
	    	<li class="selectorsRow name">
	    		<div class="copyContainer nameCopyBtn" title="click to copy name">
		    		<button class="copy-btn box nameCopyBtn" title="click to copy name"></button>
					<mark class="noOfMatch name"></mark><span class="typeOfLocator box nameCopyBtn">name</span>
				</div>
			    <span type="text" class="box valueSelector name nameEditBtn" data-command="name" title="Click to edit name."></span>
	    	</li>
	    	<li class="selectorsRow className">
	    		<div class="copyContainer classNameCopyBtn" title="click to copy className">
		    		<button class="copy-btn box classNameCopyBtn"></button>
					<mark class="noOfMatch className"></mark><span class="typeOfLocator box classNameCopyBtn">className</span>
				</div>
			    <span type="text" class="box valueSelector className classNameEditBtn" data-command="className" title="Click to copy className."></span>
	    	</li>
		    
	    	<li class="selectorsRow linkText">
	    		<div class="copyContainer linkTextCopyBtn" title="click to copy linkText">
		    		<button class="copy-btn box linkTextCopyBtn"></button>
					<mark class="noOfMatch linkText"></mark><span  class="typeOfLocator box linkTextCopyBtn">LinkText</span>
				</div>
			    <span type="text" class="box valueSelector linkText linkTextEditBtn" data-command="linkText" title="Click to edit linkText."></span>
	    	</li>
	    	<li class="selectorsRow partialLinkText">
	    		<div class="copyContainer partialLinkTextCopyBtn" title="click to copy partialLinkText">
		    		<button class="copy-btn box partialLinkTextCopyBtn"></button>
					<mark class="noOfMatch partialLinkText"></mark><span class="typeOfLocator box partialLinkTextCopyBtn">PLinkText</span>
				</div>
			    <span type="text" class="box valueSelector partialLinkText partialLinkTextEditBtn" data-command="partialLinkText" title="Click to edit partialLinkText."></span>
	    	</li>
	    	<li class="selectorsRow absXpath">
	    		<div class="copyContainer absCopyBtn" title="click to copy absXPath">
		    		<button class="copy-btn box absCopyBtn"></button>
					<mark class="noOfMatch absXpath"></mark><span  class="typeOfLocator box absCopyBtn">Abs XPath</span>
				</div>
			    <span type="text" class="box valueSelector abs absEditBtn" data-command="xpath" title="Click to edit absXpath."></span>
		    </li>
	    	<li class="selectorsRow tagName">
	    		<div class="copyContainer tagNameCopyBtn" title="click to copy absXPath">
		    		<button class="copy-btn box tagNameCopyBtn"></button>
				    <mark class="noOfMatch tagName"></mark><span  class="typeOfLocator box tagNameCopyBtn">tagName</span>
				</div>
			    <span type="text" class="box valueSelector tagName tagNameEditBtn" data-command="tagName" title="Click to edit tagName."></span>
	    	</li>  	
	    </ul>
	    
	    <ul class='axesXpathGenerator'>
	    	<li>
	    		<button class="parentElement active" title="use DevTools inspector to inspect">Inspect Parent</button>
	    		<button class="childElement inactive" title="use DevTools inspector to inspect">Inspect Child</button>
	    		<a class="axesTutorialLink" target="_blank" href="https://bit.ly/axesXpath">
					<button class="axesTutorialIcon">
						<span class="axesTutorial toolTip">Click to learn how to generate Axes XPath.</span>
					</button>
			  	</a>
	    	</li>
			
			<li class="axesXpath">
				<div class="axesContainer axesXpathCopyBtn" title="click to copy Axes XPath">
					<mark class="noOfAxesXpathMatch">0</mark>
					<span  class="axesXpathCopyBtn">Axes XPath</span>
					<button class="copy-btn axesXpathCopyBtn"></button>
				</div>
			    <!-- <span type="text" class="box valueSelector axesXpath axesXpathEditBtn" data-command="xpath" title="Click to edit axes XPath."></span> -->
			    
		    </li>
		    <li class="axesXpath">
				<div type="text" class="axesXpath axesXpathEditBtn" data-command="xpath" title="Click to edit axes XPath.">
				</div>			    
		    </li>
		    <li class="axesXpath">
				<div class="axesContainer lastAxesXpathCopyBtn" title="click to copy last axes XPath">
					<mark class="noOfLastAxesXpathMatch">0</mark>
					<span  class="lastAxesXpathCopyBtn">Last Axes XPath</span>
					<button class="copy-btn lastAxesXpathCopyBtn"></button>
				</div>
			    <!-- <span type="text" class="box valueSelector axesXpath axesXpathEditBtn" data-command="xpath" title="Click to edit axes XPath."></span> -->
			    
		    </li>
		    <li class="axesXpath">
				<div type="text" class="lastAxesXpath lastAxesXpathEditBtn" data-command="xpath" title="Click to edit last axes XPath."></div>			    
		    </li>
		</ul>
    </div>
	<div id="xpathValueAlertMsg"></div>

	<div id="multiSelectorContainer">
	  <table id="recordedSelectorTable" border="1">
	  	<thead>
		    <tr id="multiSelectorRow">
			    <th class="row-no-header">#</th>
			    <th class="selectorName-header">Selector Name</th>
			    <th class="selector-header xpath-header"><span id="selectorHeader">Command</span><span class="selectorsCount">(0)</span>
				    <button class="copyAllBtn xpath">
				      	<span class="copyAll toolTip">Click to copy all the XPaths.</span>
				    </button>
				    <button class="deleteAllBtn">
				     	<span class="deleteAll toolTip">Click to delete all the rows.</span>
				    </button>
				    <a class="tutorialVideoLink" target="_blank" href="https://youtu.be/gX-b8VRNt2U">
					      <button class="tutorialVideoIcon">
					      	<span class="codeTutorial toolTip">Watch tutorial.</span>
					      </button>
				  	</a>
			  	<!-- </th>
			    <th class="headerExportBtn"> -->
					<button class="importButton">
						<span class="uploadModalIcon toolTip">Click to upload downloaded XPath xls file</span>
					</button>
					<button class="exportButton">
						<span class="export toolTip">Click to export data into xls.</span>
					</button>
					<button id="changeIconButton" class="openSelectorModal">
						<span class="pasteModal toolTip">Click to paste all the XPaths</span>
					</button>
				</th>
				<th class="selector-header cssSelector-header"><span id="selectorHeader">cssSelector</span>
					<button class="copyAllBtn cssSelector">
				      	<span class="copyAll toolTip">Click to copy all the cssSelectors.</span>
				    </button>
				</th>
				<th class="selector-header"><span id="selectorHeader">id</span></th>
				<th class="selector-header"><span id="selectorHeader">name</span></th>
				<th class="selector-header"><span id="selectorHeader">class</span></th>
			    <th class="selector-header"><span id="selectorHeader">linkText</span></th>
			    <th class="selector-header"><span id="selectorHeader">partialLinkText</span></th>
			    <th class="selector-header"><span id="selectorHeader">tagName</span></th>
		    </tr>
		</thead>
		<tbody id="tableBody">
		    <tr style="display: none;">
		      <td id="s-no" ></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="labelData" class="label editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="xpath selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="cssSelector selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="id selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="name selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="class selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="linkText selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="partialLinkText selectorValue editableContent" contenteditable="true"></td>
		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="selectorData" class="tagName selectorValue editableContent" contenteditable="true"></td>

		      <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" id="actionBtn">
		      	<!-- <span class="copyToolTip" id="myPopup">copied</span> -->
		      	<button id="row-copy-btn" title="click to copy relative XPath"></button>
		      	<button id="row-edit-btn" title="click to edit relative XPath"></button>
		      	<button id="delButton" title="Click to delete the row."></button>
		      </td>
	    	</tr>
    	</tbody>
	  </table>
	  <!-- <div class="tablePlaceholder">No selector recorded yet.<br>Please inspect elements or click on DOM nodes to record selectors.</div> -->
	  <div class="tablePlaceholder">
	  	1. Inspect elements or click on DOM nodes one by one to record xpaths for many.<br>
	  	2. Turn on the driver command to generate xpath with command.<br>
	  	3. You can also set the attribute to generate xpath.<br>
	  	4. Click on Copy All button in header to copy all xpaths.<br>
	  	5. You can also export the recorded in xls by click on export icon.
	  	6. Watch the detailed tutorial <a href="http://bit.ly/SH_Code" target="_blank">here</a> how to generate code.
	  </div>

	  <!-- Add XPATH modal -->
	  <div id="addNewModal" class="modal addNewModal">
			<div class="vertical-alignment-helper">
				<div class="modal-dialog vertical-align-center">
					<div class="close">
						<span id="modalClose">&times;</span>
					</div>
					<div class="modal-contents">
						<div class="modal-header">
							<p class="modal-title" id="addNewTitle"></p>
						</div>
						<div class="modal-body">
							<div style="margin-top: 10px; display: flex; align-items: center;">
								<p class="new-title-label" id="addNewTitleLabel"></p>
								<div style="height: 40px; display: block; align-items: center;">
									<span style="font-size: 12px; font-family: sans-serif;">Pass XPath command as input</span>
									<div id='inputToggle' class="toggle-btn active" title="Turn On toggle to pass the XPath Command" >
										<div class="toggle-circle"></div>
									</div>
								</div>
							</div>
							<input type="text" placeholder="replace xpath by xpathvalue as keyword in the command." autocomplete="off" autocorrect="off" title='You can change this command, just replace xpath by xpathvalue keyword in the command. For ex- FindBy(xpath="//div") then enter command as FindBy(xpath="xpathvalue") or if xpath is not inside double quotes like FindBy(xpath=//div) then FindBy(xpath=xpathvalue)' spellcheck="false" value='driver.findElement(By.xpath("xpathvalue"))' class="enterLabels" id="customCommand">
							<p id="commandValidation" style="display: none;">Replace xpath by xpathvalue keyword in the command. For ex- FindBy(xpath="//div") then enter command as FindBy(xpath="xpathvalue") or if xpath is not inside double quotes like FindBy(xpath=//div) then FindBy(xpath=xpathvalue)</p>
							<!-- <textarea  placeholder="Enter Labels" rows="4" class="enterLabels" id="enterLabels" cols="20"></textarea> -->
							<textarea placeholder="Paste selectors page or complete script here" rows="4" class="enterNewTitle" id="enterNewTitle" cols="20"></textarea>
							<input style="display: none;" type="file" name="imgupload" accept=".xls, .xlsx, .csv" id="imgupload">
						</div>
						<div class="modal-footer">
							<button type="button" id="modal-cancel" class="btn-cancel" data-dismiss="modal">Cancel</button>
							<button type="button" id="btn-submit" class="btn-submit">Submit</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	 
	</div>
	
	<div class="infoPlaceholder">
		<div>
			How to use:
		  	1. Inspect element to get auto suggest while typing.
		  	2. It will show the auto suggest for inspected element while typing. 
		  	3. If auto suggest doesn't work while typing, delete the value & type again. 
		  	4. For more details, pls follow <a target="_blank" href="https://bit.ly/3gvXNuF">video tutorial</a> and checkout <a target="_blank" href="https://www.selectorshub.com/faq/">FAQs here.</a>
	  	</div>
	</div>
	<div id="nestedBlock">
		<button class="copy-btn box nestedCodeCopyBtn"></button>
		<textarea id="nestedCode" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"></textarea>
	</div>
	<button class="copy-btn domContentCopyBtn" title="Click to copy all values"></button>
	<ul id="selectorsHubEleContainer" placeholder="It will show the auto suggest for inspected element while typing.">	
	</ul>
	
	<div class="patronRequest">
		<!-- <button class="countdown-btn">
			<span class="footerCounter" title="Countdown for auto generated selectors."></span>
			<span class="countdown toolTip">After 5mins auto generated selectors will be off. Reopen SH to see them again.</span>
		</button> -->
		<button class='closeIcon svgIcon' title="Click to close footer."></button>
		
		
		

		<!-- <a class="requestHeader" href="http://bit.ly/sh_sponsors" target="_blank" title="Click to see all sponsors.">Sponsors:</a>
		<a target="_blank" class="sponsor-link" href="https://testproject.io/?utm_source=selectorshub&utm_medium=blog&utm_campaign=sj">
			<span class="sponsor toolTip">SelectorsHub Level Sponsor: TestProject, FREE automation tool.</span>
			<button class='sponsorIcon svgIcon'></button>
		</a> -->

		<!-- <span id="movingFooter"><b>Urgent help needed: SelectorsHub is running out of funds, pls donate atleast $10 & support. Donate here👇</b></span> -->
		<!-- <a class="requestHeader" href="https://www.youtube.com/c/SelectorsHubTheXPathTool?sub_confirmation=1" target="_blank" title="Click to watch tutorials">Watch Tutorial.
		</a>
		<span> | </span> -->
		<a class="requestHeader sponsor" href="https://bit.ly/new_testrigor_sh" target="_blank" title="Click to learn more about testRigor">Supported by <span class="sponsorName">testRigor<span>
		</a>
		
		<a href="https://selectorshub.com/selectorshub-pro/plans/?utm_source=ads-toggle&utm_medium=shub&utm_campaign=sh" target="_blank" class="ads__info__icon">
			<div class="ads__info__tooltip">Upgrade to Pro Version to go Ads Free. Sponsored ads.</div>
		</a>
		<div class="ads__viewer">
			<div class="ads__navigation__btn navigate__prev__btn"></div>
			<div class="ads__container"></div>
			<div class="ads__navigation__btn navigate__next__btn"></div>
		</div>
		<!-- <span> | </span> 
		<a class="requestHeader" target="_blank" href="https://bit.ly/SH_Slack" title="Click to join SelectorsHub slack channel.">Need Help?
		</a> -->
		
		<!-- <span> | </span> 
		<a class="requestHeader" href="http://bit.ly/SH_Patron" target="_blank" title="Click to be a Patron">Please be atleast $1 Patron. Target- 100, currently only 30 Patrons. 
		</a>
		<span> | </span> 
		<a class="requestHeader" href="http://bit.ly/SH_buymeacoffee" target="_blank" title="Click to buy a coffee for SH">Buy me a coffee ☕️
		</a>
		<span> | </span> 
		<a class="requestHeader" target="_blank" title="Click to watch tutorials" href="https://www.youtube.com/c/SelectorsHubTheXPathTool?sub_confirmation=1">
			Watch Tutorials
		</a> -->
		<!-- <span> | </span>
		<a class="requestHeader" href="https://youtu.be/1b1yfYfnowM" target="_blank" title="Learn how to use on Safari">Try on Safari, its different.
		</a> -->
		
		<span class="requestHeader"><a target="_blank" title="Click to see Changelog" href="http://bit.ly/SH_Changelog">v 4.6.1</a></span>
		
	</div>
	
	<div id="reviewModal" class="reviewModal">
	  <!-- Modal content -->
	  <div class="review-modal-content">
			
			<p>Loving SelectorsHub?</p>
	  		<a class="modalReviewLink2" href="https://bit.ly/SelectorsHub-Review" title="Click to rate" target="_blank">Share the love by rating it in the browser store.
	  			<p class="reviewStars">⭐️⭐️⭐️⭐️⭐️</p>
	  		</a>
	  		<button class='reviewClose svgIcon' title="Click to close."></button>
	  	
	    
	  </div>
	</div>

	<div id="instructionModal" class="instructionModal">
         <!-- Modal content -->
         <div class="instruction-modal-content">
         	<a class="instructionLink2" href="https://bit.ly/sh_courses_recordings" target="_blank">Checkout the courses to be expert in automation by SelectorsHub Creator.
            </a>
            <button class='instructionClose svgIcon' title="Click to close."></button>  
         </div>
    </div>


	<div id="tweetModal" class="tweetModal">
         <!-- Modal content -->
         <div class="tweet-modal-content">
               <a class="tweetLink2" href="https://bit.ly/3hiNWZQ" target="_blank">Click to subscribe SH youtube channel & support SH to grow & get all updates.
               </a>
               <button class='tweetClose svgIcon' title="Click to close."></button>
         </div>
    </div>

	<!-- The Modal -->
	<div id="patronModal" class="patronModal">
	  <!-- Modal content -->
	  <div class="patron-modal-content">
	  	<span class="patronClose">&times;</span><br>
	  	<p class="popupHeader" style="font-size: 1rem;">Please fill the work email <a class="workEmailLink" href="https://bit.ly/SHUserWorkEmail" title="Click to fill your work email." target="_blank"> here</a> to continue.</p>
	  	<br>
	  	<ul>
	  		<span>After filling the correct work email, this popup will not appear again. Student can fill the gmail id.</span>
	  		<!-- <li class="messagePoint">Follow <a href="https://bit.ly/learningProgram" target="_blank">these steps</a> to get Unique Key without making any donation.</li>
	  		<br>
	  		<li class="messagePoint">Or, simply be a <a href="https://bit.ly/SH_Patron" target="_blank">Patron</a> or make a one time donation <a href="https://bit.ly/sh_donate" target="_blank">here</a> to get Unique Key.</li>
	  		<br> -->
	  	</ul>
	    
	  </div>
	</div>

	<div class="uiConfig">
		<table class="uiConfig-table">
			<thead>
				<tr>
					<th class="uiConfig-header">
						<span>Selectors</span>
					</th>
					<th class="uiConfig-header">
						<span>UI Buttons</span>
					</th>
					<th class="uiConfig-header">
						<a class="uiConfigTutorialLink" target="_blank" href="https://bit.ly/SH4_UiConfig">
							<button class="uiConfigTutorialIcon">
								<span class="uiConfigTutorial toolTip">Click to learn how to use Ui Config feature.</span>
							</button>
					  	</a>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose cssSelector" type="checkbox" checked />rel cssSelector</label>
					</td>
					<td>
						<label><input class="choose autoSuggestToggle" type="checkbox" checked />Left Auto Suggest Toggle</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose relXpath" type="checkbox" checked />rel XPath</label>
					</td>
					<td>
						<label><input class="choose autoGenerateToggle" type="checkbox" checked />Right Auto Generate Toggle</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose indexXpath" type="checkbox" checked />index XPath</label>
					</td>
					<td>
						<label><input class="choose caseInsensitiveBtn" type="checkbox" checked />Case Insensitive Btn</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose testRigorPath" type="checkbox" checked />testRigor Path</label>
					</td>
					<td>
						<label><input class="choose suggestedXpath" type="checkbox" checked />SH Selector</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose jQuery" type="checkbox" checked />jQuery</label>
					</td>
					<td title="Set the debugger time. Ex- 5 means debugger will start in 5sec after clicking on debugger button.">
						<label><input class="choose debugTime" type="text" value="5" />  Debugger Start Time in Sec</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose jsPath" type="checkbox" checked />JS Path</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose id" type="checkbox" checked />id</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose className" type="checkbox" checked />className</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose name" type="checkbox" checked />name</label>
					</td>
				</tr>
				
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose linkText" type="checkbox" checked />LinkText</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose partialLinkText" type="checkbox" checked />Partial LinkText</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
					<td>
						<label><input class="choose absXpath" type="checkbox" checked />Abs XPath</label>
					</td>
				</tr>
				<tr class="uiConfig-row">
                    <td>
                            <label><input class="choose tagName" type="checkbox" checked />tagName</label>
                    </td>
               </tr>
			</tbody>
		</table>
	</div>
</div>

	<script src="../extension/countries.js"></script>
	<script src="devtools-script.js"></script>
    
</body>
</html>