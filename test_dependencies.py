#!/usr/bin/env python3
"""
Test script to check if all required dependencies are installed
"""

def test_imports():
    """Test if all required modules can be imported"""
    try:
        print("Testing imports...")
        
        # Test Streamlit
        import streamlit as st
        print("✅ Streamlit - OK")
        
        # Test Selenium
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.common.keys import Keys
        print("✅ Selenium - OK")
        
        # Test ConfigParser
        from configparser import ConfigParser
        print("✅ ConfigParser - OK")
        
        # Test other standard libraries
        import time
        import os
        print("✅ Standard libraries - OK")
        
        print("\n🎉 All dependencies are installed correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n💡 Please install missing dependencies:")
        print("pip install streamlit selenium")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config():
    """Test if config.ini file exists and is readable"""
    try:
        print("\nTesting config file...")
        
        if not os.path.exists("config.ini"):
            print("❌ config.ini file not found")
            return False
            
        parser = ConfigParser()
        parser.read("config.ini", encoding='utf-8')
        
        # Check required sections
        if "User_Data" not in parser.sections():
            print("❌ [User_Data] section missing in config.ini")
            return False
            
        if "Program_Data" not in parser.sections():
            print("❌ [Program_Data] section missing in config.ini")
            return False
            
        # Check required keys
        user_id = parser["User_Data"]["user_id"]
        competitor_link = parser["Program_Data"]["competitor_link"]
        
        print(f"✅ Config file - OK")
        print(f"   User ID: {user_id[:10]}...")
        print(f"   Competitor: {competitor_link}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config error: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testing Insta-Sniper Dependencies\n")
    
    imports_ok = test_imports()
    config_ok = test_config()
    
    if imports_ok and config_ok:
        print("\n✅ All tests passed! You can run the application.")
    else:
        print("\n❌ Some tests failed. Please fix the issues above.")
