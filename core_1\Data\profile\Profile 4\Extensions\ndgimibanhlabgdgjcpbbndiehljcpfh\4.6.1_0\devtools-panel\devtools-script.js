var isOpera=!!window.opr&&!!opr.addons||!!window.opera||0<=navigator.userAgent.indexOf(" OPR/"),isFirefox="undefined"!==typeof InstallTrigger,isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),isEdge=-1<window.navigator.userAgent.indexOf("Edg/")?!0:!1,isChrome=!!window.chrome&&(!!window.chrome.webstore||!!window.chrome.runtime),modalReviewLink1=document.querySelector(".modalReviewLink1"),modalReviewLink2=document.querySelector(".modalReviewLink2"),linkValue="https://bit.ly/SelectorsHub-Review",
newFeatureLaunch=document.querySelector("#newFeatureLaunch"),newFeatureLink="https://bit.ly/tcs_chrome";const API_URL="https://shubads.testcasehub.net/",EXT_ID="1";let platform="";var browserType=chrome;isChrome&&(platform="chrome");
isFirefox&&(linkValue="https://addons.mozilla.org/en-US/firefox/addon/selectorshub/reviews/",newFeatureLink="https://addons.mozilla.org/en-US/firefox/addon/testcase-studio/",platform="firefox",document.querySelector("body").style.fontFamily="RooneySans-Regular, Lucida Grande, Lucida Sans Unicode, Trebuchet MS, sans-serif",document.querySelector("#selectorsHubEleContainer").style.fontSize="12px");
isOpera&&(platform="opera",linkValue="https://addons.opera.com/en-gb/extensions/details/selectorshub/",newFeatureLink="https://addons.opera.com/en-gb/extensions/details/testcase-studio/");isEdge&&(platform="edge",linkValue="https://microsoftedge.microsoft.com/addons/detail/selectorshub/iklfpdeiaeeookjohbiblmkffnhdippe",newFeatureLink="https://microsoftedge.microsoft.com/addons/detail/testcase-studio/jdglgpgchkgiciogpolofelfdfkdgfcg");let countryName=null;
if(Intl){let a=Intl.DateTimeFormat().resolvedOptions().timeZone.split("/");countryName=COUNTRIES[a[a.length-1]]}trackEvent("open extension");modalReviewLink2.setAttribute("href",linkValue);var connectBackgroundPage=browserType.runtime.connect({name:"devtools-page"});connectBackgroundPage.postMessage({name:"init",tabId:browserType.devtools.inspectedWindow.tabId,contentScript:"../content-script/contentScript.js",contentCSS:"../content-script/contentScript.css"});
var openedRecorder=!1,openUploadModal=!1,userEmail="Anonymous",SMsteps=[],SMstepsWithOutCmd=[],CPstepsWithOutCmd=[],CPsteps=[],quotesBtn=document.querySelector(".quotes-btn"),smartFixPlaceholder="1. Open the page where XPaths need to be verified.\r\n2. Click on Editor icon (above in header).\r\n3. Set the XPath driver command.\r\n4. Paste the selectors page or complete script in the box.\r\n5. Click on Submit.\r\n6. SH will list all the xpaths with their occurrences.\r\n7. You can also download the XPath file.",
themeName=window.browserType.devtools.panels.themeName,iconColor="grey";"dark"===themeName?(document.querySelector("#cssFile").setAttribute("href","darkTheme.css"),iconColor="yellow"):(document.querySelector("#cssFile").setAttribute("href","lightTheme.css"),iconColor="black");
var OS=window.navigator.userAgent.includes("Mac")?"mac":"windows",browserLang=window.navigator.language,relXpathMatchingNodeLeft="6px",attrCheckbox=document.querySelectorAll(".chooseAttr"),eleContainerHeight=0,totalCountMargin=isFirefox?"39px":"7px",totalCountMarginIframe=isFirefox?"40px":"2px",totalCountMarginFrame=isFirefox?"37px":"2px",totalCountMarginShadow=isFirefox?"70px":"17px",totalCountMarginSvg=isFirefox?"61px":"8px",selectorEditBoxWidth="calc(100% - 128px)",selectorEditBoxWidthShadow="calc(100% - 138px)",
toggleElement=document.querySelector(".toggle-btn");
if(OS.includes("mac")){selectorEditBoxWidth="calc(100% - 128px)";document.querySelector(".selector-editor-div").style.width=selectorEditBoxWidth;document.querySelector(".chooseAttr.user").style.width=isFirefox?"calc(100% - 367px)":"calc(100% - 347px)";document.querySelector("label").style.margin="4px 6px 3px 4px";var editorContent=document.querySelector(".editorContent");editorContent.style.padding=isFirefox?"2px 1px 3px 3px":"3px 1px 3px 3px";relXpathMatchingNodeLeft="6px";document.querySelector(".toggle-btn").style.lineHeight=
"30px";document.querySelectorAll(".chooseAttr");document.querySelector(".uploadModalIcon").innerText="click to upload downloaded xpath xls file.";eleContainerHeight="calc(100% - 85px)";totalCountMargin=isFirefox?"8px":"7px";totalCountMarginIframe=isFirefox?"3px":"2px";totalCountMarginFrame=isFirefox?"3px":"2px";totalCountMarginShadow=isFirefox?"18px":"17px";totalCountMarginSvg=isFirefox?"9px":"8px";selectorEditBoxWidthShadow="calc(100% - 138px)";if(browserLang.includes("zh")){var autosuggestToggleBtn=
document.querySelector(".autosuggest-toggle-btn"),autosuggestToggleCircle=document.querySelector(".autosuggest-toggle-circle"),toggleBtn=document.querySelector(".toggle-btn"),toggleCircle=document.querySelector(".toggle-circle");autosuggestToggleBtn.style.width="34px";autosuggestToggleCircle.style.marginLeft="5px";toggleBtn.style.width="34px";toggleCircle.style.marginLeft="5px";totalCountMargin="32px";editorContent.style.padding="1px 1px 3px 3px"}}else{toggleBtn=document.querySelector(".toggle-btn");
selectorEditBoxWidth=isFirefox?"calc(100% - 130px)":"calc(100% - 134px)";document.querySelector(".selector-editor-div").style.width=selectorEditBoxWidth;document.querySelector(".chooseAttr.user").style.width=isFirefox?"calc(100% - 367px)":"calc(100% - 349px)";document.querySelector("label").style.margin="4px 0px 3px 6px";document.querySelector(".editorHeader").style.padding=isFirefox?"5px 5px 12px 5px":"6px 5px 9px 5px";relXpathMatchingNodeLeft="-16px";toggleBtn.style.marginLeft=isFirefox?"calc(100% - 34px)":
"calc(100% - 39px)";toggleBtn.style.lineHeight="29px";toggleBtn.style.marginTop="-18px";for(var typeOfLocator=document.querySelectorAll(".typeOfLocator"),noOfMatch=document.querySelectorAll(".noOfMatch"),selectorsCopyBtn=document.querySelectorAll("button.copy-btn"),i=0;i<typeOfLocator.length-1;i++)typeOfLocator[i].style.verticalAlign="super",noOfMatch[i].style.verticalAlign="super",noOfMatch[i].style.padding=isFirefox?"0px 4px 0px 4px":"0px 5px 0px 4px",selectorsCopyBtn[i].style.verticalAlign=isFirefox?
"text-top":"inherit";document.querySelector(".uploadModalIcon").innerText="click to upload downloaded xpath csv file.";eleContainerHeight=isFirefox?"calc(100% - 89px)":"calc(100% - 86px)";totalCountMargin=isFirefox?"18px":"10px";totalCountMarginIframe=isFirefox?"10px":"5px";totalCountMarginFrame=isFirefox?"9px":"5px";totalCountMarginShadow=isFirefox?"27px":"19px";totalCountMarginSvg=isFirefox?"20px":"12px";document.querySelector(".configContainer").style.marginTop=isFirefox?"1px":"2px";document.querySelector(".editorContent").style.fontFamily=
"inherit";selectorEditBoxWidthShadow=isFirefox?"calc(100% - 137px)":"calc(100% - 143px)";var errorInfo=document.querySelector(".errorInfo");errorInfo.style.padding="1px 5px 2px 5px";var cheetSheet=document.querySelector(".cheetSheet");cheetSheet.style.marginTop=isFirefox?"4px":"2px";document.querySelector(".ignoreCaseBtn").style.marginTop=isFirefox?"-27px":"-18px";var autoSuggestTop=isFirefox?"32":"30";autosuggestToggleBtn=document.querySelector(".autosuggest-toggle-btn");autosuggestToggleBtn.style.setProperty("--top",
autoSuggestTop);autosuggestToggleBtn.style.verticalAlign="middle";var toggleTop=isFirefox?"31":"29";document.querySelector(".toggle-btn").style.setProperty("--top",toggleTop);var attributeFilter=document.querySelector(".attributeFilter");attributeFilter.style.height=isFirefox?"26px":"25px";var nestedCodeCopyBtn=document.querySelector(".nestedCodeCopyBtn");nestedCodeCopyBtn.style.verticalAlign=isFirefox?"top":"bottom";var axesBtn=document.querySelector(".axes-btn");axesBtn.style.verticalAlign="middle";
var totalCountMessage=document.querySelector(".total-match-count");totalCountMessage.style.verticalAlign="middle";errorInfo=document.querySelector(".errorInfo");errorInfo.style.verticalAlign="middle"}
var showTotalCount=!1,showTotalResults=function(a){var b=document.querySelector(".jsTotalMatchCount"),c=document.querySelector(".jsSelector").value,d=document.querySelector(".errorInfo"),e=document.querySelector(".cheetSheetLink");b.style.padding=c?OS.includes("mac")?"1px 4px 0px 5px":isFirefox?"1px 5px 2px 5px":"1px 5px 1px 5px":"0px 0px 0px 0px";try{if(!showTotalCount||a.includes("blank")||1===a.length&&multiSelectorRecordBtn.className.includes("red")||!c)b.className+=" hideMatchCountMsg",d.className+=
" hideMatchCountMsg",e.className+=" hideMatchCountMsg";else{b.classList.remove("hideMatchCountMsg");d.className+=" hideMatchCountMsg";e.className+=" hideMatchCountMsg";var f=document.querySelector(".jsSelector").value;a.includes("wrong")?(d.classList.remove("hideMatchCountMsg"),e.classList.remove("hideMatchCountMsg"),b.style.background="red",b.textContent="Invalid "+a.split("wrong")[1].split("errorInfo")[0],a.split("errorInfo")[1]?(d.textContent=a.split("errorInfo")[1],d.setAttribute("title",a.split("errorInfo")[1])):
(d.className+=" hideMatchCountMsg",e.className+=" hideMatchCountMsg")):0===a.length?(b.textContent=a.length+" element matching.",b.style.background="#f29a00"):"/"===f||"."===f||"/."===f?(b.textContent="It's default DOM.",b.style.background="#0cb9a9"):"//."===f?(b.textContent=a.length+" matching all nodes.",b.style.background="#0cb9a9"):1===a.length?(b.textContent=a.length+" element matching.",b.style.background="#0cb9a9"):(b.textContent=a.length+" elements matching.",b.style.background="#f29a00")}}catch(h){console.log(h)}showTotalCount=
!1},highlighter=function(a){var b="";b=document.querySelector(".jsSelector").value;b=insideShadowDom||document.querySelector(".jsSelector").value&&!b.charAt(0).includes("/")&&!a.hasAttribute("xpath")?"css":"xpath";b=insideShadowDom?"css":b;a=a.getAttribute(b);a={name:b,index:a};a=JSON.stringify(a);browserType.devtools.inspectedWindow.eval("outlineOnHover(`"+a+"`)",{useContentScriptContext:!0},function(c){})},removeHighlighter=function(a){var b="";b=document.querySelector(".jsSelector").value;b=insideShadowDom||
b&&!b.charAt(0).includes("/")&&!a.hasAttribute("xpath")?"css":"xpath";b=insideShadowDom?"css":b;a=a.getAttribute(b);a={name:b+"-remove",index:a};a=JSON.stringify(a);browserType.devtools.inspectedWindow.eval("outlineOnHover(`"+a+"`)",{useContentScriptContext:!0},function(c){})},showAllMatchingNode=function(a){var b=document.querySelector("#selectorsHubEleContainer"),c="";c=document.querySelector(".jsSelector").value;c=!c||c.charAt(0).includes("/")?"xpath":"css";c=insideShadowDom?"css":c;b.innerHTML=
"";if("blank"!=a&&a[0].includes("<"))for(var d=1;d<=a.length;d++){if(a[d-1]=a[d-1]?a[d-1]:"",a[d-1]){var e=a[d-1];e=e.replace(/(\r\n|\n|\r)/gm,"");var f="";e.match(/^<td/)||e.match(/^<th/)?f="tr":e.match(/^<tr/)?f="tbody":e.match(/^<tbody/)?f="table":(e.match(/^<body/)?e=e.replace("<body","<bodytag").replace("body>","bodytag>"):e.match(/^<html/)?e=e.replace("<html","<htmltag").replace("html>","htmltag>"):e.match(/^<head/)?e=e.replace("<head","<headtag").replace("head>","headtag>"):e.match(/^<frame/)&&
(e=e.replace("<frame","<frametag").replace("frame>","frametag>")),f="li");var h=createElement(f);h.innerHTML=e;e=convertToTreeStructure(h,"parent closed");e.setAttribute(c,d);b.appendChild(e);e.onmouseover=function(){highlighter(this)};e.onmouseout=function(){removeHighlighter(this)}}}else for(d=1;d<=a.length;d++)h=createElement(f),h.innerHTML=a[d-1],e=convertToTreeStructure(h,"parent closed"),e.setAttribute(c,d),b.appendChild(e)},createDummyElement=function(){var a=createElement("div");a.innerHTML=
"<nav></nav>";var b=document.querySelector(".result");a=convertToTreeStructure(a,"parent closed","<nav></nav>");b.appendChild(a)},selectElements=function(a,b){chooseAttrsOption();var c="";b?c=document.querySelector(".jsSelector").value:(a=a.toLowerCase().includes("xpath")?"relXpath":a,c=document.querySelector(".valueSelector."+a.substring(0,3)).getAttribute(a.toLowerCase()));clearElements();browserType.devtools.inspectedWindow.eval("verifyXpathSelectors(`highlight-element`,`"+a+"`,`"+c+"`,`"+b+"`,`"+
chooseAttrs+"`)",{useContentScriptContext:!0},function(d){receiveXpathResults(d)})},secondPageUrl="xyz",insideShadowDom=!1,insideFrame=!1,bothListOfTextAndAttr=[],listOfTextAndAttr=[],shadowHostSelector="",nestedBlockOn="no";
function assignSelectorsValue(a){var b=document.querySelector(".selectorsRow.relXpath"),c=document.querySelector(".selectorsRow.indexXpath"),d=document.querySelector(".selectorsRow.cssSelector"),e=document.querySelector(".selectorsRow.jsPath"),f=document.querySelector(".selectorsRow.jQuery"),h=document.querySelector(".selectorsRow.id"),g=document.querySelector(".selectorsRow.name"),l=document.querySelector(".selectorsRow.className"),n=document.querySelector(".selectorsRow.linkText"),m=document.querySelector(".selectorsRow.partialLinkText"),
k=document.querySelector(".selectorsRow.absXpath"),p=document.querySelector(".selectorsRow.tagName"),q=document.querySelector(".selectorsRow.testRigorPath");relXpathWithCount=a[0];cssSelectorWithCount=a[1];quotesBtn.classList.contains("active")&&(relXpathWithCount[0]=relXpathWithCount[0].replaceAll("'",'"'),cssSelectorWithCount[0]=cssSelectorWithCount[0].replaceAll("'",'"'));var r=cssSelectorWithCount[0];indexXpathWithCount=a[2];idWithCount=a[3];nameWithCount=a[4];classNameWithCount=a[5];linkTextWithCount=
a[6];partialLinkTextWithCount=a[7];absXpathWithCount=a[8];tagNameWithCount=a[9];toggleElement.classList.contains("active")&&multiSelectorRecordBtn.className.includes("red")&&(label=a[10]);a[11]&&(shadowHostSelector=a[11][0]);testRigorPathWithCount=a[12];toggleElement=document.querySelector(".toggle-btn");if(toggleElement.classList.contains("active")){var u=document.querySelector(".jsSelector");document.querySelector(".header-copy-btn");u.focus();document.querySelector(".editorContent");u.setAttribute("placeholder",
"Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f");relXpathWithCount?generateColoredRelXpath():b.style.display="none";indexXpathWithCount?generateIndexXpath():c.style.display="none";cssSelectorWithCount?generateCss():d.style.display="none";if(cssSelectorWithCount.includes("closed shadow")||!cssSelectorWithCount)e.style.display="none",f.style.display="none";idWithCount?generateId():h.style.display="none";nameWithCount?generateName():g.style.display="none";
classNameWithCount?generateClassName():l.style.display="none";linkTextWithCount?generateLinkText():n.style.display="none";partialLinkTextWithCount?generatePartialLinkText():m.style.display="none";absXpathWithCount?generateAbsXpath():k.style.display="none";tagNameWithCount?generateTagName():p.style.display="none";testRigorPathWithCount?generateTestRigorPath():q.style.display="none";setTimeout(function(){xpathOrCss=insideShadowDom?"css":"xpath";selectElements(xpathOrCss,!1)},100);c=document.querySelector("#nestedBlock");
b=document.querySelector("#nestedCode");if(a[0][0].includes("closed Shadow DOM")||!a[11]||multiSelectorRecordBtn.classList.contains("red")||smartFix.classList.contains("defaultsmartFix")||uiSettingBtn.classList.contains("active")||axesBtn.classList.contains("active"))b.textContent="",c.style.display="none",nestedBlockOn="no";else{nestedBlockOn="yes";c.style.display="block";c=1==a[11].length?"//This Element is inside single shadow DOM.":"//This Element is inside "+a[11].length+" nested shadow DOM.";
a[11].reverse();for(e=0;e<a[11].length;e++)c=c+"\nString cssSelectorForHost"+(e+1)+' = "'+a[11][e]+'";';c+="\nThread.sleep(1000);";if(1==a[11].length)d='SearchContext shadow = driver.findElement(By.cssSelector("'+a[11][0]+'")).getShadowRoot();',c=c+"\n"+d+'\nThread.sleep(1000);\nshadow.findElement(By.cssSelector("'+(r+'"));');else{for(e=0;e<a[11].length;e++)d=0==e?"SearchContext shadow"+e+' = driver.findElement(By.cssSelector("'+a[11][e]+'")).getShadowRoot();':"SearchContext shadow"+e+" = shadow"+
(e-1)+'.findElement(By.cssSelector("'+a[11][e]+'")).getShadowRoot();',c=c+"\n"+d+"\nThread.sleep(1000);";a="shadow"+(a[11].length-1)+'.findElement(By.cssSelector("'+r+'"));';c=c+"\n"+a}b.textContent=c}}}var pageDomainName="";
connectBackgroundPage.onMessage.addListener(function(a){a.url&&!pageDomainName&&(pageDomainName=a.url.replace(/.+\/\/|www.|\..+/g,""));a.showInspected&&assignSelectorsValue(a.xpath);var b="";document.querySelector(".jsSelector");var c=document.querySelector(".selectors-input"),d=document.querySelector(".selector-editor-div"),e=document.querySelector(".selectorsRow.suggestedXpath"),f=document.querySelector(".selectorsRow.iframeXpath"),h=document.querySelector(".editorTitle"),g=document.querySelector(".editorContent"),
l=document.querySelector(".total-match-count");e.style.display="none";try{var n=a.axesXpathWithCount}catch(y){}try{var m=a.count.includes("wrong");var k="frame"==a.count?!0:"iframe"==a.count?!0:!1;var p="iframe"==a.count[0]?!0:"frame"==a.count[0]?!0:!1;var q="svgelement"===a.count;var r=a.count.includes("shadowdom");var u=a.count.includes("notIframe");var t=a.count.includes("suggestedSelector");var v=a.count.includes("z$*[shub]");b=a.count.includes("pageReload");var w=a.count.includes("sanjayXpathIndex");
var x=a.count.split("elementInfo-")[1]}catch(y){}n&&assignAxesXpath(n);if(k||r||q){if(r?(h.innerText="in ShadowDOM",insideShadowDom=!0,d.style.width=selectorEditBoxWidthShadow,g.style.backgroundColor="#2196F3",g.style.color="#ffffff",l.style.marginLeft=totalCountMarginShadow,c.setAttribute("placeholder","Write & verify cssSelector as XPath doesn't support shadowDOM......Click on \u2795 icon to save value \u27a1\ufe0f"),elementInfoAlert('Alert: This element is inside shadow dom which can\'t be accessed through XPath, use cssSelector for it.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a>',
"#2196f3")):q?(g.style.color="#ffffff",d.style.width=selectorEditBoxWidth,h.innerText="SVG element...",r||k||(g.style.backgroundColor="#f5388fb3"),l.style.marginLeft=totalCountMarginSvg,c.setAttribute("placeholder","Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f"),elementInfoAlert('Alert: This is a svg element & it doesn\'t support standard XPath format.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a>',
"#f5388fb3")):(insideFrame=!0,g.style.color="black",h.innerText=" inside iframe ",g.style.backgroundColor="#cccccc",l.style.marginLeft="frame"==a.count?totalCountMarginFrame:totalCountMarginIframe,c.setAttribute("placeholder","Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f"),elementInfoAlert('Alert: This element is inside iframe. Switch inside iframe to access it through automation.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a>',
"#747272")),void 0===a.count[1]||r||q)f.style.display="none"}else if(p){e=document.querySelector(".valueSelector.iframeXpath");document.querySelector(".typeOfLocator.box.iframeCopyBtn").innerText=a.count[0]+" XPath";e.style.color="white";f.style.display="block";a.count[1][0]=addDriverCommand.className.includes("inactive")?a.count[1][0]:addPreCommandInSelector(a.count[1][0]);f=a.count[2];m=document.querySelector("#nestedCode");k=document.querySelector("#nestedBlock");if(!(0<f.length)||multiSelectorRecordBtn.classList.contains("red")||
smartFix.classList.contains("defaultsmartFix")||uiSettingBtn.classList.contains("active")||axesBtn.classList.contains("active"))insideShadowDom||(k.style.display="none",nestedBlockOn="no");else{nestedBlockOn="yes";k.style.display="block";k="//This element is inside "+(f.length+1)+" nested frames.";u=addDriverCommand.className.includes("inactive")?"XPath for":"WebElement";u=document.querySelector(".driver-command");u=!addDriverCommand.className.includes("inactive")&&u.value?"WebElement":"XPath for";
for(p=1;p<=f.length;p++)f[p-1]=addDriverCommand.className.includes("inactive")?f[p-1]:addPreCommandInSelector(f[p-1]),k=k+"\n"+u+" frame"+p+" = "+f[p-1]+";";p=addDriverCommand.className.includes("inactive")?relXpathWithCount[0]:addPreCommandInSelector(relXpathWithCount[0]);k=k+"\n"+u+" frame"+(f.length+1)+" = "+a.count[1][0]+";\n"+u+" inspectedElement = "+p+";";m.textContent=k}e.innerText=a.count[1][0];document.querySelector(".noOfMatch.iframeXpath").textContent=a.count[1][1];e=document.querySelector(".noOfMatch.iframeXpath");
e.style.backgroundColor="1"==a.count[1][1]?"#0cb9a9":"0"==a.count[1][1]?"#ff0000":"#f78f06";setElementContainerHeight()}else if(u)insideShadowDom=!1,g.style.color="black",h.innerText="XPath/cssSel..",g.style.backgroundColor="dark"===themeName?"#f29a00db":"#f29a00a8",d.style.width=selectorEditBoxWidth,f.style.display="none",l.style.marginLeft=totalCountMargin,c.setAttribute("placeholder","Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f"),setElementContainerHeight();
else if(v)bothListOfTextAndAttr=[],bothListOfTextAndAttr=a.count;else if(w)w="",bothListOfTextAndAttr[3]=a.count;else if(t)f=document.querySelector(".valueSelector.suggestedXpath"),e.style.display="block",document.querySelector(".choose.suggestedXpath").checked||(e.style.display="none"),void 0===a.count[1]||insideShadowDom?e.style.display="none":(a.count[1][0]=addDriverCommand.className.includes("inactive")?a.count[1][0]:addPreCommandInSelector(a.count[1][0]),a.count[1][0]=a.count[1][0].includes("By.xpath")?
a.count[1][0].replace("By.xpath","By.cssSelector"):a.count[1][0].includes("By(xpath")?a.count[1][0].replace("By(xpath","By(cssSelector"):a.count[1][0].includes("ByXPath")?a.count[1][0].replace("ByXPath","ByCssSelector"):a.count[1][0].includes("XPath")?a.count[1][0].replace("XPath","CssSelector"):a.count[1][0].includes("Xpath")?a.count[1][0].replace("Xpath","CssSelector"):a.count[1][0].includes("XPATH")?a.count[1][0].replace("XPATH","CSSSELECTOR"):a.count[1][0].replace("xpath","cssSelector"),f.innerText=
a.count[1][0],document.querySelector(".noOfMatch.suggestedXpath").textContent=a.count[1][1],e=document.querySelector(".noOfMatch.suggestedXpath"),e.style.backgroundColor="1"==a.count[1][1]?"#0cb9a9":"0"==a.count[1][1]?"#ff0000":"#f78f06"),setElementContainerHeight();else if(b)setTimeout(()=>{connectBackgroundPage.postMessage({name:"init",tabId:browserType.devtools.inspectedWindow.tabId,contentScript:"../content-script/contentScript.js",contentCSS:"../content-script/contentScript.css"});setTimeout(()=>
{multiSelectorRecordBtn.className.includes("red")||generateSelectors()},1E3)},1E3);else if(x)elementInfoAlert(x,"#eb3030");else{if(m){e.style.display="none";highlightWrongXpath();showTotalResults(a.count);return}removeWrongXpath();showTotalCount&&showTotalResults(a.count);try{(document.querySelector(".jsSelector").value||1===a.count.length&&!toggleElement.classList.contains("inactive"))&&showAllMatchingNode(a.count)}catch(y){}a.event&&"shown"===a.event&&selectElements("xpath",!1)}document.querySelector(".selectorsGenerator").scrollTop=
0});function elementInfoAlert(a,b){var c=document.querySelector(".elementInfo"),d=document.querySelector(".elementInfoMsg");setTimeout(function(){d.innerHTML=a;c.style.display="block";c.style.background=b},100)}function appendElementsCount(a){let b=document.getElementById(a.id),c=nodesColor(a.elementCount);b.textContent=`${a.elementCount}`;b.classList.add(c)}
browserType.runtime.onMessage.addListener(function(a,b,c){"elementsCount"===a.type&&appendElementsCount(a);"generate-selector"!==a.message||multiSelectorRecordBtn.className.includes("red")||generateSelectors();"StopRecording"===a.message&&document.querySelector(".testCaseStudioBtn").classList.remove("redBlink");"AttachStudio"===a.message&&(document.querySelector(".testCaseStudioBtn").classList.add("redBlink"),openedRecorder=!0,updateRecorderAttr());"DeattachStudio"===a.message&&(document.querySelector(".testCaseStudioBtn").classList.remove("redBlink"),
openedRecorder=!1);"updateCPAttr"===a.type&&updateCPAttributes(a.data);"contextmenu"===a.name&&trackEvent("contextmenu: "+a.value)});function updateRecorderAttr(){connectBackgroundPage.postMessage({name:"recorderAttr",tabId:browserType.devtools.inspectedWindow.tabId,data:{idChecked:idAttr.checked,classChecked:classAttr.checked,nameChecked:nameAttr.checked,placeholderChecked:placeholderAttr.checked,textChecked:textCheckbox.checked,placeholderText:preCommandInput.value}})}
function updateCPAttributes(a){idAttr.checked=a.idChecked;classAttr.checked=a.classChecked;nameAttr.checked=a.nameChecked;placeholderAttr.checked=a.placeholderChecked;textCheckbox.checked=a.textChecked;browserType.storage.local.set({classChecked:a.classChecked},function(){});browserType.storage.local.set({nameChecked:a.nameChecked},function(){});browserType.storage.local.set({placeholderChecked:a.placeholderChecked},function(){});browserType.storage.local.set({idChecked:a.idChecked},function(){});
browserType.storage.local.set({textCheckboxChecked:a.textChecked},function(){})}var infoPlaceholderParent=document.querySelector(".infoPlaceholder"),infoPlaceholder=document.querySelector(".infoPlaceholder div");
document.addEventListener("DOMContentLoaded",async function(){var a=document.querySelector(".jsSelector"),b=document.querySelector(".chooseAttr.user");driverCommandBox.style.display="none";document.querySelector(".header-copy-btn");a.focus();document.querySelector(".editorTitle");setTimeout(()=>{multiSelectorRecordBtn.className.includes("red")||generateSelectors()},100);setTimeout(()=>{if(relXpathWithCount&&""!==relXpathWithCount)infoPlaceholder.style.display="none",infoPlaceholderParent.style.display=
"none";else if(""===relXpathWithCount&!toggleElement.classList.contains("inactive")){var d=document.querySelector(".valueSelector.rel"),e=document.querySelector(".valueSelector.css"),f=document.querySelector(".valueSelector.id");d.innerText="After installation, restart browser or open website in new tab.";e.innerHTML="Don't use it in blank tab, open <a  target='_blank' href='https://www.google.com/'>google.com</a>";f.innerHTML="For more details watch <a  target='_blank' href='https://bit.ly/2SzENm1'>this tutorial.</a>";
infoPlaceholder.style.display="block";infoPlaceholderParent.style.display="block";infoPlaceholder.style.color="red";infoPlaceholder.style.marginLeft="60px";infoPlaceholder.innerHTML="Ooops.....Looks like you are missing something.\n1. Close DevTools & open website in the new tab.\n2. There should be a url like <a  target='_blank' href='https://www.google.com/'>google.com</a> in the address bar.\n3. Neither Element should be inside cross-origin iframe nor inside shadow-root (closed).\n4. For more details watch <a  target='_blank' href='https://bit.ly/2SzENm1'>this tutorial.</a>\n5. If issue still persist, pls email me at <a href='mailto:<EMAIL>'><EMAIL></a>"}},
400);a.addEventListener("search",function(d){document.querySelector(".jsSelector").value||generateSelectors()});var c="";a.addEventListener("keyup",function(d){d=d.which||d.keyCode;var e=document.querySelector(".jsSelector").value,f=["f43axAVwxUiplyooQJfYTRRj0UtPYSac","DM2QfjpVMMtRn5zRliMJC4VVoFImyUmq","eA5aj9R4pO5ybBLIbzo7iIFIxg3M64Y8","2s8anL1BlMfSCXwwJVg3EkkXqlzN00Uf"];13===d?(trackEvent("Verify Selectors"),savedSelectors.style.display="none",e&&f.includes(e)&&(hideFooter(),footerCounter.style.display=
"none",toggleElement.classList.remove("notPatron"),plusBtn.classList.remove("notPatron"),copyAllBtnXPath.classList.remove("notPatron"),copyAllBtnCssSelector.classList.remove("notPatron"),nestedCodeCopyBtn.classList.remove("notPatron"),toggleElement.classList.remove("inactive"),toggleElement.classList.add("active"),browserType.storage.local.set({toggleElement:"active"},function(){}),toggleState(),generateSelectors()),showTotalCount=!0,e.slice(0,9).includes("document.")||e.slice(0,2).includes("$(")?
(clearElements(),executeScript()):(checkWrongXpath(),selectElements("xpath",!0)),document.querySelector(".infoPlaceholder div").style.display="none",document.querySelector(".infoPlaceholder").style.display="none",setElementContainerHeight()):document.querySelector(".jsSelector").value||8==d||46==d?(checkWrongXpath(),c=document.querySelector(".jsSelector").value):document.querySelector(".jsSelector").value||(selectElements("xpath",!0),generateSelectors());setTimeout(function(){var h=document.querySelector("#selectorsHubEleContainer > .text-node"),
g=document.querySelector(".domContentCopyBtn");g.style.display=h?"block":"none";g.addEventListener("click",function(){trackEvent("copy dom content");copyToClipboard("#selectorsHubEleContainer");showCopied()})},100)});isChrome&&a.addEventListener("keydown",function(d){d=window.event?event:d;if(90==d.keyCode&&d.ctrlKey||90==d.keyCode&&d.metaKey)document.querySelector(".jsSelector").value=c});b.addEventListener("keyup",async function(d){if(13===(d.which||d.keyCode))if("what is my sh_id"===d.target.value){var e=
await browserType.storage.local.get(["ext_uniq_id"]);if(e.ext_uniq_id){const f=document.createElement("div");f.classList.add("extension__id__wrapper");f.innerHTML=`
                            <div class="extension__id">
                                <div class="extension__id__close"></div>
                                <h4>Your unique sh_id</h4>
                                <div class="extension__id__container">
                                    <input type="text" name="extension-id" id="extension-id" value="${e.ext_uniq_id}" readOnly />
                                    <div class="extension__id__copy">
                                        <div class="icon"></div>
                                    </div>
                                </div>
                            </div>
                        `;let h=f.querySelector("#extension-id");e=f.querySelector(".extension__id__close");let g=f.querySelector(".extension__id__copy");e.addEventListener("click",()=>f.remove());g.addEventListener("click",async()=>{h.select();document.execCommand("Copy");g.style.backgroundColor="#ffe0a6";setTimeout(()=>g.style.backgroundColor="#ffffff",300)});document.body.appendChild(f);d.target.value=""}}else trackEvent("Enter user attr"),generateSelectors(),generateSuggestedXpath()});preCommandInput.addEventListener("keyup",
function(d){d=d.which||d.keyCode;this.value=this.value.replace(/\u201c/g,'"').replace(/\u201d/g,'"');if(13===d){this.value.replace(/xpathvalue/i,"xpathvalue");if(this.value&&!this.value.toLowerCase().includes("xpathvalue")){this.classList.add("wrongXpath");showXpathValueAlertMsg("xpathValue keyword should be there in the command.");return}if(this.value&&this.value.toLowerCase().includes('"xpathvalue"')&&quotesBtn.classList.contains("active")){this.classList.add("wrongXpath");showXpathValueAlertMsg('Put xpathValue inside single quote as XPath is generated with "".');
return}generateSelectors();generateSuggestedXpath();smartFix.classList.contains("defaultsmartFix")&&updateSMSteps();multiSelectorRecordBtn.classList.contains("red")&&updateselectorsHubSteps()}if(this.value&&(!this.value.toLowerCase().includes("xpathvalue")||this.value.toLowerCase().includes('"xpathvalue"')&&quotesBtn.classList.contains("active")))this.classList.add("wrongXpath"),addDriverCommand.classList.add("inactive"),addDriverCommand.classList.remove("active");else if(!this.value||this.value.toLowerCase().includes("xpathvalue"))this.classList.remove("wrongXpath"),
addDriverCommand.classList.remove("inactive"),addDriverCommand.classList.add("active")});addClickHandlers();clickToCopyIframeXpath();clickToEditIframeXpath();clickToCopySuggestedXpath();clickToEditSuggestedXpath();clickToCopyRelXpath();clickToEditRelXpath();clickToCopyIndexXpath();clickToEditIndexXpath();clickToCopyId();clickToEditId();clickToCopyName();clickToEditName();clickToCopyClassName();clickToEditClassName();clickToCopyCss();clickToEditCss();clickToCopyJQuery();clickToEditJQuery();clickToCopyJsPath();
clickToEditJsPath();clickToCopyLinkText();clickToEditLinkText();clickToCopyPartialLinkText();clickToEditPartialLinkText();clickToCopyTagName();clickToEditTagName();clickToCopyAbsXpath();clickToEditAbsXpath();clickToCopyAxesXpath();clickToEditAxesXpath();clickToCopyLastAxesXpath();clickToEditLastAxesXpath();clickToCopyTestRigorPath();clickToEditTestRigorPath();setTimeout(()=>{},2E3)});
function setElementContainerHeight(){var a=79,b=0,c=document.querySelectorAll(".selectorsRow"),d=document.querySelector(".selectorsGenerator").offsetHeight;expandBtn.classList.contains("inactive");for(var e=0;e<c.length;e++)!c[e].style.display.includes("none")&&b<d&&(b+=25);"none"!=driverCommandBox.style.display&&(a+=25);"none"!=attributeFilter.style.display&&(a+=25);"none"==patronRequest.style.display&&(a-=24);toggleElement=document.querySelector(".toggle-btn");toggleElement.classList.contains("inactive")?
document.querySelector("#selectorsHubEleContainer").style.height=eleContainerHeight:document.querySelector("#selectorsHubEleContainer").style.height="calc(100% - "+(b+a-15)+"px)"}
function generateAbsXpath(){var a=document.querySelector(".valueSelector.abs"),b=document.querySelector(".selectorsRow.absXpath");document.querySelector(".choose.absXpath").checked||(b.style.display="none");if(void 0===absXpathWithCount){var c="Element might be inside cross-origin iframe. Use SH contextMenu feature to copy selectors.";var d="0";"xpath".includes("abs")||(b.style.display="none")}else c=absXpathWithCount[0],d=absXpathWithCount[1];a.setAttribute("absXpath",c);c=addDriverCommand.className.includes("inactive")?
c:addPreCommandInSelector(c);c=c.includes("cy.get")?c.replace("cy.get","cy.xpath"):c;a.innerText=c;c=document.querySelector(".noOfMatch.absXpath");c.textContent=d;c.style.backgroundColor="1"==d?"#0cb9a9":"0"==d?"#ff0000":"#f78f06";"xpath".includes("abs")&&(selectElements("xpath",!1),setElementContainerHeight(),document.querySelector(".noOfMatch.absXpath").parentElement.style.width="26px");"0"!=d||"xpath".includes("abs")||(b.style.display="none")}
function generateCss(){var a="",b=document.querySelector(".valueSelector.css"),c=document.querySelector(".selectorsRow.cssSelector"),d=document.querySelector(".noOfMatch.cssSelector"),e=document.querySelector(".valueSelector.jQuery"),f=document.querySelector(".selectorsRow.jQuery"),h=document.querySelector(".noOfMatch.jQuery"),g=document.querySelector(".valueSelector.jsPath"),l=document.querySelector(".selectorsRow.jsPath"),n=document.querySelector(".noOfMatch.jsPath"),m=document.querySelector(".choose.cssSelector"),
k=document.querySelector(".choose.jsPath"),p=document.querySelector(".choose.jQuery");c.style.display="";l.style.display="";f.style.display="";if(void 0===cssSelectorWithCount||0==cssSelectorWithCount[1]||void 0==cssSelectorWithCount[0])cssSelectorWithCount[0].includes("closed shadow dom")?(d.textContent="0",b.innerHTML='<a href="https://youtu.be/Qtdmo9H0JZA" target="blank">Please watch this Tutorial to handle closed shadow dom element</a>'):(b.textContent="Element inside cross-origin iframe. Copy Selectors by right click on element or open iframe src url in new tab.",
d.textContent="0",g.textContent="Element inside cross-origin iframe. Copy Selectors by right click on element or open iframe src url in new tab.",n.textContent="0",e.textContent="Element inside cross-origin iframe. Copy Selectors by right click on element or open iframe src url in new tab.",h.textContent="0","xpath".includes("css")||(c.style.display="none")),l.style.display="none",f.style.display="none";else{try{b.setAttribute("css",cssSelectorWithCount[0].trim()),quotesBtn.classList.contains("active")?
(g.innerText="document.querySelector(`"+cssSelectorWithCount[0].trim()+"`)",e.innerText="$(`"+cssSelectorWithCount[0].trim()+"`)"):(g.innerText='document.querySelector("'+cssSelectorWithCount[0].trim()+'")',e.innerText='$("'+cssSelectorWithCount[0].trim()+'")'),cssSelectorWithoutCommand=cssSelectorWithCount[0].trim(),cssSelectorWithCount[0]=addDriverCommand.className.includes("inactive")?cssSelectorWithCount[0].trim():addPreCommandInSelector(cssSelectorWithCount[0].trim()),cssSelectorWithCount[0]=
cssSelectorWithCount[0].includes("By.xpath")?cssSelectorWithCount[0].replace("By.xpath","By.cssSelector"):cssSelectorWithCount[0].includes("By(xpath")?cssSelectorWithCount[0].replace("By(xpath","By(cssSelector"):cssSelectorWithCount[0].includes("ByXPath")?cssSelectorWithCount[0].replace("ByXPath","ByCssSelector"):cssSelectorWithCount[0].includes("cy.xpath")?cssSelectorWithCount[0].replace("cy.xpath","cy.get"):cssSelectorWithCount[0].replace("cy.Xpath","cy.get"),b.innerText=cssSelectorWithCount[0],
a=cssSelectorWithCount[1],d.textContent=a,h.textContent=a,h.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06",n.textContent=a,n.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06",("xpath".includes("css")||insideShadowDom)&&setElementContainerHeight(),"0"!=a||"xpath".includes("css")||(b.textContent="Element inside cross-origin iframe. Copy Selectors by right click on element or open iframe src url in new tab.",c.style.display="none",l.style.display="none",f.style.display=
"none")}catch(q){relXpathWithCount[0].includes("cross-origin")?(a="0",d.textContent=a,b.innerHTML='<a href="https://youtu.be/vil6Ks5P-Ng" target="blank">Please watch this Tutorial to handle this element</a>'):c.style.display="none"}d.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06";c.style.display=m.checked?"block":"none";l.style.display=k.checked?"block":"none";f.style.display=p.checked?"block":"none"}}
function generateIndexXpath(){var a=document.querySelector(".valueSelector.indexXpath"),b=document.querySelector(".selectorsRow.indexXpath");b.style.display="";document.querySelector(".choose.indexXpath").checked||(b.style.display="none");void 0===indexXpathWithCount||0===indexXpathWithCount[1]?b.style.display="none":(a.setAttribute("selector",indexXpathWithCount[0]),indexXpathWithoutCommand=indexXpathWithCount[0],indexXpathWithCount[0]=addDriverCommand.className.includes("inactive")?indexXpathWithCount[0]:
addPreCommandInSelector(indexXpathWithCount[0]),indexXpathWithCount[0]=indexXpathWithCount[0].includes("cy.get")?indexXpathWithCount[0].replace("cy.get","cy.xpath"):indexXpathWithCount[0],a.innerText=indexXpathWithCount[0],a=indexXpathWithCount[1],b=document.querySelector(".noOfMatch.indexXpath"),b.textContent=a,b.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06")}
function generateId(){var a=document.querySelector(".valueSelector.id"),b=document.querySelector(".selectorsRow.id");b.style.display="";document.querySelector(".choose.id").checked||(b.style.display="none");void 0===idWithCount||0===idWithCount[1]?b.style.display="none":(a.setAttribute("selector",idWithCount[0]),idWithoutCommand=idWithCount[0],idWithCount[0]=addDriverCommand.className.includes("inactive")?idWithCount[0]:addPreCommandInSelector(idWithCount[0]),idWithCount[0]=idWithCount[0].includes("By.xpath")?
idWithCount[0].replace("By.xpath","By.id"):idWithCount[0].includes("By(xpath")?idWithCount[0].replace("By(xpath","By(id"):idWithCount[0].includes("ByXPath")?idWithCount[0].replace("ByXPath","ById"):idWithCount[0].includes("XPath")?idWithCount[0].replace("XPath","Id"):idWithCount[0].includes("Xpath")?idWithCount[0].replace("Xpath","Id"):idWithCount[0].includes("XPATH")?idWithCount[0].replace("XPATH","ID"):idWithCount[0].replace("xpath","id"),a.innerText=idWithCount[0],a=idWithCount[1],b=document.querySelector(".noOfMatch.id"),
b.textContent=a,b.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06")}
function generateClassName(){var a=document.querySelector(".valueSelector.className"),b=document.querySelector(".selectorsRow.className");b.style.display="";document.querySelector(".choose.className").checked||(b.style.display="none");void 0===classNameWithCount||0===classNameWithCount[1]?b.style.display="none":(a.setAttribute("selector",classNameWithCount[0]),classNameWithoutCommand=classNameWithCount[0],classNameWithCount[0]=addDriverCommand.className.includes("inactive")?classNameWithCount[0]:
addPreCommandInSelector(classNameWithCount[0]),classNameWithCount[0]=classNameWithCount[0].includes("By.xpath")?classNameWithCount[0].replace("By.xpath","By.className"):classNameWithCount[0].includes("By(xpath")?classNameWithCount[0].replace("By(xpath","By(className"):classNameWithCount[0].includes("ByXPath")?classNameWithCount[0].replace("ByXPath","ByClassName"):classNameWithCount[0].includes("XPath")?classNameWithCount[0].replace("XPath","ClassName"):classNameWithCount[0].includes("Xpath")?classNameWithCount[0].replace("Xpath",
"ClassName"):classNameWithCount[0].includes("XPATH")?classNameWithCount[0].replace("XPATH","CLASSNAME"):classNameWithCount[0].replace("xpath","className"),a.innerText=classNameWithCount[0],a=classNameWithCount[1],b=document.querySelector(".noOfMatch.className"),b.textContent=a,b.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06")}
function generateName(){var a=document.querySelector(".valueSelector.name"),b=document.querySelector(".selectorsRow.name");b.style.display="";document.querySelector(".choose.name").checked||(b.style.display="none");void 0===nameWithCount||0===nameWithCount.length?b.style.display="none":(a.setAttribute("selector",nameWithCount[0]),nameWithoutCommand=nameWithCount[0],nameWithCount[0]=addDriverCommand.className.includes("inactive")?nameWithCount[0]:addPreCommandInSelector(nameWithCount[0]),nameWithCount[0]=
nameWithCount[0].includes("By.xpath")?nameWithCount[0].replace("By.xpath","By.name"):nameWithCount[0].includes("By(xpath")?nameWithCount[0].replace("By(xpath","By(name"):nameWithCount[0].includes("ByXPath")?nameWithCount[0].replace("ByXPath","ByName"):nameWithCount[0].includes("XPath")?nameWithCount[0].replace("XPath","Name"):nameWithCount[0].includes("Xpath")?nameWithCount[0].replace("Xpath","Name"):nameWithCount[0].includes("XPATH")?nameWithCount[0].replace("XPATH","NAME"):nameWithCount[0].replace("xpath",
"name"),a.innerText=nameWithCount[0],a=nameWithCount[1],b=document.querySelector(".noOfMatch.name"),b.textContent=a,b.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06")}
function generateTagName(){var a=document.querySelector(".valueSelector.tagName"),b=document.querySelector(".selectorsRow.tagName");b.style.display="";document.querySelector(".choose.tagName").checked||(b.style.display="none");void 0===tagNameWithCount||0===tagNameWithCount[1]?b.style.display="none":(a.setAttribute("selector",tagNameWithCount[0]),tagNameWithoutCommand=tagNameWithCount[0],tagNameWithCount[0]=addDriverCommand.className.includes("inactive")?tagNameWithCount[0]:addPreCommandInSelector(tagNameWithCount[0]),
tagNameWithCount[0]=tagNameWithCount[0].includes("By.xpath")?tagNameWithCount[0].replace("By.xpath","By.tagName"):tagNameWithCount[0].includes("By(xpath")?tagNameWithCount[0].replace("By(xpath","By(tagName"):tagNameWithCount[0].includes("ByXPath")?tagNameWithCount[0].replace("ByXPath","ByTagName"):tagNameWithCount[0].includes("XPath")?tagNameWithCount[0].replace("XPath","TagName"):tagNameWithCount[0].includes("Xpath")?tagNameWithCount[0].replace("Xpath","TagName"):tagNameWithCount[0].includes("XPATH")?
tagNameWithCount[0].replace("XPATH","TAGNAME"):tagNameWithCount[0].replace("xpath","tagName"),a.innerText=tagNameWithCount[0],a=tagNameWithCount[1],b=document.querySelector(".noOfMatch.tagName"),b.textContent=a,b.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06");setElementContainerHeight();setWidthOfSelectorHeader()}
function generateTestRigorPath(){var a=document.querySelector(".valueSelector.testRigorPath"),b=document.querySelector(".selectorsRow.testRigorPath");b.style.display="";document.querySelector(".choose.testRigorPath").checked||(b.style.display="none");void 0===testRigorPathWithCount||0===testRigorPathWithCount[1]?b.style.display="none":(a.setAttribute("selector",testRigorPathWithCount[0]),testRigorPathWithoutCommand=testRigorPathWithCount[0],a.innerText=testRigorPathWithCount[0]);setElementContainerHeight();
setWidthOfSelectorHeader()}
function generateLinkText(){var a=document.querySelector(".valueSelector.linkText"),b=document.querySelector(".selectorsRow.linkText");b.style.display="";document.querySelector(".choose.linkText").checked||(b.style.display="none");void 0===linkTextWithCount||0===linkTextWithCount[1]?b.style.display="none":(a.setAttribute("selector",linkTextWithCount[0]),linkTextWithoutCommand=linkTextWithCount[0],linkTextWithCount[0]=addDriverCommand.className.includes("inactive")?linkTextWithCount[0]:addPreCommandInSelector(linkTextWithCount[0]),
linkTextWithCount[0]=linkTextWithCount[0].includes("By.xpath")?linkTextWithCount[0].replace("By.xpath","By.linkText"):linkTextWithCount[0].includes("By(xpath")?linkTextWithCount[0].replace("By(xpath","By(linkText"):linkTextWithCount[0].includes("ByXPath")?linkTextWithCount[0].replace("ByXPath","ByLinkText"):linkTextWithCount[0].includes("XPath")?linkTextWithCount[0].replace("XPath","LinkText"):linkTextWithCount[0].includes("Xpath")?linkTextWithCount[0].replace("Xpath","LinkText"):linkTextWithCount[0].includes("XPATH")?
linkTextWithCount[0].replace("XPATH","LINKTEXT"):linkTextWithCount[0].replace("xpath","linkText"),a.innerText=linkTextWithCount[0],a=linkTextWithCount[1],b=document.querySelector(".noOfMatch.linkText"),b.textContent=a,b.style.backgroundColor="1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06")}
function generatePartialLinkText(){var a=document.querySelector(".valueSelector.partialLinkText"),b=document.querySelector(".selectorsRow.partialLinkText");b.style.display="";document.querySelector(".choose.partialLinkText").checked||(b.style.display="none");void 0===partialLinkTextWithCount||0===partialLinkTextWithCount[1]?b.style.display="none":(a.setAttribute("selector",partialLinkTextWithCount[0]),partialLinkTextWithoutCommand=partialLinkTextWithCount[0],partialLinkTextWithCount[0]=addDriverCommand.className.includes("inactive")?
partialLinkTextWithCount[0]:addPreCommandInSelector(partialLinkTextWithCount[0]),partialLinkTextWithCount[0]=partialLinkTextWithCount[0].includes("By.xpath")?partialLinkTextWithCount[0].replace("By.xpath","By.partialLinkText"):partialLinkTextWithCount[0].includes("By(xpath")?partialLinkTextWithCount[0].replace("By(xpath","By(partialLinkText"):partialLinkTextWithCount[0].includes("ByXPath")?partialLinkTextWithCount[0].replace("ByXPath","ByPartialLinkText"):partialLinkTextWithCount[0].includes("XPath")?
partialLinkTextWithCount[0].replace("XPath","PartialLinkText"):partialLinkTextWithCount[0].includes("Xpath")?partialLinkTextWithCount[0].replace("Xpath","PartialLinkText"):partialLinkTextWithCount[0].includes("XPATH")?partialLinkTextWithCount[0].replace("XPATH","PARTIALLINKTEXT"):partialLinkTextWithCount[0].replace("xpath","partialLinkText"),a.innerText=partialLinkTextWithCount[0],a=partialLinkTextWithCount[1],b=document.querySelector(".noOfMatch.partialLinkText"),b.textContent=a,b.style.backgroundColor=
"1"==a?"#0cb9a9":"0"==a?"#ff0000":"#f78f06")}function updateGAEvents(){}var chooseAttrs=[],idAttr=document.querySelector(".chooseAttr.id"),classAttr=document.querySelector(".chooseAttr.class"),nameAttr=document.querySelector(".chooseAttr.name"),placeholderAttr=document.querySelector(".chooseAttr.placeholder"),userAttrName=document.querySelector(".chooseAttr.user"),textCheckbox=document.querySelector(".chooseAttr.textXpath"),preCommandInput=document.querySelector(".driver-command");
userAttrName.addEventListener("keyup",function(a){13===(a.which||a.keyCode)&&"what is my sh_id"===a.target.value?browserType.storage.local.set({userAttrName:""},function(){}):(a=userAttrName.value.trim(),browserType.storage.local.set({userAttrName:a},function(){}))});browserType.storage.local.get(["userAttrName"],function(a){userAttrName.value=void 0==a.userAttrName?"":a.userAttrName});
function chooseAttrsOption(){chooseAttrs=[userAttrName.value.trim(),idAttr.checked?"withid":"withoutid",classAttr.checked?"withclass":"withoutclass",nameAttr.checked?"withname":"withoutname",placeholderAttr.checked?"withplaceholder":"withoutplaceholder",textCheckbox.checked?"withtext":"withouttext"]}
function generateColoredRelXpath(){chooseAttrsOption();var a=document.querySelector(".valueSelector.rel");var b=document.querySelector(".selectorsRow.relXpath");document.querySelector(".choose.relXpath").checked||(b.style.display="none");if(void 0===relXpathWithCount){b="Element inside cross-origin iframe. Copy Selectors by right click on element or open iframe src url in new tab.";var c="0"}else b=relXpathWithCount[0],c=relXpathWithCount[1];xpathWithoutCommand=b;a.setAttribute("selectors",b);a.setAttribute("relXpath",
b);var d=preCommandInput.value.trim();d=d.replace(/\u201c/g,'"').replace(/\u201d/g,'"');d=d.includes("cy.get")?d.replace("cy.get","cy.xpath"):d;var e="",f="";!addDriverCommand.className.includes("inactive")&&void 0!==b&&d&&(f=(f=testRigorPathWithCount[0].replaceAll('"',""))?f.slice(0,30):f,d=d.replace(/selectorname/i,toCamelCase(f)),d.includes("xpathvalue")?(e=d.split("xpathvalue")[0],f=d.split("xpathvalue")[1]):d.includes("xpathValue")?(e=d.split("xpathValue")[0],f=d.split("xpathValue")[1]):d.includes("cssValue")?
(e=d.split("cssValue")[0],f=d.split("cssValue")[1]):d.includes("cssvalue")?(e=d.split("cssvalue")[0],f=d.split("cssvalue")[1]):d.includes("cssSelectoValue")?(e=d.split("cssSelectoValue")[0],f=d.split("cssSelectoValue")[1]):d.includes("cssselectovalue")?(e=d.split("cssselectovalue")[0],f=d.split("cssselectovalue")[1]):(e=d.split('"')[0]+'"',f='"'+d.split('"')[2]));a.innerHTML="";d=createElement("span","p1-label");d.innerText=e;a.appendChild(d);var h=createElement("span","v0-label");h.innerText="//";
a.appendChild(h);d="";b.slice(2).includes("//")?(e=b.split("//"),d="//"):b.slice(2).includes("/")?(e=b.slice(1).split("/"),d="/"):e=b.split("//");if(b.includes("["))for(h=1;h<e.length;h++)if(e[h].includes("[")){var g=1===h?e[h].split("[")[0]+"[":d+e[h].split("[")[0]+"[";m=createElement("span","v1-label");m.innerText=g;a.appendChild(m);if(!e[h].split("[")[1].includes("'")||3<e[h].split("'").length)g=createElement("span","v4-label"),m=e[h].substr(e[h].indexOf("[")+1),g.innerText=m,a.appendChild(g);
else{var l=e[h].split("[")[1].split("'")[0]+"'";m=e[h].split("[")[1].split("'")[1];g="'"+e[h].split("[")[1].split("'")[2];m||(m=e[h].split("'")[1].split("'")[0],g="'"+e[h].split("'")[2]);var n=createElement("span","v2-label");n.innerText=l.includes(void 0)?"":l;a.appendChild(n);l=createElement("span","v3-label");l.innerText=m.includes(void 0)?"":m;a.appendChild(l);m=createElement("span","v4-label");m.innerText=g.includes(void 0)?"":g;a.appendChild(m);2<e[h].split("['").length&&(1<"["+e[h].split("[")[2].split("'")[0].length?
(l="["+e[h].split("[")[2].split("'")[0]+"'",m=e[h].split("[")[2].split("'")[1],g="'"+e[h].split("[")[2].split("'")[2],n=createElement("span","v2-label"),n.innerText=l.includes(void 0)?"":l,a.appendChild(n),l=createElement("span","v3-label"),l.innerText=m.includes(void 0)?"":m,a.appendChild(l),m=createElement("span","v4-label"),m.innerText=g.includes(void 0)?"":g,a.appendChild(m)):(l="["+e[h].split("[")[2].split("'")[0],n=createElement("span","v2-label"),n.innerText=l.includes(void 0)?"":l,a.appendChild(n)))}}else{g=
1===h?e[h]:d+e[h];var m=createElement("span","v1-label");m.innerText=g;a.appendChild(m)}else a.removeChild(h),d=createElement("span","abs-label"),d.innerText=b,a.appendChild(d);d=createElement("span","p2-label");d.innerText=f;a.appendChild(d);document.querySelector(".noOfMatch.relXpath").textContent=c;document.querySelector(".noOfMatch.relXpath").style.backgroundColor="1"==c?"#0cb9a9":"0"==c?"#ff0000":"#f78f06";a="";c=document.querySelector(".warningIcon");f=document.querySelector(".alert.toolTip");
document.querySelector(".noOfMatch.relXpath");c.style.display="none";b&&(relXpathWithCount[2]||relXpathWithCount[3])&&(c.style.display="",OS.includes("mac")&&(c.parentElement.style.verticalAlign="baseline"),relXpathWithCount[2]&&relXpathWithCount[3]?a="id & class both look dynamic. \nUncheck the id & class checkbox to generate rel xpath\n without them if it is generated with them.":relXpathWithCount[2]?a=relXpathWithCount[2]+" looks dynamic. Uncheck the "+relXpathWithCount[2]+"\n checkbox or delete the "+
relXpathWithCount[2]+" attribute from attribute box to\n generate rel xpath without "+relXpathWithCount[2]+" if it is generated with "+relXpathWithCount[2]+".":(c.style.display="none",c.parentElement.style.verticalAlign="text-bottom"),f.innerText=a);insideShadowDom=b.toLowerCase().includes("this element is inside")&&b.toLowerCase().includes("shadow dom")?!0:!1;"xpath".includes("rel")&&setElementContainerHeight()}
var highlightWrongXpath=function(){document.querySelector(".selectors-input").className+=" wrongXpath"},removeWrongXpath=function(){try{document.querySelector(".selectors-input.wrongXpath").classList.remove("wrongXpath");document.querySelector(".jsTotalMatchCount").textContent="";var a=document.querySelector(".errorInfo"),b=document.querySelector(".cheetSheetLink");a.textContent="";a.className+=" hideMatchCountMsg";b.className+=" hideMatchCountMsg";document.querySelector(".jsTotalMatchCount").style.padding=
"0px 0px 0px 0px"}catch(c){}},checkWrongXpath=function(){var a=document.querySelector(".jsSelector"),b=a.value,c=document.querySelector(".jsTotalMatchCount"),d=document.querySelector(".errorInfo"),e=document.querySelector(".errorInfo"),f="";f=insideShadowDom||b&&!b.charAt(0).includes("/")&&!b.substr(0,2).includes("./")&&!b.charAt(0).includes("(")&&"."!==b?"CSS":"XPath";b||(c.className+=" hideMatchCountMsg",d.className+=" hideMatchCountMsg",e.className+=" hideMatchCountMsg",selectElements(f,!1),clearElements());
a.getAttribute("class").includes("wrongXpath")&&removeWrongXpath();if(b)try{browserType.devtools.inspectedWindow.eval("checkInvalidSelector(`"+b+"`)",{useContentScriptContext:!0},function(h){h&&highlightWrongXpath()})}catch(h){b.slice(0,9).includes("document.")||b.slice(0,2).includes("$(")||highlightWrongXpath()}},clearElements=function(){var a=document.querySelector("#selectorsHubEleContainer"),b=document.querySelector(".jsTotalMatchCount"),c=document.querySelector(".errorInfo");document.querySelector(".cheetSheetLink");
document.querySelector(".jsSelector");b.innerHTML="";c.innerHTML="";b.style.padding="0px 0px 0px 0px";toggleElement=document.querySelector(".toggle-btn");if(0<=bothListOfTextAndAttr.length||toggleElement.classList.contains("inactive"))a.innerHTML=""};browserType.devtools.panels.elements.onSelectionChanged.addListener(function(){multiSelectorRecordBtn.className.includes("red")||generateSelectors();setElementContainerHeight()});
function changeBackground(a){document.body.style.background=a.target.value}
var createElement=function(a,b){a=document.createElement(a);b&&a.setAttribute("class",b);return a},elementRegEx=/(?:^(<.+?>)(.+)(<\/.+?>)$)|(?:^(<.+?>)$)/,getOpenCloseTag=function(a){a=elementRegEx.exec(a.outerHTML);var b="",c="";a&&(b=a[1]?a[1]:a[0],c=a[3]?a[3]:"");return{openTag:b,closeTag:c}},getTextNodeWrapper=function(a){var b=createElement("div","child level-padding");b.innerText=a.textContent;return b},getWrapperNode=function(a,b){var c=getOpenCloseTag(a);if(a.nodeType===Node.TEXT_NODE)return getTextNodeWrapper(a);
a.childNodes&&0===a.childNodes.length&&!a.textContent&&(b="leaf-node level-padding");b=createElement("div",b);var d=createElement("span","open-tag-label");d.innerText=c.openTag;b.appendChild(d);if(a.childNodes&&0<a.childNodes.length||a.textContent)childElements=convertToTreeStructure(a,"child level-padding closed"),b.appendChild(childElements);a=createElement("span","close-tag-label");a.innerText=c.closeTag;b.appendChild(a);return b},convertToTreeStructure=function(a,b){if(!a.childElementCount){var c=
createElement("div","text-node level-padding");c.innerText=a.textContent;return c}if(1<a.childNodes.length){a=a.childNodes;b=createElement("div",b+" children-cont");for(var d=0;d<a.length;d++)c=a[d],c=getWrapperNode(c,"child closed"),b.appendChild(c);return b}c=a.childNodes[0];return c=getWrapperNode(c,b)},onClickHandler=function(a){a.preventDefault();a.stopPropagation();a=a.target;var b=a.id;b.includes("selectorsHubEleContainer")||(document.querySelector(".selectedNode")&&("dark"===themeName?document.querySelector(".selectedNode").style.backgroundColor=
"#2f2d2d8c":document.querySelector(".selectedNode").style.backgroundColor="white",document.querySelector(".selectedNode").classList.remove("selectedNode")),a.classList.add("selectedNode"),a.style.backgroundColor="#9e9e9e5c");var c=a.className;c.includes("open-tag-label")||c.includes("close-tag-label")||0==c.length||b.includes("selectorsHubEleContainer")||(c?-1!==c.indexOf("open")?(a.classList.remove("open"),a.classList.add("closed")):(a.classList.add("open"),a.classList.remove("closed")):a.classList.add("open"))},
addClickHandlers=function(){document.querySelector("#selectorsHubEleContainer").addEventListener("click",onClickHandler)};const selectorInput=document.querySelector(".jsSelector");
var clickToCopyBoxValue=function(){var a=document.querySelector(".header-copy-btn");a.addEventListener("click",copyBoxValueToClipboard);a.addEventListener("click",showCopied)},copyBoxValueToClipboard=function(){document.querySelector(".selectors-input.jsSelector").select();document.execCommand("Copy")},clickToCopyRelXpath=function(){var a=document.querySelector(".relXpathCopyBtn");a.addEventListener("click",copyRelXpathToClipboard);a.addEventListener("click",showCopied)},copyRelXpathToClipboard=function(){copyToClipboard(".valueSelector.rel")},
clickToCopyIframeXpath=function(){var a=document.querySelector(".iframeCopyBtn");a.addEventListener("click",copyIframeXpathToClipboard);a.addEventListener("click",showCopied)},copyIframeXpathToClipboard=function(){copyToClipboard(".valueSelector.iframeXpath")},clickToCopySuggestedXpath=function(){var a=document.querySelector(".suggestedCopyBtn");a.addEventListener("click",copySuggestedXpathToClipboard);a.addEventListener("click",showCopied)},copySuggestedXpathToClipboard=function(){copyToClipboard(".valueSelector.suggestedXpath")},
clickToCopyIndexXpath=function(){var a=document.querySelector(".indexXpathCopyBtn");a.addEventListener("click",copyIndexXpathToClipboard);a.addEventListener("click",showCopied)},copyIndexXpathToClipboard=function(){copyToClipboard(".valueSelector.indexXpath")},clickToCopyId=function(){var a=document.querySelector(".idCopyBtn");a.addEventListener("click",copyIdToClipboard);a.addEventListener("click",showCopied)},copyIdToClipboard=function(){copyToClipboard(".valueSelector.id")},clickToCopyClassName=
function(){var a=document.querySelector(".classNameCopyBtn");a.addEventListener("click",copyClassNameToClipboard);a.addEventListener("click",showCopied)},copyClassNameToClipboard=function(){copyToClipboard(".valueSelector.className")},clickToCopyName=function(){var a=document.querySelector(".nameCopyBtn");a.addEventListener("click",copyNameToClipboard);a.addEventListener("click",showCopied)},copyNameToClipboard=function(){copyToClipboard(".valueSelector.name")},clickToCopyTagName=function(){var a=
document.querySelector(".tagNameCopyBtn");a.addEventListener("click",copyTagNameToClipboard);a.addEventListener("click",showCopied)},copyTagNameToClipboard=function(){copyToClipboard(".valueSelector.tagName")},clickToCopyTestRigorPath=function(){var a=document.querySelector(".testRigorPathCopyBtn");a.addEventListener("click",copyTestRigorPathToClipboard);a.addEventListener("click",showCopied)},copyTestRigorPathToClipboard=function(){copyToClipboard(".valueSelector.testRigorPath")},clickToCopyAbsXpath=
function(){var a=document.querySelector(".absCopyBtn");a.addEventListener("click",copyAbsXpathToClipboard);a.addEventListener("click",showCopied)},copyAbsXpathToClipboard=function(){copyToClipboard(".valueSelector.abs")},clickToCopyCss=function(){var a=document.querySelector(".cssCopyBtn");a.addEventListener("click",copyCssToClipboard);a.addEventListener("click",showCopied)},copyCssToClipboard=function(){copyToClipboard(".valueSelector.css")},clickToCopyJQuery=function(){var a=document.querySelector(".jQueryCopyBtn");
a.addEventListener("click",copyJQueryToClipboard);a.addEventListener("click",showCopied)},copyJQueryToClipboard=function(){copyToClipboard(".valueSelector.jQuery")},clickToCopyJsPath=function(){var a=document.querySelector(".jsPathCopyBtn");a.addEventListener("click",copyJsPathToClipboard);a.addEventListener("click",showCopied)},copyJsPathToClipboard=function(){copyToClipboard(".valueSelector.jsPath")},clickToCopyLinkText=function(){var a=document.querySelector(".linkTextCopyBtn");a.addEventListener("click",
copyLinkTextToClipboard);a.addEventListener("click",showCopied)},copyLinkTextToClipboard=function(){copyToClipboard(".valueSelector.linkText")},clickToCopyPartialLinkText=function(){var a=document.querySelector(".partialLinkTextCopyBtn");a.addEventListener("click",copyPartialLinkTextToClipboard);a.addEventListener("click",showCopied)},copyPartialLinkTextToClipboard=function(){copyToClipboard(".valueSelector.partialLinkText")},clickToCopyAxesXpath=function(){var a=document.querySelector(".axesXpathCopyBtn");
a.addEventListener("click",copyAxesXpathToClipboard);a.addEventListener("click",showCopied)},copyAxesXpathToClipboard=function(){copyToClipboard(".axesXpath.axesXpathEditBtn")},clickToCopyLastAxesXpath=function(){var a=document.querySelector(".lastAxesXpathCopyBtn");a.addEventListener("click",copyLastAxesXpathToClipboard);a.addEventListener("click",showCopied)},copyLastAxesXpathToClipboard=function(){copyToClipboard(".lastAxesXpath.lastAxesXpathEditBtn")};nestedCodeCopyBtn=document.querySelector(".copy-btn.box.nestedCodeCopyBtn");
nestedCodeCopyBtn.addEventListener("click",function(){trackEvent("copy nested code");nestedCodeCopyBtn.classList.contains("notPatron")&&(patronModal.style.display="block");nestedCodeCopyBtn.classList.contains("notPatron")||(copyNestedCodeToClipboard(),showCopied())});var copyNestedCodeToClipboard=function(){copyToClipboard("#nestedCode")};
function addPreCommandInSelector(a){var b=preCommandInput.value.trim();b=b.replace(/\u201c/g,'"').replace(/\u201d/g,'"');(a=b.includes("xpathvalue")||b.includes("xpathValue")?b.includes("xpathvalue")?b.replace("xpathvalue",a):b.replace("xpathValue",a):b?b.split('"')[0]+'"'+a+'"'+b.split('"')[2]:a)&&a.toLowerCase().includes("selectorname")&&(b=(b=testRigorPathWithCount[0].replaceAll('"',""))?b.slice(0,30):b,a=a.replace(/selectorname/i,toCamelCase(b)));return a}
preCommandInput.addEventListener("keyup",onUpdateCommand);
function onUpdateCommand(a){trackEvent("Entered driver command");a=preCommandInput.value.trim();a=a.replace(/\u201c/g,'"').replace(/\u201d/g,'"');browserType.storage.local.set({preCommandValue:a},function(){});if(a&&!a.toLowerCase().includes("xpathvalue"))preCommandInput.classList.add("wrongXpath"),addDriverCommand.classList.add("inactive"),addDriverCommand.classList.remove("active");else if(!a||a.toLowerCase().includes("xpathvalue"))preCommandInput.classList.remove("wrongXpath"),addDriverCommand.classList.add("active"),
addDriverCommand.classList.remove("inactive")}browserType.storage.local.get(["preCommandValue"],function(a){preCommandInput.value=void 0==a.preCommandValue?'driver.findElement(By.xpath("xpathvalue"))':a.preCommandValue});
function copyToClipboard(a){trackEvent("Copy "+a);a=document.querySelector(a);let b=document.createElement("textarea");b.id="t";b.style.height=0;document.body.appendChild(b);b.value=""==a.innerText?a.textContent:a.innerText;document.querySelector("#t").select();document.execCommand("copy");document.body.removeChild(b)}
function copyAllRowsToClipboard(a){let b=document.createElement("textarea");b.id="t";b.style.height=0;document.body.appendChild(b);if(multiSelectorRecordBtn.className.includes("grey"))for(var c=1;c<a.length;c++){let d=1<a[c].innerText.split(" ").length?a[c].innerText.split(" ").slice(1).join(""):a[c].innerText;b.value=b.value+"\n"+(3<d.length?d:"")}else for(c=1;c<a.length;c++)b.value=b.value+"\n"+a[c].innerText;document.querySelector("#t").select();document.execCommand("copy");document.body.removeChild(b)}
var clickToEditIframeXpath=function(){document.querySelector(".iframeEditBtn").addEventListener("click",editIframeXpath)},editIframeXpath=function(){editSelector(".valueSelector.iframeXpath")},clickToEditSuggestedXpath=function(){document.querySelector(".suggestedEditBtn").addEventListener("click",editSuggestedXpath)},editSuggestedXpath=function(){editSelector(".valueSelector.suggestedXpath")},clickToEditRelXpath=function(){document.querySelector(".relXpathEditBtn").addEventListener("click",editRelXpath)},
editRelXpath=function(){editSelector(".valueSelector.rel")},clickToEditIndexXpath=function(){document.querySelector(".indexXpathEditBtn").addEventListener("click",editIndexXpath)},editIndexXpath=function(){editSelector(".valueSelector.indexXpath")},clickToEditId=function(){document.querySelector(".idEditBtn").addEventListener("click",editId)},editId=function(){editSelector(".valueSelector.id")},clickToEditName=function(){document.querySelector(".nameEditBtn").addEventListener("click",editName)},editName=
function(){editSelector(".valueSelector.name")},clickToEditClassName=function(){document.querySelector(".classNameEditBtn").addEventListener("click",editClassName)},editClassName=function(){editSelector(".valueSelector.className")},clickToEditAbsXpath=function(){document.querySelector(".absEditBtn").addEventListener("click",editAbsXpath)},editAbsXpath=function(){editSelector(".valueSelector.abs")},clickToEditCss=function(){document.querySelector(".cssEditBtn").addEventListener("click",editCss)},editCss=
function(){editSelector(".valueSelector.css")},clickToEditJQuery=function(){document.querySelector(".jQueryEditBtn").addEventListener("click",editJQuery)},editJQuery=function(){editSelector(".valueSelector.jQuery")},clickToEditJsPath=function(){document.querySelector(".jsPathEditBtn").addEventListener("click",editJsPath)},editJsPath=function(){editSelector(".valueSelector.jsPath")},clickToEditLinkText=function(){document.querySelector(".linkTextEditBtn").addEventListener("click",editLinkText)},editLinkText=
function(){editSelector(".valueSelector.linkText")},clickToEditPartialLinkText=function(){document.querySelector(".partialLinkTextEditBtn").addEventListener("click",editPartialLinkText)},editPartialLinkText=function(){editSelector(".valueSelector.partialLinkText")},clickToEditTagName=function(){document.querySelector(".tagNameEditBtn").addEventListener("click",editTagName)},editTagName=function(){editSelector(".valueSelector.tagName")},clickToEditTestRigorPath=function(){document.querySelector(".testRigorPathEditBtn").addEventListener("click",
editTestRigorPath)},editTestRigorPath=function(){editSelector(".valueSelector.TestRigorPath")},clickToEditAxesXpath=function(){document.querySelector(".axesXpathEditBtn").addEventListener("click",editAxesXpath)},editAxesXpath=function(){editSelector(".axesXpath.axesXpathEditBtn")},clickToEditLastAxesXpath=function(){document.querySelector(".lastAxesXpathEditBtn").addEventListener("click",editLastAxesXpath)},editLastAxesXpath=function(){editSelector(".lastAxesXpath.lastAxesXpathEditBtn")},editSelector=
function(a){trackEvent("Edit "+a);a=document.querySelector(a).innerText;var b=document.querySelector(".jsSelector");b.value=a;b.focus()};function buttonMouseHover(a,b){a.style.outline="0.01em solid #73bde2f2";a.style.backgroundImage="url('../icons/"+b+"')"}function buttonMouseOut(a,b){"dark"===themeName&&(b=b.replace("grey","orange"));a.style.boxShadow="";a.style.outline="";a.style.backgroundImage="url('../icons/"+b+"')"}
function selectorContainerMouseHover(a){a.style.backgroundColor="dark"===themeName?"#6f6868":"#e3e8ea"}function selectorContainerMouseOut(a){a.style.backgroundColor=""}var buttons=document.querySelectorAll("button");toggleElement.addEventListener("click",function(){trackEvent("Right Toggle");toggleAction();generateSelectors()});
function toggleAction(){var a=document.querySelector(".infoPlaceholder div"),b=document.querySelector(".infoPlaceholder"),c=document.querySelector(".shub-generator"),d=document.querySelector(".configOptions");document.querySelector(".selectorsHubEleContainer");var e=document.querySelector("#nestedCode"),f=document.querySelector("#nestedBlock");toggleElement.classList.contains("inactive")?(a.style.display="none",b.style.display="none",browserType.storage.local.set({toggleElement:"active"},function(){}),
toggleElement.classList.remove("inactive"),toggleElement.classList.add("active"),c.style.display="block",d.style.visibility="visible"):(a.style.display="block",b.style.display="block",browserType.storage.local.set({toggleElement:"inactive"},function(){}),toggleElement.classList.remove("active"),toggleElement.classList.add("inactive"),c.style.display="none",d.style.visibility="hidden",f.style.display="none",e.textContent="",clearElements());a=document.querySelector(".toggle.toolTip");toggleElement.classList.contains("inactive")?
(a.textContent="Turn on to get auto generated selectors.",connectBackgroundPage.postMessage({name:"highlight-element",tabId:browserType.devtools.inspectedWindow.tabId,xpath:["xpath","",!1]})):a.textContent="Turn off auto generated selectors."}browserType.storage.local.get(["classChecked"],function(a){classAttr.checked=void 0==a.classChecked?!0:a.classChecked});browserType.storage.local.get(["nameChecked"],function(a){nameAttr.checked=void 0==a.nameChecked?!0:a.nameChecked});
browserType.storage.local.get(["placeholderChecked"],function(a){placeholderAttr.checked=void 0==a.placeholderChecked?!0:a.placeholderChecked});browserType.storage.local.get(["idChecked"],function(a){idAttr.checked=void 0==a.idChecked?!0:a.idChecked});browserType.storage.local.get(["textCheckboxChecked"],function(a){textCheckbox.checked=void 0==a.textCheckboxChecked?!0:a.textCheckboxChecked});
textCheckbox.addEventListener("click",function(){trackEvent("text checkbox");browserType.storage.local.set({textCheckboxChecked:textCheckbox.checked},function(){});axesBtn.classList.contains("inactive")&&(generateSelectors(),generateSuggestedXpath(),updateGAEvents())});
idAttr.addEventListener("click",function(){trackEvent("Id attr checkbox");browserType.storage.local.set({idChecked:idAttr.checked},function(){});axesBtn.classList.contains("inactive")&&(generateSelectors(),generateSuggestedXpath(),updateGAEvents())});classAttr.addEventListener("click",function(){trackEvent("class checkbox");browserType.storage.local.set({classChecked:classAttr.checked},function(){});axesBtn.classList.contains("inactive")&&(generateSelectors(),generateSuggestedXpath(),updateGAEvents())});
nameAttr.addEventListener("click",function(){trackEvent("name checkbox");browserType.storage.local.set({nameChecked:nameAttr.checked},function(){});axesBtn.classList.contains("inactive")&&(generateSelectors(),generateSuggestedXpath(),updateGAEvents())});
placeholderAttr.addEventListener("click",function(){trackEvent("Placeholder checkbox");browserType.storage.local.set({placeholderChecked:placeholderAttr.checked},function(){});axesBtn.classList.contains("inactive")&&(generateSelectors(),generateSuggestedXpath(),updateGAEvents())});function generateSuggestedXpath(){document.querySelector(".jsSelector").value&&selectElements("xpath",!0)}function updateCPSteps(a){deleteAllRow();a?a.forEach(b=>{insRow(b)}):CPsteps.forEach(b=>{insRow(b)})}
function updateColumnsColor(a){for(var b=document.querySelector("#multiSelectorRow").getElementsByTagName("th"),c="dark"==themeName?"#ff9800":"black",d=0;d<b.length;d++)b[d].style.backgroundColor=a,b[d].style.color=c}
var multiSelectorRecordBtn=document.querySelector(".multiSelectorRecordBtn"),selectorsGenerator=document.querySelector(".selectorsGenerator"),selectorsHubEleContainer=document.querySelector("#selectorsHubEleContainer"),multiSelectorContainer=document.querySelector("#multiSelectorContainer"),multiSelectorRow=document.querySelector("#multiSelectorRow"),copyAll=document.querySelector(".copyAll"),deleteAll=document.querySelector(".deleteAll"),insertRow=!1,xpathWithoutCommand="",cssSelectorWithoutCommand=
"",idWithoutCommand="",nameWithoutCommand="",classNameWithoutCommand="",linkTextWithoutCommand="",partialLinkTextWithoutCommand="",tagNameWithoutCommand="";
multiSelectorRecordBtn.addEventListener("click",function(){trackEvent("generate automation code");deActivateAllButtons(multiSelectorRecordBtn);deleteAllRow();document.querySelector(".selectorName-header").style.display="table-cell";selectorHeader.innerText="Selectors";tablePlaceholder.textContent="1. Inspect elements or click on DOM nodes one by one to record xpaths for many. \r\n2. Turn on the driver command to generate xpath with command. \r\n3. Set the attribute to generate xpath with required attribute. \r\n4. Click on Copy All button present in header to copy all xpaths. \r\n5. Click on export icon to download the recorded data.";tutorialVideoLink.setAttribute("href",
"http://bit.ly/SH_Code");tutorialTooltip.textContent="Watch Tutorial.";resetSmart.textContent="click for smart maintenance.";smartFix.classList.remove("defaultsmartFix");openSelectorModal.style.display="none";importButton.style.display="none";copyAll.style.left="45px";deleteAll.style.left="75px";updateColumnsColor("dark"==themeName?"#174440":"#969290");var a="xpath",b=document.querySelector(".capture.toolTip"),c=document.querySelector(".configOptions"),d=document.querySelector("#nestedBlock");multiSelectorRecordBtn.className.includes("grey")?
(multiSelectorRecordBtn.classList.add("red"),multiSelectorRecordBtn.classList.remove("grey"),b.textContent="click to stop recording and go back to home page.",c.style.marginTop="5px"):(setElementContainerHeight(),multiSelectorRecordBtn.classList.add("grey"),multiSelectorRecordBtn.classList.remove("red"),b.textContent="Click to generate Locators Page & Multiple Selectors.");selectElements(a,!1);if(multiSelectorRecordBtn.className.includes("red")&&toggleElement.classList.contains("active")){selectorsGenerator.style.display=
"none";try{selectorsHubEleContainer.style.display="none"}catch(e){}multiSelectorContainer.style.display="block";browserType.devtools.panels.elements.onSelectionChanged.addListener(function(){if(multiSelectorRecordBtn.className.includes("red")&&toggleElement.classList.contains("active")){d.style.display="none";insertRow=!0;chooseAttrsOption();var e="",f="",h="",g="",l="",n="",m="",k="",p="";generateSelectors();setTimeout(function(){addDriverCommand.className.includes("inactive")||!preCommandInput.value.trim()?
(e=xpathWithoutCommand,p=cssSelectorWithoutCommand):(e=addPreCommandInSelector(xpathWithoutCommand),e=e.includes("cy.get")?e.replace("cy.get","cy.xpath"):e,p=addPreCommandInSelector(cssSelectorWithoutCommand),p=p.includes("By.xpath")?p.replace("By.xpath","By.cssSelector"):p.includes("By(xpath")?p.replace("By(xpath","By(cssSelector"):p.includes("ByXPath")?p.replace("ByXPath","ByCssSelector"):p.includes("cy.xpath")?p.replace("cy.xpath","cy.get"):p.replace("cy.Xpath","cy.get"));try{h=idWithCount[0]}catch(r){}try{g=
nameWithCount[0]}catch(r){}try{l=classNameWithCount[0]}catch(r){}try{n=linkTextWithCount[0]}catch(r){}try{m=partialLinkTextWithCount[0]}catch(r){}try{k=tagNameWithCount[0]}catch(r){}f=label?label.slice(0,30):label;if(insertRow&&multiSelectorRecordBtn.className.includes("red")&&toggleElement.classList.contains("active")&&void 0!==e){var q=[];q.push({selectorName:f,selector:e,cssSelector1:p,idWithoutCommand,nameWithoutCommand,classNameWithoutCommand,linkTextWithoutCommand,partialLinkTextWithoutCommand,
tagNameWithoutCommand});CPstepsWithOutCmd.push({selectorName:f,selector:xpathWithoutCommand,cssSelector1:cssSelectorWithoutCommand,idWithoutCommand,nameWithoutCommand,classNameWithoutCommand,linkTextWithoutCommand,partialLinkTextWithoutCommand,tagNameWithoutCommand});CPsteps.push({selectorName:f,selector:e,cssSelector1:p,id:h,name:g,className:l,linkText:n,partialLinkText:m,tagName:k});insRow(q[0])}tagNameWithoutCommand=partialLinkTextWithoutCommand=linkTextWithoutCommand=classNameWithoutCommand=nameWithoutCommand=
idWithoutCommand=cssSelectorWithoutCommand=""},100)}});updateCPSteps()}else multiSelectorContainer.style.display="none",a="xpath",selectorsGenerator.style.display="block",selectorsHubEleContainer.style.display="block","yes"==nestedBlockOn&&(d.style.display="block"),setElementContainerHeight();"none"===driverCommandBox.style.display?document.querySelector("#selectorHeader").textContent="XPath":document.querySelector("#selectorHeader").textContent="Command";a=document.querySelectorAll(".selector-header");
for(b=0;b<a.length-1;b++)a[b+1].style.display="table-cell"});var smartFix=document.getElementById("smartFix"),selectorHeader=document.getElementById("selectorHeader"),tablePlaceholder=document.querySelector(".tablePlaceholder"),resetSmart=document.querySelector(".resetSmart"),tutorialVideoLink=document.querySelector(".tutorialVideoLink"),tutorialTooltip=document.querySelector(".codeTutorial.tooltip");smartFix.addEventListener("click",smartFixScreen,!0);trackEvent("verify multiple Xpath");
function smartFixScreen(a){deActivateAllButtons(this);axesXpathGenerator.style.display="none";uiConfig.style.display="none";deleteAllRow();multiSelectorRecordBtn.className.includes("grey")||multiSelectorRecordBtn.click();a=document.querySelector("#multiSelectorContainer");var b=document.querySelector(".selectorsGenerator"),c=document.querySelector("#selectorsHubEleContainer"),d=document.querySelector(".selectorName-header"),e=document.querySelector("#nestedBlock");b.style.display="none";c.style.display=
"none";a.style.display="block";d.style.display="none";selectorHeader.innerText="XPath";tablePlaceholder.innerText=smartFixPlaceholder;openSelectorModal.style.display="";importButton.style.display="";multiSelectorRow.style.backgroundColor="#ffdca0";copyAll.style.left="20px";deleteAll.style.left="60px";updateColumnsColor("dark"==themeName?"#4b4c4c":"#ffdca0");tutorialVideoLink.setAttribute("href","https://bit.ly/smartmain");tutorialTooltip.textContent="Smart Maintenance Tutorial";this.classList.contains("defaultsmartFix")?
(b.style.display="block",c.style.display="block",document.querySelector("#multiSelectorContainer").style.display="none",resetSmart.textContent="XPath Healing: Click to verify all the XPaths of script.",this.classList.remove("defaultsmartFix"),"yes"==nestedBlockOn&&(e.style.display="block")):(this.classList.add("defaultsmartFix"),resetSmart.textContent="click to go back to home page",appendRows(SMsteps,!0));a=document.querySelectorAll(".selector-header");for(b=0;b<a.length-1;b++)a[b+1].style.display=
"none"}
function insRow(a){var b=document.querySelector("#recordedSelectorTable tbody"),c=b.rows[0].cloneNode(!0);c.style.display="";var d=b.rows.length,e=c.cells[1];e.id+=d;e.innerHTML=a.selectorName;e.addEventListener("input",function(g){g.data?this.classList.remove("removeOutline"):this.classList.add("removeOutline")});for(var f=2;10>f;f++){e=c.cells[f];e.id+=d;e.addEventListener("input",function(g){g.data?addDriverCommand.classList.contains("inactive")&&(g=this.parentNode.rowIndex,CPstepsWithOutCmd[g-f].selector=
this.innerText,CPsteps[g-f].selector=this.innerText,this.classList.remove("removeOutline")):this.classList.add("removeOutline")});try{var h=2==f?a.selector:3==f?a.cssSelector1:4==f?a.idWithoutCommand:5==f?a.nameWithoutCommand:6==f?a.classNameWithoutCommand:7==f?a.linkTextWithoutCommand:8==f?a.partialLinkTextWithoutCommand:a.tagNameWithoutCommand;h&&h.toLowerCase().includes("selectorname")&&(h=h.replace(/selectorname/i,toCamelCase(a.selectorName)));e.textContent=h;e.setAttribute("title",h)}catch(g){e.textContent=
""}}b.appendChild(c);insertRow=!1;updateTotalRowsCount()}function toCamelCase(a){return a.replace(/_/g," ").replace(/\s(.)/g,function(b){return b.toUpperCase()}).replace(/\s/g,"").replace(/^(.)/,function(b){return b.toLowerCase()})}
document.querySelector("#recordedSelectorTable").addEventListener("click",function(a){var b=a.target.parentNode.parentNode.rowIndex;a.target.id.includes("delButton")?deleteRow(b):"row-copy-btn"===a.target.id?(a=a.target.parentNode.parentNode.querySelector(".selectorValue").id,copyToClipboard("#"+a),showCopied()):"row-edit-btn"===a.target.id?(a=a.target.parentNode.parentNode.querySelector(".selectorValue").innerText,document.querySelector(".jsSelector").focus(),document.execCommand("selectAll"),document.execCommand("insertText",
!1,a)):"row-edit-btn-SM"===a.target.id?(a=a.target.parentNode.parentNode.querySelector(".selectorValue").innerText,a=1<a.split(" ").length?a.split(" ").slice(1).join(""):a,document.querySelector(".jsSelector").focus(),document.execCommand("selectAll"),document.execCommand("insertText",!1,a)):"row-copy-btn-SM"===a.target.id&&(a=a.target.parentNode.parentNode.querySelector(".selectorValue").id,copyToClipboardSM("#"+a),showCopied())});
function copyToClipboardSM(a){a=document.querySelector(a);let b=document.createElement("textarea");b.id="t";b.style.height=0;document.body.appendChild(b);b.value=1<a.innerText.split(" ").length?a.innerText.split(" ").slice(1).join(""):a.innerText;document.querySelector("#t").select();document.execCommand("copy");document.body.removeChild(b)}
function rowAction(a){for(var b=document.querySelectorAll("#"+a),c=b.length,d=0;d<c;d++)b[d].addEventListener("click",function(){a.includes("delButton")?deleteRow(this):a.includes("copy")||a.includes("edit")})}
function deleteRow(a){document.getElementById("recordedSelectorTable").deleteRow(a);updateTotalRowsCount();smartFix.classList.contains("defaultsmartFix")&&(SMstepsWithOutCmd.splice(a-2,1),SMsteps.splice(a-2,1),appendRows(SMsteps,!0));multiSelectorRecordBtn.className.includes("red")&&(CPstepsWithOutCmd.splice(a-2,1),CPsteps.splice(a-2,1))}
function deleteAllRow(){for(var a=document.querySelectorAll("#recordedSelectorTable tr"),b=2;b<a.length;b++)document.getElementById("recordedSelectorTable").deleteRow(2);document.querySelector(".tablePlaceholder").style.display="";updateTotalRowsCount()}var copyAllBtnXPath=document.querySelector(".copyAllBtn.xpath"),copyAllBtnCssSelector=document.querySelector(".copyAllBtn.cssSelector");
copyAllBtnXPath.addEventListener("click",function(){trackEvent("copy all xpaths");var a=document.querySelectorAll(".xpath.selectorValue");copyAllBtnXPath.classList.contains("notPatron")&&(patronModal.style.display="block");copyAllBtnXPath.classList.contains("notPatron")||(copyAllRowsToClipboard(a),showCopied())});
copyAllBtnCssSelector.addEventListener("click",function(){trackEvent("copy all css selectors");var a=document.querySelectorAll(".cssSelector.selectorValue");copyAllBtnCssSelector.classList.contains("notPatron")&&(patronModal.style.display="block");copyAllBtnCssSelector.classList.contains("notPatron")||(copyAllRowsToClipboard(a),showCopied())});var deleteAllBtn=document.querySelector(".deleteAllBtn");
deleteAllBtn.addEventListener("click",function(){trackEvent("Delete All btn");deleteAllRow();smartFix.classList.contains("defaultsmartFix")&&(SMstepsWithOutCmd=[],SMsteps=[]);multiSelectorRecordBtn.className.includes("red")&&(CPsteps=[],CPstepsWithOutCmd=[])});function showXpathValueAlertMsg(a){let b=document.querySelector("#xpathValueAlertMsg");b.innerText=a;b.className="show";setTimeout(function(){b.className=b.className.replace("show","")},2E3)}var exportButton=document.querySelector(".exportButton");
exportButton.addEventListener("click",function(){trackEvent("Click on Export btn");1<document.getElementById("tableBody").rows.length?exportSelectors("recordedSelectorTable","SelectorsHub"):showXpathValueAlertMsg("No data to save")});
function openFileUploadModal(){browserType.storage.local.get(["preCommandValue"],async function(a){customCommand.value=void 0==await a.preCommandValue?'driver.findElement(By.xpath("xpathvalue"))':a.preCommandValue});submitBtn.textContent="Next";document.getElementById("enterNewTitle").style.display="none";document.getElementById("addNewTitle").textContent="Upload XPath file";document.getElementById("imgupload").style.display="none";document.getElementById("addNewModal").style.display="block";document.getElementById("enterNewTitle").focus();
document.getElementById("enterNewTitle").value="";checkXpathInputCommand()}function checkXpathInputCommand(){setTimeout(()=>{customCommand.value.toLowerCase().includes("xpathvalue")?customCommand.value.toLowerCase().includes("xpathvalue")&&(customCommand.classList.remove("wrongXpath"),customCommand.style.backgroundColor="#FFF"):(customCommand.classList.add("wrongXpath"),customCommand.style.backgroundColor="#FF6347")},100)}var fileToRead=document.getElementById("imgupload");
fileToRead.addEventListener("change",function(a){a=a.target.files[0];var b=new FileReader,c=document.getElementById("customCommand").value.trim(),d=document.getElementById("inputToggle");b.onload=function(e){var f=XLSX.read(e.target.result,{type:"binary"});f.SheetNames.forEach(function(h){h=XLSX.utils.sheet_to_row_object_array(f.Sheets[h]);document.querySelector(".selectorName-header").style.display="none";d.classList.contains("active")?(SMsteps=[...h].map(g=>{let l=g.Label;g=extractXpath(g.XPath||
g.Command,c);return{Step:l,XPath:g}}),SMstepsWithOutCmd=[...SMsteps],appendRows(SMsteps,!0)):(SMsteps=[...h],SMstepsWithOutCmd=SMsteps.map(g=>({Step:g.Label,XPath:g.XPath||g.Command})),appendRows(SMstepsWithOutCmd,!0))})};b.onerror=function(e){console.error("File could not be read! Code "+e.target.error.code)};b.readAsBinaryString(a);fileToRead.value=""},!1);function nodesColor(a){switch(a){case 0:return"nodesCountZero";case 1:return"nodesCountOne";default:return"nodesCountDef"}}
function appendRows(a,b){deleteAllRow();try{a.forEach(async(c,d)=>{var e=document.getElementById("tableBody").rows.length;tableBody.innerHTML+=`
                <tr>
                    <td id='s-no'></td>
                    <td style="display:${b?"none":""}" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" contenteditable="true" id="labelData${e}" class="label editableContent">
                        ${c[Object.keys(c)[0]]||""}
                    </td>
                    <td autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false" contenteditable="true" id="selectorData${e}" class="xpath selectorValue editableContent">
                        <span class='nodesCount' id="occurrence${e}">0</span>
                        ${c.XPath||""}
                    </td>
                    <td id="actionBtn">
                        <button id="row-copy-btn-SM" title="click to copy relative XPath"></button>
                        <button id="row-edit-btn-SM" title="click to edit relative XPath"></button>
                        <button id="delButton"></button>
                    </td>
                </tr>
            `;c={xpath:SMstepsWithOutCmd[d].XPath||"",id:`occurrence${e}`};c=JSON.stringify(c);await browserType.devtools.inspectedWindow.eval("calculateElements(`"+c+"`)",{useContentScriptContext:!0},function(f){appendElementsCount(f)});addChangeHandler(`occurrence${e}`)}),updateTotalRowsCount()}catch(c){console.warn(c,"Error")}}
function addChangeHandler(a){let b=document.getElementById(a);b.parentNode.addEventListener("input",function(c){if(c.data){if(addDriverCommand.classList.contains("inactive")){c=this.parentNode.rowIndex;var d=this.innerText.slice(this.innerText.indexOf(" ")+1);SMstepsWithOutCmd[c-2].XPath=d;SMsteps[c-2].XPath=d;b.parentElement.classList.remove("removeOutline")}}else b.parentElement.classList.add("removeOutline")})}
function updateNodesCount(a,b,c){try{var d=document.querySelectorAll(".nodesCount");if(a)for(b=0;b<d.length;b++)d[b].innerHTML="";else for(a=0;a<d.length;a++)d[a].innerHTML=b[a]}catch(e){console.warn(e,"ERR")}}
function downloadFile(a,b,c,d,e,f,h,g,l,n){var m=[],k=[],p=window.navigator.userAgent.includes("Windows");c.forEach((t,v)=>{smartFix.classList.contains("defaultsmartFix")?(m=[["XPath","Occurrence"]],k.push([t,b[v]])):(m="none"===preCommandInput.style.display?["Label XPath cssSelector id name class linkText partialLinkText tagName".split(" ")]:["Label Command cssSelector id name class linkText partialLinkText tagName".split(" ")],k.push([a[v],t,d[v],e[v],f[v],h[v],g[v],l[v],n[v]]))});var q="";m.concat(k).forEach(function(t){t=
smartFix.classList.contains("defaultsmartFix")?[`${t[0]}`.trim(),`${t[1]}`]:[`${t[0]}`.trim(),`${t[1]}`,`${t[2]}`,`${t[3]}`,`${t[4]}`,`${t[5]}`,`${t[6]}`,`${t[7]}`,`${t[8]}`];q+=p?t.join(","):t.join("\t");q+="\n"});var r=window.document.createElement("a");r.href=window.URL.createObjectURL(new Blob([q],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;"}));connectBackgroundPage.postMessage({name:"url",tabId:browserType.devtools.inspectedWindow.tabId});var u=getFormattedTime();
setTimeout(function(){r.download=p?pageDomainName+"-"+u+".csv":pageDomainName+"-"+u+".xls";document.body.appendChild(r);r.click();document.body.removeChild(r);r=null},100)}
var exportSelectors=function(a,b){a=document.querySelectorAll(".xpath.selectorValue");var c=document.querySelectorAll(".nodesCount"),d=document.querySelectorAll(".label");b=document.querySelectorAll(".cssSelector.selectorValue");let e=document.querySelectorAll(".id.selectorValue"),f=document.querySelectorAll(".name.selectorValue"),h=document.querySelectorAll(".class.selectorValue"),g=document.querySelectorAll(".linkText.selectorValue"),l=document.querySelectorAll(".partialLinkText.selectorValue"),
n=document.querySelectorAll(".tagName.selectorValue"),m=[],k=[],p=[],q=[],r=[],u=[],t=[],v=[],w=[],x=[];for(let y=1;y<d.length;y++)m.push(d[y].textContent.trim());for(d=0;d<c.length;d++)k.push(c[d].textContent);updateNodesCount(!0,k,m);for(c=1;c<a.length;c++)p.push(a[c].textContent),smartFix.classList.contains("defaultsmartFix")||(q.push(b[c].textContent),r.push(e[c].textContent),u.push(f[c].textContent),t.push(h[c].textContent),v.push(g[c].textContent),w.push(l[c].textContent),x.push(n[c].textContent));
downloadFile(m,k,p,q,r,u,t,v,w,x);updateNodesCount(!1,k,m)};function updateSMSteps(){SMsteps=SMstepsWithOutCmd.map(a=>({Step:a.Step,XPath:preCommandInput.value.trim().replace(/xpathvalue/i,a.XPath)}));appendRows(SMsteps,!0)}
function updateselectorsHubSteps(){var a=preCommandInput.value,b=a.includes("cy.get")?a.replace("cy.get","cy.xpath"):a,c=a.includes("By.xpath")?a.replace("By.xpath","By.cssSelector"):a.includes("By(xpath")?a.replace("By(xpath","By(cssSelector"):a.includes("ByXPath")?a.replace("ByXPath","ByCssSelector"):a.includes("cy.xpath")?a.replace("cy.xpath","cy.get"):a.replace("cy.Xpath","cy.get");CPsteps=CPstepsWithOutCmd.map(d=>({selectorName:d.selectorName,selector:b?b.trim().replace(/xpathvalue/i,d.selector):
d.selector,cssSelector1:c?c.trim().replace(/xpathvalue/i,d.cssSelector1):d.cssSelector1,idWithoutCommand:d.idWithoutCommand,nameWithoutCommand:d.nameWithoutCommand,classNameWithoutCommand:d.classNameWithoutCommand,linkTextWithoutCommand:d.linkTextWithoutCommand,PartialLinkTextWithoutCommand:d.PartialLinkTextWithoutCommand,tagNameWithoutCommand:d.tagNameWithoutCommand}));updateCPSteps(CPsteps)}
var driverCommandBox=document.querySelector(".driver-command-box"),addDriverCommand=document.querySelector(".addDriverCommand"),totalCount=document.querySelector(".total-match-count");
addDriverCommand.addEventListener("click",async function(){trackEvent("set driver command");var a=preCommandInput.value,b=a.includes("cy.get")?a.replace("cy.get","cy.xpath"):a,c=a.includes("By.xpath")?a.replace("By.xpath","By.cssSelector"):a.includes("By(xpath")?a.replace("By(xpath","By(cssSelector"):a.includes("ByXPath")?a.replace("ByXPath","ByCssSelector"):a.includes("cy.xpath")?a.replace("cy.xpath","cy.get"):a.replace("cy.Xpath","cy.get");if("none"===driverCommandBox.style.display)browserType.storage.local.set({driverCommandBox:"active"},
function(){}),document.querySelector("#selectorHeader").textContent="Command",preCommandInput.value.toLowerCase().includes("xpathvalue")&&(addDriverCommand.classList.remove("inactive"),addDriverCommand.classList.add("active")),addDriverCommand.style.backgroundImage="url('../icons/addDriverCommand_blue.svg')",driverCommandBox.style.display="block",preCommandInput.value.toLowerCase().includes("xpathvalue")&&(smartFix.classList.contains("defaultsmartFix")?(a=preCommandInput.value.split('"')[0],!SMsteps[0]||
SMsteps[0].XPath.includes(a)||SMsteps[0].XPath.toLowerCase().includes(a)||(SMsteps=SMsteps.map(d=>({Step:d.Step,XPath:preCommandInput.value.trim().replace(/xpathvalue/i,d.XPath)})),appendRows(SMsteps,!0))):multiSelectorRecordBtn.classList.contains("red")&&(a=preCommandInput.value.split('"')[0],CPstepsWithOutCmd[0]&&!CPstepsWithOutCmd[0].selector.includes(a)&&(CPsteps=CPstepsWithOutCmd.map(d=>({selectorName:d.selectorName,selector:b.trim().replace(/xpathvalue/i,d.selector),cssSelector1:c.trim().replace(/xpathvalue/i,
d.cssSelector1),idWithoutCommand:d.idWithoutCommand,nameWithoutCommand:d.nameWithoutCommand,classNameWithoutCommand:d.classNameWithoutCommand,linkTextWithoutCommand:d.linkTextWithoutCommand,PartialLinkTextWithoutCommand:d.PartialLinkTextWithoutCommand,tagNameWithoutCommand:d.tagNameWithoutCommand})),updateCPSteps(CPsteps))),selectorsGenerator.offsetParent&&preCommandInput.value.toLowerCase().includes("xpathvalue")&&removeAttrCommands(!0)),preCommandInput.value&&!preCommandInput.value.toLowerCase().includes("xpathvalue")&&
preCommandInput.classList.add("wrongXpath");else if(browserType.storage.local.set({driverCommandBox:"inactive"},function(){}),document.querySelector("#selectorHeader").textContent="XPath",addDriverCommand.classList.remove("active"),addDriverCommand.classList.add("inactive"),addDriverCommand.style.backgroundImage="url('../icons/addDriverCommand_"+iconColor+".svg')",driverCommandBox.style.display="none",preCommandInput.value.toLowerCase().includes("xpathvalue")){if(smartFix.classList.contains("defaultsmartFix")){a=
SMsteps.map(e=>e.XPath);let d=await extractAllCommands(preCommandInput.value.trim(),a.join(""));SMsteps=SMsteps.map((e,f)=>({selectorName:e.selectorName,XPath:d[f][1]}));appendRows(SMsteps,!0)}else multiSelectorRecordBtn.classList.contains("red")&&(CPsteps=CPstepsWithOutCmd.map((d,e)=>({selectorName:d.selectorName,selector:d.selector,cssSelector1:d.cssSelector1,idWithoutCommand:d.idWithoutCommand,nameWithoutCommand:d.nameWithoutCommand,classNameWithoutCommand:d.classNameWithoutCommand,linkTextWithoutCommand:d.linkTextWithoutCommand,
PartialLinkTextWithoutCommand:d.PartialLinkTextWithoutCommand,tagNameWithoutCommand:d.tagNameWithoutCommand})),updateCPSteps(CPsteps));selectorsGenerator.offsetParent&&removeAttrCommands(!1)}});browserType.storage.local.get(["driverCommandBox"],function(a){"active"==a.driverCommandBox&&(driverCommandBox.style.display="block",addDriverCommand.classList.remove("inactive"),addDriverCommand.classList.add("active"))});
async function removeAttrCommands(a){var b=document.querySelectorAll(".valueSelector"),c=document.querySelector(".rel");if(a){for(a=0;a<b.length;a++)if(b[a].offsetParent&&!b[a].classList.contains("rel")&&!b[a].classList.contains("jsPath")&&!b[a].classList.contains("testRigorPath")){var d=b[a].dataset.command,e=preCommandInput.value.trim();e=e.includes("cy.xpath")?e.replace("cy.xpath","cy.get"):e;e.toLowerCase().indexOf("xpath")!==e.toLowerCase().lastIndexOf("xpath")&&(e=e.trim().replace("xpath",d));
!b[a].textContent.includes(e.replace(/\u201d|\u201c/g,'"').replace("xpath",b[a].dataset.command).replace(/\u201d|\u201c/g,'"').split('"')[0])&&(b[a].textContent=e.replace(/xpathvalue/i,b[a].textContent),d.includes("xpath")||d.includes("index"))&&(b[a].textContent=b[a].textContent.replace("cy.get","cy.xpath"),b[a].textContent=b[a].textContent.replace("indexXpath","xpath"))}c.offsetParent&&(c=preCommandInput.value.toLowerCase().trim().indexOf("xpathvalue"),b=preCommandInput.value.trim().length-c,c=
preCommandInput.value.trim().slice(0,c),b=preCommandInput.value.trim().slice(-b+10),c=c.includes("cy.get")?c.replace("cy.get","cy.xpath"):c,document.querySelector(".p1-label").textContent=c,document.querySelector(".p2-label").textContent=b)}else{for(a=0;a<b.length;a++)if(b[a].offsetParent&&!b[a].classList.contains("rel")&&!b[a].classList.contains("jsPath")){d=b[a].textContent;d=d.includes("cy.xpath")?d.replace("cy.xpath","cy.get"):d;e=b[a].dataset.command;e=e.includes("cy.xpath")?e.replace("cy.xpath",
"cy.get"):e;e=e.includes("indexXpath")?e.replace("indexXpath","xpath"):e;let f=preCommandInput.value.trim();f=f.includes("cy.xpath")?f.replace("cy.xpath","cy.get"):f;f.toLowerCase().indexOf("xpath")!==f.toLowerCase().lastIndexOf("xpath")&&(f=f.trim().replace(/xpath/i,e));d=await extractAllCommands(f.replace(/\u201d|\u201c/g,'"'),d.replace(/\u201d|\u201c/g,'"'));d[0]&&(b[a].textContent=d[0][1])}c.offsetParent&&(document.querySelector(".p1-label").textContent="",document.querySelector(".p2-label").textContent=
"")}}var settingBtn=document.querySelector(".setting_btn");attributeFilter=document.querySelector(".attributeFilter");function updateTotalRowsCount(){var a=document.querySelector(".selectorsCount"),b=document.querySelectorAll("#recordedSelectorTable tr").length-2;0<b?document.querySelector(".tablePlaceholder").style.display="none":document.querySelector(".tablePlaceholder").style.display="";a.innerText="("+b+")"}
window.addEventListener("click",function(a){setElementContainerHeight();setWidthOfSelectorHeader();a=document.querySelectorAll("td");for(var b=0;b<a.length;b++)a[b].scrollLeft=0;event.target.className.includes("edit")||event.target.className.includes("savedSelectorRow")||"block"!=savedSelectors.style.display||(savedSelectors.style.display="none");event.target==patronModal&&(patronModal.style.display="none");a=document.querySelector(".savedCommand");b=document.querySelector(".driver-command");"block"==
a.style.display&&event.target!=b&&(a.style.display="none")},!0);
function showCopied(){var a="";a=event.target.className;var b=document.querySelector(".copyToolTip");b.textContent=a.includes("editorContent")?"Selector value is copied.":a.includes("iframe")?"iframe XPath is copied":a.includes("suggested")?"SH suggested selector is copied":a.includes("rel")?"Rel XPath is copied":a.includes("id")?"id is copied":a.includes("name")?"name is copied":a.includes("tagName")?"tagName is copied":a.includes("class")?"className is copied":a.includes("abs")?"Abs XPath is copied":
a.includes("css")?"copied cssSelector":a.includes("linkText")?"copied linkText":a.includes("partialLinkText")?"copied partialLinkText":a.includes("plus-btn")?"Value is saved here.":a.includes("domContentCopyBtn")?"Value copied in the clipboard.":"copied selector's value";event.target.id.includes("row-copy-btn")?(b.style.right="62px",a=event.target.parentNode.offsetTop+87):event.target.className.includes("copyAllBtn")?(b.style.left="216px",a="113px",b.style.marginLeft="82px"):event.target.className.includes("nestedCodeCopyBtn")?
(b.textContent="Code is copied.",a="205px"):(a=event.target.offsetTop+1,b.style.left="24px");b.classList.add("show");b.style.top=a;setTimeout(function(){b.classList.remove("show");b.style.left="";b.style.right=""},800)}
record.addEventListener("click",function(){openedRecorder?connectBackgroundPage.postMessage({name:"active",tabId:browserType.devtools.inspectedWindow.tabId,index:null}):(this.classList.add("redBlink"),connectBackgroundPage.postMessage({name:"openStudio",tabId:browserType.devtools.inspectedWindow.tabId,index:null}))});var resetConfigBtn=document.querySelector(".reset-btn");
resetConfigBtn.addEventListener("click",function(){trackEvent("reset settings");deActivateAllButtons();var a=document.querySelector(".selectorsGenerator"),b=document.querySelector("#selectorsHubEleContainer");a.style.display="block";b.style.display="block";idAttr.checked=!0;classAttr.checked=!0;nameAttr.checked=!0;placeholderAttr.checked=!0;userAttrName.value="";textCheckbox.checked=!0;preCommandInput.value="";attributeFilter.style.display="block";attrFilterBtn.classList.remove("inactive");attrFilterBtn.classList.add("active");
quotesBtn.classList.remove("active");quotesBtn.classList.add("inactive");autosuggestToggleElement.classList.remove("inactive");autosuggestToggleElement.classList.add("active");addDriverCommand.classList.remove("active");addDriverCommand.classList.add("inactive");driverCommandBox.style.display="none";expandBtn.classList.remove("active");expandBtn.classList.add("inactive");browserType.storage.local.set({expandBtn:"inactive"},function(){});document.querySelector(".selectorsGenerator").style.maxHeight=
"125px";expandBtnToolTip.textContent="Click to expand selectors block to see all generated selectors.";browserType.storage.local.set({classChecked:!0},function(){});browserType.storage.local.set({nameChecked:!0},function(){});browserType.storage.local.set({placeholderChecked:!0},function(){});browserType.storage.local.set({idChecked:!0},function(){});browserType.storage.local.set({textCheckboxChecked:!0},function(){});browserType.storage.local.set({userAttrName:userAttrName.value.trim()},function(){});
browserType.storage.local.set({preCommandValue:preCommandInput.value},function(){});browserType.storage.local.set({toggleElement:"active"},function(){});browserType.storage.local.set({attributeFilter:"active"},function(){});browserType.storage.local.set({quotesBtn:"inactive"},function(){});browserType.storage.local.set({autosuggestToggleElement:"active"},function(){});browserType.storage.local.set({toggleElement:"active"},function(){});browserType.storage.local.set({driverCommandBox:"inactive"});
cssSelectorChoice.checked=!0;relXpathChoice.checked=!0;suggestedXpathChoice.checked=!0;indexXpathChoice.checked=!0;idChoice.checked=!0;nameChoice.checked=!0;classNameChoice.checked=!0;jQueryChoice.checked=!0;jsPathChoice.checked=!0;linkTextChoice.checked=!0;partialLinkTextChoice.checked=!0;absXpathChoice.checked=!0;tagNameChoice.checked=!0;autoSuggestToggleChoice.checked=!0;autoGenerateToggleChoice.checked=!0;caseInsensitiveBtnChoice.checked=!0;browserType.storage.local.set({cssSelectorChoice:"yes"},
function(){});browserType.storage.local.set({relXpathChoice:"yes"},function(){});browserType.storage.local.set({suggestedXpathChoice:"yes"},function(){});browserType.storage.local.set({indexXpathChoice:"yes"},function(){});browserType.storage.local.set({idChoice:"yes"},function(){});browserType.storage.local.set({nameChoice:"yes"},function(){});browserType.storage.local.set({classNameChoice:"yes"},function(){});browserType.storage.local.set({jQueryChoice:"yes"},function(){});browserType.storage.local.set({jsPathChoice:"yes"},
function(){});browserType.storage.local.set({linkTextChoice:"yes"},function(){});browserType.storage.local.set({partialLinkTextChoice:"yes"},function(){});browserType.storage.local.set({absXpathChoice:"yes"},function(){});browserType.storage.local.set({tagNameChoice:"yes"},function(){});browserType.storage.local.set({autoSuggestToggleChoice:"yes"},function(){});browserType.storage.local.set({autoGenerateToggleChoice:"yes"},function(){});browserType.storage.local.set({caseInsensitiveBtnChoice:"yes"},
function(){});document.querySelector("#autosuggestToggle").style.visibility="visible";document.querySelector(".toggle-btn").style.visibility="visible";document.querySelector(".ignoreCaseBtn").style.visibility="visible";a=document.querySelectorAll(".selectorsRow");for(b=2;b<a.length;b++)a[b].style.display="block";debugTimeBox.value=5;browserType.storage.local.set({debugTime:5},function(){})});
function setWidthOfSelectorHeader(){setTimeout(function(){for(var a=1,b=document.querySelectorAll(".noOfMatch"),c=0;c<b.length;c++)a=b[c].textContent.length>a?b[c].textContent.length:a;for(c=0;c<b.length;c++)document.querySelectorAll(".noOfMatch")[c].style.width=3===a?"35px":4===a?"41px":5===a?"47px":"26px",document.querySelectorAll(".typeOfLocator")[c].style.width=2===a?"77px":3===a?"76px":4===a?"69px":5===a?"62px":"90px"},150)}
var openSelectorModal=document.querySelector(".openSelectorModal"),importButton=document.querySelector(".importButton"),closeIcon=document.getElementById("modalClose"),cancelBtn=document.getElementById("modal-cancel"),submitBtn=document.getElementById("btn-submit"),commandValidation=document.getElementById("commandValidation"),customCommand=document.getElementById("customCommand"),changeIconButton=document.getElementById("changeIconButton");openSelectorModal.addEventListener("click",openDataUploadModal);
importButton.addEventListener("click",function(){openUploadModal=!0;openFileUploadModal()});
function openDataUploadModal(){trackEvent("Click to paste all xpath");browserType.storage.local.get(["preCommandValue"],function(a){customCommand.value=void 0==a.preCommandValue?'driver.findElement(By.xpath("xpathvalue"))':a.preCommandValue});submitBtn.textContent="Submit";openUploadModal=!1;document.getElementById("enterNewTitle").style.display="block";document.getElementById("imgupload").style.display="none";document.getElementById("addNewTitle").textContent="Paste whole selectors page or script";
document.getElementById("btn-submit").classList.add("submit-case");document.getElementById("btn-submit").classList.remove("submit-case");document.getElementById("addNewModal").style.display="block";document.getElementById("enterNewTitle").focus();document.getElementById("enterNewTitle").value="";checkXpathInputCommand()}function closeAddNewModal(){document.getElementById("addNewModal").style.display="none"}closeIcon.addEventListener("click",function(){closeAddNewModal()},!1);
cancelBtn.addEventListener("click",function(){closeAddNewModal()},!1);function extractXpath(a,b){if(b){var c=b.toLowerCase().indexOf("xpathvalue");return 0>c?null:a.slice(c,-(b.length-c)+10)}return a}function escapeRegExp(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function findMatches(a,b,c=[]){const d=a.exec(b);d&&c.push(d)&&findMatches(a,b,c);return c}
function extractAllCommands(a,b){try{return new Promise((c,d)=>{if(a){var e=a.toLowerCase().indexOf("xpathvalue");d=a.length-e;e=escapeRegExp(a.slice(0,e));d=escapeRegExp(a.slice(-d+10));d=findMatches(new RegExp(e+"(.*?)"+d,"gi"),b);c(d)}})}catch(c){rej(c)}}customCommand.addEventListener("keyup",clearInputField,!1);submitBtn.addEventListener("click",showXpaths,!1);
function clearInputField(a){commandValidation.style.display="none";browserType.storage.local.set({preCommandValue:a.target.value},function(){});this.value.toLowerCase().includes("xpathvalue")?this.value.toLowerCase().includes("xpathvalue")&&(commandValidation.style.display="none",this.classList.remove("wrongXpath"),this.style.backgroundColor="#FFF"):(commandValidation.style.display="inline-block",this.classList.add("wrongXpath"),this.style.backgroundColor="#FF6347")}
async function showXpaths(){multiSelectorRecordBtn.className.includes("grey")||smartFixScreen();if(document.getElementById("inputToggle").classList.contains("active")&&!document.getElementById("customCommand").value.toLowerCase().includes("xpathvalue"))commandValidation.style.display="inline-block";else if(!document.getElementById("enterNewTitle").offsetParent||document.getElementById("enterNewTitle").value.length)if(document.getElementById("customCommand").value.trim()||!document.getElementById("inputToggle").classList.contains("active")){if(openUploadModal)document.querySelector("#imgupload").click();
else{let b=document.getElementById("enterNewTitle").value.replace(/\u201d|\u201c/g,'"');var a=document.getElementById("customCommand").value.trim().replace(/\u201d|\u201c/g,'"');if(document.getElementById("inputToggle").classList.contains("inactive"))SMsteps=b.trim().split(/\r?\n/).filter(c=>""!==c).map(c=>({Step:"-",XPath:c})),document.querySelector(".selectorName-header").style.display="none",selectorHeader.innerText="XPath",tablePlaceholder.innerText=smartFixPlaceholder,resetSmart.textContent=
"click to go back to default view",tutorialVideoLink.setAttribute("href","https://bit.ly/smartmain"),tutorialTooltip.textContent="Smart Maintenance Tutorial",SMstepsWithOutCmd=[...SMsteps],appendRows(SMsteps,!0);else if(a=await extractAllCommands(a,b))b.trim().split(/\r?\n/),SMsteps=a.map(c=>({Step:"-",XPath:c[1]})),document.querySelector(".selectorName-header").style.display="none",selectorHeader.innerText="XPath",tablePlaceholder.innerText=smartFixPlaceholder,resetSmart.textContent="click to go back to default view",
tutorialVideoLink.setAttribute("href","https://bit.ly/smartmain"),tutorialTooltip.textContent="Smart Maintenance Tutorial",SMstepsWithOutCmd=[...SMsteps],appendRows(SMsteps,!0);b.value=""}closeAddNewModal()}}var inputToggle=document.getElementById("inputToggle");
inputToggle.addEventListener("click",function(){this.classList.contains("active")?(this.classList.remove("active"),this.classList.add("inactive"),document.getElementById("customCommand").style.display="none",commandValidation.style.display="none"):(this.classList.remove("inactive"),this.classList.add("active"),document.getElementById("customCommand").style.display="block")});var indexFlag=!1;
function autocomplete(a,b){function c(h){if(!h)return!1;for(var g=0;g<h.length;g++)h[g].classList.remove("autocomplete-active");e>=h.length&&(e=0);0>e&&(e=h.length-1);try{h[e].classList.add("autocomplete-active"),h[e].scrollIntoView()}catch(l){}}function d(h){for(var g=document.getElementsByClassName("autocomplete-items"),l=0;l<g.length;l++)h!=g[l]&&h!=a&&g[l].parentNode.removeChild(g[l])}var e,f=document.querySelector(".autosuggest-toggle-btn");a.addEventListener("input",function(h){savedSelectors.style.display=
"none";if(f.classList.contains("active")){n=[];var g,l=this.value,n=[];if(l.charAt(0).includes("/")||l.charAt(0).includes("(")||l.substr(0,2).includes("./")){n=bothListOfTextAndAttr[1];bothListOfTextAndAttr[3]&&(indexFlag?n[n.length-1]=bothListOfTextAndAttr[3].split("-sanjayXpathIndex-")[0]+bothListOfTextAndAttr[3].split("-sanjayXpathIndex-")[1]:(n.push(bothListOfTextAndAttr[3].split("-sanjayXpathIndex-")[0]+bothListOfTextAndAttr[3].split("-sanjayXpathIndex-")[1]),indexFlag=!0));var m="xpath"}else n=
bothListOfTextAndAttr[2],m="css";var k=0;k=h.target.selectionStart;k=2<k?k:2;d();if(!l)return!1;e=-1;h=document.createElement("DIV");h.setAttribute("id",this.id+"autocomplete-list");h.style="position: fixed; background-color: #e0e0e0; width: calc(100% - 190px); max-height: 78%; overflow-y: scroll; overflow-x: hidden";h.setAttribute("class","autocomplete-items");this.parentNode.appendChild(h);var p="";for(g=0;g<n.length;g++)if(("."==l.slice(k-2,k-1)||"#"==l.slice(k-2,k-1))&&n[g].split("-sanjayMatchingNode-")[1].substr(0,
1)==l.slice(k-2,k-1)||"."==l.slice(k-1,k)&&n[g].split("-sanjayMatchingNode-")[1].substr(0,1)==l.slice(k-1,k)||"#"==l.slice(k-1,k)&&n[g].split("-sanjayMatchingNode-")[1].substr(0,1)==l.slice(k-1,k)||"["==l.slice(k-1,k)&&n[g].split("-sanjayMatchingNode-")[1].substr(0,1)==l.slice(k-1,k)||":"==l.slice(k-1,k)&&n[g].split("-sanjayMatchingNode-")[1].substr(0,1)==l.slice(k-1,k)||"@"==l.slice(k-1,k)&&n[g].split("-sanjayMatchingNode-")[1].substr(0,1)==l.slice(k-1,k)||n[g].split("-sanjayMatchingNode-")[1].substr(0,
2)==l.slice(k-2,k)||n[g].split("-sanjayMatchingNode-")[1].substr(0,3)==l.slice(k-3,k)||n[g].split("-sanjayMatchingNode-")[1].substr(0,4)==l.slice(k-4,k)||n[g].split("-sanjayMatchingNode-")[1].substr(0,5)==l.slice(k-5,k)){p=n[g].split("-sanjayMatchingNode-")[1];var q=document.createElement("DIV");q.style="color: black; white-space: nowrap; text-overflow: ellipsis; width: 98%; overflow: hidden;-o-text-overflow: ellipsis;-ms-text-overflow: ellipsis;-moz-binding: url('ellipsis.xml#ellipsis');";isFirefox&&
(q.style.padding="5px");var r=n[g].split("-sanjayMatchingNode-")[0]?n[g].split("-sanjayMatchingNode-")[0]:"",u=n[g].split("-sanjayMatchingNode-")[1];q.setAttribute("id","auto"+m);q.setAttribute("value",u);q.setAttribute("title","");if(r){var t="0"==r?"node0":"1"==r?"node1":"abc"==r?"nodeHide":"node2";t=(OS.includes("mac")?"suggestedMatchingNode":"suggestedMatchingNodeWindows")+" "+t;q.innerHTML="<span title='matching node' class='"+t+"'>"+r+"</span>"}q.innerHTML+="<strong>"+u.substr(0,l.length)+"</strong>";
q.innerHTML+=u.substr(l.length);q.innerHTML+='<input type="hidden" value="'+u+'">';q.addEventListener("click",function(v){u!=this.getAttribute("value")&&"["==u.charAt(0)&&"["!=this.getAttribute("value").charAt(0)&&this.setAttribute("value","["+this.getAttribute("value"));v=(a.value.substr(0,k).substr(0,a.value.substr(0,k).lastIndexOf(p.substr(0,1)))+this.getAttribute("value")).length;if("(/"==p.substr(0,2))a.value=this.getAttribute("value");else if(2==k&&"/"==p.substr(0,1)){var w="/"==a.value.substr(k-
1)?"":a.value.substr(k-1);a.value=a.value.substr(0,k).substr(0,a.value.substr(0,k).lastIndexOf(p.substr(0,1)))+this.getAttribute("value")+w}else a.value=a.value.substr(0,k).substr(0,a.value.substr(0,k).lastIndexOf(p.substr(0,1)))+this.getAttribute("value")+a.value.substr(k);a.value=a.value.includes("...")?replaceAll(a.value,"...",".."):a.value;w=a.value.match(/\[/g)?a.value.match(/\[/g).length:0;var x=a.value.match(/\]/g)?a.value.match(/\]/g).length:0;w!=x&&(a.value=a.value.includes("[[")?replaceAll(a.value,
"[[","["):a.value,a.value=a.value.includes("]]")?replaceAll(a.value,"]]","]"):a.value);a.value=a.value.includes("////")?replaceAll(a.value,"////","//"):a.value;a.value=a.value.includes("///")?replaceAll(a.value,"///","//"):a.value;a.value=a.value.includes("+ +")?replaceAll(a.value,"+ +","+"):a.value;a.value=a.value.includes("++")?replaceAll(a.value,"++","+"):a.value;"(/"!=p.substr(0,2)&&"]/"!=p.substr(0,2)&&(v="/"==a.value.charAt(k-1)?v-1:v);a.setSelectionRange?(a.focus(),a.setSelectionRange(v,v)):
a.createTextRange&&(w=a.createTextRange(),w.collapse(!0),w.moveEnd("character",v),w.moveStart("character",v),w.select());checkWrongXpath();d()});document.querySelector(".jsTotalMatchCount").innerText.toLowerCase().includes("invalid")||h.appendChild(q)}}});a.addEventListener("keydown",function(h){var g=document.getElementById(this.id+"autocomplete-list");g&&=g.getElementsByTagName("div");40==h.keyCode?(e++,c(g)):38==h.keyCode?(e--,c(g)):13==h.keyCode&&(h.preventDefault(),-1<e&&g&&g[e].click(),d(h))});
document.addEventListener("click",function(h){d(h.target)})}listOfTextAndAttr=["@id","@class","@text","@placeholder","@name"];autocomplete(document.querySelector(".selectors-input.jsSelector"),listOfTextAndAttr);
var evalString="try{var ele =document.querySelector('*[xpathtest]');ele.removeAttribute('xpathtest');}catch(err){};$0.setAttribute('xpathtest','1');",relXpathWithCount="",indexXpathWithCount="",absXpathWithCount="",cssSelectorWithCount="",linkTextWithCount="",partialLinkTextWithCount="",idWithCount="",classNameWithCount="",nameWithCount="",tagNameWithCount="",label="",testRigorPathWithCount="",domContentCopyBtn=document.querySelector(".domContentCopyBtn");
function generateSelectors(){infoPlaceholderParent.style.display="none";ignoreCaseBtn.classList.remove("active");document.querySelector(".editorTitle");var a=document.querySelector(".parentElement"),b=document.querySelector(".childElement"),c="";if(axesBtn.classList.contains("active"))if(a.classList.contains("active")){c=document.querySelector(".axesXpath.axesXpathEditBtn");var d=document.querySelector(".noOfAxesXpathMatch");c.innerText&&(assignLastAxesXpath(),c.innerText="",d.innerText="0",d.style.backgroundColor=
"#f29a00a8");c="parentElement";a.classList.remove("active");a.classList.add("inactive");b.classList.add("active");b.classList.remove("inactive");isFirefox||browserType.devtools.inspectedWindow.eval("assignParentElement($0)",{useContentScriptContext:!0},function(f){})}else b.classList.contains("active")&&(c="childElement",b.classList.remove("active"),b.classList.add("inactive"),a.classList.add("active"),a.classList.remove("inactive"),isFirefox||browserType.devtools.inspectedWindow.eval("createAxesXpathForElement($0)",
{useContentScriptContext:!0},function(f){f&&assignAxesXpath(f)}));chooseAttrsOption();toggleElement=document.querySelector(".toggle-btn");a=toggleElement.classList.contains("inactive")?"onlyEditor":"generatorAndEditor";toggleElement.classList.contains("active")&&multiSelectorRecordBtn.className.includes("red")&&(a="generatorAndEditorRecording");var e=document.querySelector(".elementInfo");isFirefox?(browserType.devtools.inspectedWindow.eval(evalString),e.style.display="none",domContentCopyBtn.style.display=
"none",connectBackgroundPage.postMessage({name:a,chooseAttrs,parentOrChild:c,tabId:browserType.devtools.inspectedWindow.tabId})):(browserType.devtools.inspectedWindow.eval("prepareListOfAttrText($0)",{useContentScriptContext:!0},function(f){domContentCopyBtn.style.display="none";bothListOfTextAndAttr=[];f&&(bothListOfTextAndAttr=f)}),browserType.devtools.inspectedWindow.eval("elementTypeAndInfo($0)",{useContentScriptContext:!0},function(f){e.style.display="none";try{f.elementType&&setElementType(f.elementType),
f.elementInfo&&setElementAlertInfo(f.elementInfo)}catch(h){}}),a.includes("generatorAndEditor")&&browserType.devtools.inspectedWindow.eval('onInspectElementClick($0, "'+chooseAttrs+'", "'+a+'")',{useContentScriptContext:!0},function(f){assignSelectorsValue(f)}));saveCaseSensitiveSelectors()}isFirefox&&browserType.devtools.inspectedWindow.eval(evalString);function replaceAll(a,b,c){return a.split(b).join(c)}attributeFilter=document.querySelector(".attributeFilter");var attrFilterBtn=document.querySelector(".attrFilterBtn");
attrFilterBtn.addEventListener("click",function(a){trackEvent("click set attribute button");"none"===attributeFilter.style.display?(browserType.storage.local.set({attributeFilter:"active"},function(){}),attributeFilter.style.display="block",attrFilterBtn.classList.remove("inactive"),attrFilterBtn.classList.add("active")):(browserType.storage.local.set({attributeFilter:"inactive"},function(){}),attributeFilter.style.display="none",attrFilterBtn.classList.remove("active"),attrFilterBtn.classList.add("inactive"))});
browserType.storage.local.get(["attributeFilter"],function(a){"inactive"==a.attributeFilter&&(attributeFilter.style.display="none",attrFilterBtn.classList.remove("active"),attrFilterBtn.classList.add("inactive"))});
setTimeout(function(){browserType.storage.local.get(["toggleElement"],function(a){var b=document.querySelector(".shub-generator"),c=document.querySelector(".configOptions");"inactive"==a.toggleElement?(toggleElement.setAttribute("data-before","Off"),toggleElement.classList.remove("active"),toggleElement.classList.add("inactive"),b.style.display="none",c.style.visibility="hidden",clearElements(),document.querySelector("#selectorsHubEleContainer").innerHTML="",setElementContainerHeight()):"active"==
a.toggleElement?(toggleElement.setAttribute("data-before","On"),toggleElement.classList.remove("inactive"),toggleElement.classList.add("active"),toggleState(),setElementContainerHeight(),generateSelectors()):(toggleState(),document.querySelector(".infoPlaceholder div").style.display="none",document.querySelector(".infoPlaceholder").style.display="none");a=document.querySelector(".toggle.toolTip");toggleElement.classList.contains("inactive")?a.textContent="Turn on to get auto generated selectors.":
a.textContent="Turn off auto generated selectors."})},90);var patronCloseButton=document.querySelector(".closeIcon"),patronRequest=document.querySelector(".patronRequest"),patronRequestLink=document.querySelector(".patronRequest a"),patronModal=document.querySelector(".patronModal"),patronModalClose=document.querySelector(".patronClose");patronCloseButton.addEventListener("click",function(a){patronModal.style.display="block"});
patronModalClose.addEventListener("click",function(a){patronModal.style.display="none"});var workEmailLink=document.querySelector(".workEmailLink");workEmailLink.addEventListener("click",function(a){patronModal.style.display="none";browserType.storage.local.set({patronModalEmail:"hide"},function(){})});setTimeout(function(){browserType.storage.local.get(["patronModalEmail"],function(a){"hide"==a.patronModalEmail&&(patronModal.style.display="none")})},5E3);
var reviewModalClose=document.querySelector(".reviewClose"),reviewModal=document.querySelector(".reviewModal");reviewModalClose.addEventListener("click",function(a){reviewModal.style.display="none";browserType.storage.local.set({reviewModal:"hide"},function(){})});modalReviewLink2=document.querySelector(".modalReviewLink2");modalReviewLink2.addEventListener("click",function(a){reviewModal.style.display="none";browserType.storage.local.set({reviewModal:"hide"},function(){})});
setTimeout(function(){browserType.storage.local.get(["reviewModal"],function(a){"hide"==a.reviewModal&&(reviewModal.style.display="none")})},3E5);setTimeout(function(){reviewModal.style.display="none"},33E4);var tweetModalClose=document.querySelector(".tweetClose"),tweetModal=document.querySelector(".tweetModal");tweetModalClose.addEventListener("click",function(a){tweetModal.style.display="none";browserType.storage.local.set({tweetModal1:"hide"},function(){})});var tweetLink2=document.querySelector(".tweetLink2");
tweetLink2.addEventListener("click",function(a){tweetModal.style.display="none";browserType.storage.local.set({tweetModal1:"hide"},function(){})});setTimeout(function(){browserType.storage.local.get(["tweetModal1"],function(a){"hide"==a.tweetModal1&&(tweetModal.style.display="none")})},12E4);setTimeout(function(){tweetModal.style.display="none"},15E4);var instructionModalClose=document.querySelector(".instructionClose"),instructionModal=document.querySelector(".instructionModal");
instructionModalClose.addEventListener("click",function(a){instructionModal.style.display="none";browserType.storage.local.set({instructionModal2:"hide"},function(){})});var instructionLink2=document.querySelector(".instructionLink2");instructionLink2.addEventListener("click",function(a){instructionModal.style.display="none";browserType.storage.local.set({instructionModal3:"hide"},function(){})});
setTimeout(function(){browserType.storage.local.get(["instructionModal3"],function(a){instructionModal.style.display="hide"==a.instructionModal3?"none":"block"})},3E4);setTimeout(function(){instructionModal.style.display="none";browserType.storage.local.set({instructionModal3:"hide"},function(){})},5E4);function hideFooter(){countdownBtn.style.display="none";browserType.storage.local.set({patronRequest:"hide"},function(){})}
browserType.storage.local.get(["patronRequest"],function(a){"hide"==a.patronRequest&&(countdownBtn.style.display="none",toggleElement.classList.remove("notPatron"),plusBtn.classList.remove("notPatron"),copyAllBtnXPath.classList.remove("notPatron"),copyAllBtnCssSelector.classList.remove("notPatron"),nestedCodeCopyBtn.classList.remove("notPatron"))});var debugBtn=document.querySelector(".debugBtn");
debugBtn.addEventListener("click",function(){trackEvent("turn on debugger");debugBtn.classList.remove("inactive");debugBtn.classList.add("active");var a=document.querySelector(".choose.debugTime").value;browserType.devtools.inspectedWindow.eval("turnOnDebugger(`"+1E3*a+"`)",{useContentScriptContext:!0},function(b){});setTimeout(function(){debugBtn.classList.remove("active");debugBtn.classList.add("inactive")},15E3)});
var ignoreCaseBtn=document.querySelector(".ignoreCaseBtn"),defaultXpath,defaultCss,userSelector="";ignoreCaseBtn.addEventListener("click",function(){trackEvent("click make selectors sensitive");generateIgnoreCaseSelectors()});
function generateIgnoreCaseSelectors(){var a=document.querySelector(".selectors-input.jsSelector"),b=document.querySelector(".box.valueSelector.rel.relXpathEditBtn"),c=document.querySelector(".box.valueSelector.css.cssEditBtn");ignoreCaseBtn.classList.contains("active")?(ignoreCaseBtn.classList.remove("active"),a.value=userSelector,c.textContent=defaultCss,relXpathWithCount[0]=defaultXpath,generateColoredRelXpath()):(ignoreCaseBtn.classList.add("active"),saveCaseSensitiveSelectors(),a.value&&("/"==
a.value.charAt(0)||a.value.includes("i]")?"/"!=a.value.charAt(0)||a.value.includes("translate(")||(a.value=ignoreCaseInXPath(a.value)):a.value=ignoreCaseInCss(a.value)),b.textContent&&!b.textContent.includes("translate(")&&(relXpathWithCount[0]=ignoreCaseInXPath(b.textContent),generateColoredRelXpath()),c.textContent&&!c.textContent.includes(" i]")&&(c.textContent=ignoreCaseInCss(c.textContent)))}
function saveCaseSensitiveSelectors(){var a=document.querySelector(".selectors-input.jsSelector"),b=document.querySelector(".box.valueSelector.rel.relXpathEditBtn"),c=document.querySelector(".box.valueSelector.css.cssEditBtn");userSelector=a.value;defaultXpath=b.textContent;defaultCss=c.textContent}
function ignoreCaseInXPath(a){var b=a.split("[")[1].includes(",")?a.split("[")[1].split(",")[1]:a.split("[")[1].split("=")[1],c=a;try{a=a.replace(b,b.toLowerCase());var d=a.split("[")[1].includes(",")?a.split("[")[1].split(",")[0]:a.split("[")[1].split("=")[0],e=d.includes("contains")?d.split("contains(")[1]:d.includes("starts-with")?d.split("starts-with(")[1]:d,f="translate(textAttribute, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz')".replace("textAttribute",e);c=a.replace(e,f)}catch(h){}return c}
function ignoreCaseInCss(a){return a=a.replaceAll("]"," i]")}function executeScript(){var a=document.querySelector(".jsSelector").value;try{browserType.devtools.inspectedWindow.eval("executeJs("+a+")",{useContentScriptContext:!0},function(b){b&&(0<b.length&&showAllMatchingNode(b),showTotalResults(b))})}catch(b){console.log(b)}}var autosuggestToggleElement=document.querySelector(".autosuggest-toggle-btn");autosuggestToggleElement.addEventListener("click",function(){trackEvent("Left Toggle");autosuggestToggleAction()});
function autosuggestToggleAction(){var a=document.querySelector(".autosuggest-toggle.toolTip");autosuggestToggleElement.classList.contains("active")?(browserType.storage.local.set({autosuggestToggleElement:"inactive"},function(){}),autosuggestToggleElement.classList.remove("active"),autosuggestToggleElement.classList.add("inactive"),a.textContent="Turn on to get auto suggested functions while typing."):(browserType.storage.local.set({autosuggestToggleElement:"active"},function(){}),autosuggestToggleElement.classList.remove("inactive"),
autosuggestToggleElement.classList.add("active"),a.textContent="Turn off auto suggestions while typing.")}browserType.storage.local.get(["autosuggestToggleElement"],function(a){"inactive"==a.autosuggestToggleElement&&(autosuggestToggleElement.classList.remove("active"),autosuggestToggleElement.classList.add("inactive"))});var quotesBtnToolTip=document.querySelector(".quotes.toolTip");
quotesBtn.addEventListener("click",function(a){trackEvent("generate selectors with double quote");quotesBtn.classList.contains("inactive")?(browserType.storage.local.set({quotesBtn:"active"},function(){}),quotesBtn.classList.remove("inactive"),quotesBtn.classList.add("active"),a=document.querySelector(".box.valueSelector.rel.relXpathEditBtn"),a.textContent&&a.textContent.includes("'")&&!a.textContent.includes('"')&&(a.textContent=a.textContent.replaceAll("'",'"')),a=document.querySelector(".box.valueSelector.css.cssEditBtn"),
a.textContent&&a.textContent.includes("'")&&!a.textContent.includes('"')&&(a.textContent=a.textContent.replaceAll("'",'"')),quotesBtnToolTip.textContent="Click to generate selectors with single quote."):(browserType.storage.local.set({quotesBtn:"inactive"},function(){}),quotesBtn.classList.remove("active"),quotesBtn.classList.add("inactive"),a=document.querySelector(".box.valueSelector.rel.relXpathEditBtn"),a.textContent&&a.textContent.includes('"')&&!a.textContent.includes("'")&&(a.textContent=a.textContent.replaceAll('"',
"'")),a=document.querySelector(".box.valueSelector.css.cssEditBtn"),a.textContent&&a.textContent.includes('"')&&!a.textContent.includes("'")&&(a.textContent=a.textContent.replaceAll('"',"'")),quotesBtnToolTip.textContent="Click to generate selectors with double quote.")});browserType.storage.local.get(["quotesBtn"],function(a){"active"==a.quotesBtn&&(quotesBtn.classList.remove("inactive"),quotesBtn.classList.add("active"),quotesBtnToolTip.textContent="Click to generate selectors with single quote.")});
var expandBtn=document.querySelector(".expand-btn"),expandBtnToolTip=document.querySelector(".expand.toolTip");
expandBtn.addEventListener("click",function(a){trackEvent("expand selectors block");expandBtn.classList.contains("inactive")?(browserType.storage.local.set({expandBtn:"active"},function(){}),expandBtn.classList.remove("inactive"),expandBtn.classList.add("active"),a=document.querySelector(".selectorsGenerator"),a.style.height="auto",a.style.maxHeight="fit-content",expandBtnToolTip.textContent="Click to set default view of selectors block."):(browserType.storage.local.set({expandBtn:"inactive"},function(){}),
expandBtn.classList.remove("active"),expandBtn.classList.add("inactive"),a=document.querySelector(".selectorsGenerator"),a.style.maxHeight="125px",a.style.height="auto",expandBtnToolTip.textContent="Click to expand selectors block to see all generated selectors.")});
browserType.storage.local.get(["expandBtn"],function(a){"active"==a.expandBtn&&(expandBtn.classList.remove("inactive"),expandBtn.classList.add("active"),expandBtnToolTip.textContent="Click to set default view of selectors block.",a=document.querySelector(".selectorsGenerator"),a.style.height="auto",a.style.maxHeight="fit-content")});
var plusBtn=document.querySelector(".plus-btn"),plusBtnToolTip=document.querySelector(".plus.toolTip"),savedSelectors=document.querySelector(".savedSelectors"),selecotrsArray=[];
plusBtn.addEventListener("click",function(a){trackEvent("click save selector ");a=document.querySelector(".jsSelector").value;plusBtn.classList.contains("notPatron")&&(patronModal.style.display="block");if(!plusBtn.classList.contains("notPatron")&&a&&!selecotrsArray.includes(a)&&(document.querySelector(".savedSelectorRow.delete span").innerText="",deleteAllSaved.style.display="inline-block",a&&!selecotrsArray.includes(a))){10<=selecotrsArray.length&&(selecotrsArray.shift(),savedSelectors.removeChild(savedSelectors.childNodes[9]));
selecotrsArray.push(a);var b=createElement("li");b.setAttribute("class","savedSelectorRow");b.setAttribute("title","Click to edit this value in selector box");b.innerHTML=a;b.addEventListener("click",function(c){document.querySelector(".jsSelector").value=b.innerHTML;savedSelectors.style.display="none"});browserType.storage.local.set({selecotrsArray},function(){});savedSelectors.insertBefore(b,savedSelectors.firstChild);showCopied()}});
browserType.storage.local.get(["selecotrsArray"],function(a){if(a.selecotrsArray){selecotrsArray=a.selecotrsArray;a=10<selecotrsArray.length?10:selecotrsArray.length;0<a&&(document.querySelector(".savedSelectorRow.delete span").innerText="",deleteAllSaved.style.display="inline-block");for(var b=0;b<a;b++){var c=createElement("li");c.setAttribute("class","savedSelectorRow");c.setAttribute("title","Click to edit this value in selector box");c.innerHTML=selecotrsArray[b];c.addEventListener("click",function(d){document.querySelector(".jsSelector").value=
d.target.innerHTML;savedSelectors.style.display="none"});savedSelectors.insertBefore(c,savedSelectors.firstChild)}}});editorContent=document.querySelector(".editorContent");editorContent.addEventListener("click",function(a){trackEvent("see saved selectors");"block"==savedSelectors.style.display?savedSelectors.style.display="none":0<savedSelectors.childElementCount&&(savedSelectors.style.display="block")});
function setConfigMargin(){var a=document.querySelector(".selectorsGenerator").offsetHeight-125;20<a?(a=121+a,document.querySelector(".configOptions").style.marginTop="-"+a+"px"):document.querySelector(".configOptions").style.marginTop="-121px"}var deleteAllSaved=document.querySelector(".deleteAllSaved");
deleteAllSaved.addEventListener("click",function(){selecotrsArray=[];browserType.storage.local.set({selecotrsArray},function(){});for(var a=document.querySelector(".savedSelectors"),b=document.querySelectorAll(".savedSelectorRow"),c=0;c<b.length-1;c++)a.removeChild(a.firstChild);document.querySelector(".savedSelectorRow.delete span").innerText="No value is saved.";deleteAllSaved.style.display="none"});
function getFormattedTime(){var a=new Date,b=a.getFullYear(),c=a.getMonth()+1,d=a.getDate(),e=a.getHours(),f=a.getMinutes();a=a.getSeconds();return b+"-"+c+"-"+d+"-"+e+"-"+f+"-"+a}
function toggleState(){var a=document.querySelector(".infoPlaceholder div"),b=document.querySelector(".infoPlaceholder"),c=document.querySelector(".shub-generator"),d=document.querySelector(".configOptions");document.querySelector(".selectorsHubEleContainer");var e=document.querySelector("#nestedCode"),f=document.querySelector("#nestedBlock");toggleElement.classList.contains("inactive")?(a.style.display="none",b.style.display="none",c.style.display="none",d.style.visibility="hidden",f.style.display=
"none",e.textContent="",clearElements()):(a.style.display="none",b.style.display="none",c.style.display="block",d.style.visibility="visible");a=document.querySelector(".toggle.toolTip");toggleElement.classList.contains("inactive")?a.textContent="Turn on to get auto generated selectors.":a.textContent="Turn off auto generated selectors."}var footerCounter=document.querySelector(".footerCounter");
function countdown(){function a(){var c=document.querySelector(".toggle-btn");footerCounter=document.querySelector(".footerCounter");b--;c.setAttribute("data-before","0:"+(10>b?"0":"")+String(b));footerCounter.innerHTML="0:"+(10>b?"0":"")+String(b);if(0<b)setTimeout(a,1E3);else{c=document.querySelector(".shub-generator");var d=document.querySelector(".configOptions");toggleElement.classList.remove("active");toggleElement.classList.add("inactive");c.style.display="none";d.style.visibility="hidden";
clearElements();document.querySelector("#selectorsHubEleContainer").innerHTML="";setElementContainerHeight();patronModal.style.display="block";deActivateAllButtons()}}var b=300;a()}axesBtn=document.querySelector(".axes-btn");var axesXpathGenerator=document.querySelector(".axesXpathGenerator");
axesBtn.addEventListener("click",function(){trackEvent("click axes btn");deActivateAllButtons(axesBtn);var a=document.querySelector(".selectorsGenerator"),b=document.querySelector("#selectorsHubEleContainer"),c=document.querySelector(".uiConfig"),d=document.querySelector("#nestedBlock"),e=document.querySelector(".axes.toolTip");axesBtn.classList.contains("inactive")?(axesBtn.classList.add("active"),axesBtn.classList.remove("inactive"),a.style.display="none",b.style.display="none",setElementContainerHeight(),
axesXpathGenerator.style.display="block",multiSelectorContainer.style.display="none",c.style.display="none",e.textContent="Click to reset and go back to home screen."):(a.style.display="block",b.style.display="block",axesXpathGenerator.style.display="none",axesBtn.classList.add("inactive"),axesBtn.classList.remove("active"),multiSelectorContainer.style.display="none","yes"==nestedBlockOn&&(d.style.display="block"),e.textContent="Click to generate Axes based XPath.");a=document.querySelector(".parentElement");
b=document.querySelector(".childElement");a.classList.remove("inactive");a.classList.add("active");b.classList.add("inactive");b.classList.remove("active")});function assignAxesXpath(a){var b=document.querySelector(".axesXpath.axesXpathEditBtn"),c=document.querySelector(".noOfAxesXpathMatch");a[0]=addDriverCommand.className.includes("inactive")?a[0]:addPreCommandInSelector(a[0]);b.innerText=a[0];c.style.backgroundColor="1"==a[1]?"#0cb9a9":"0"==a[1]?"#ff0000":"#f78f06";c.innerText=a[1]}
function assignLastAxesXpath(){var a=document.querySelector(".axesXpath.axesXpathEditBtn"),b=document.querySelector(".noOfAxesXpathMatch"),c=document.querySelector(".lastAxesXpath.lastAxesXpathEditBtn"),d=document.querySelector(".noOfLastAxesXpathMatch");c.innerText=a.innerText;d.style.backgroundColor="1"==b.innerText?"#0cb9a9":"0"==b.innerText?"#ff0000":"#f78f06";d.innerText=b.innerText}var uiSettingBtn=document.querySelector(".uiSetting-btn"),uiConfig=document.querySelector(".uiConfig");
uiSettingBtn.addEventListener("click",function(){trackEvent("customize UI");deActivateAllButtons(uiSettingBtn);var a=document.querySelector("#nestedBlock"),b=document.querySelector(".selectorsGenerator"),c=document.querySelector("#selectorsHubEleContainer"),d=document.querySelector(".uiSetting.toolTip");uiSettingBtn.classList.contains("inactive")?(uiSettingBtn.classList.add("active"),uiSettingBtn.classList.remove("inactive"),b.style.display="none",c.style.display="none",uiConfig.style.display="block",
multiSelectorContainer.style.display="none",axesXpathGenerator.style.display="none",setElementContainerHeight(),d.textContent="Click to go back to home screen."):(b.style.display="block",c.style.display="block",uiConfig.style.display="none",uiSettingBtn.classList.add("inactive"),uiSettingBtn.classList.remove("active"),multiSelectorContainer.style.display="none","yes"==nestedBlockOn&&(a.style.display="block"),setElementContainerHeight(),d.textContent="Click to customize UI.")});
var cssSelectorChoice=document.querySelector(".choose.cssSelector");cssSelectorChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.cssSelector");cssSelectorChoice.checked?(a.style.display="block",browserType.storage.local.set({cssSelectorChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({cssSelectorChoice:"no"},function(){}))});
browserType.storage.local.get(["cssSelectorChoice"],function(a){"no"==a.cssSelectorChoice&&(cssSelectorChoice.checked=!1)});var relXpathChoice=document.querySelector(".choose.relXpath");relXpathChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.relXpath");relXpathChoice.checked?(a.style.display="block",browserType.storage.local.set({relXpathChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({relXpathChoice:"no"},function(){}))});
browserType.storage.local.get(["relXpathChoice"],function(a){"no"==a.relXpathChoice&&(relXpathChoice.checked=!1)});var testRigorPathChoice=document.querySelector(".choose.testRigorPath");
testRigorPathChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.testRigorPath");testRigorPathChoice.checked?(a.style.display="block",browserType.storage.local.set({testRigorPathChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({testRigorPathChoice:"no"},function(){}))});browserType.storage.local.get(["testRigorPathChoice"],function(a){"no"==a.testRigorPathChoice&&(testRigorPathChoice.checked=!1)});var suggestedXpathChoice=document.querySelector(".choose.suggestedXpath");
suggestedXpathChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.suggestedXpath");suggestedXpathChoice.checked?(a.style.display="block",browserType.storage.local.set({suggestedXpathChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({suggestedXpathChoice:"no"},function(){}))});browserType.storage.local.get(["suggestedXpathChoice"],function(a){"no"==a.suggestedXpathChoice&&(suggestedXpathChoice.checked=!1)});var indexXpathChoice=document.querySelector(".choose.indexXpath");
indexXpathChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.indexXpath");indexXpathChoice.checked?(a.style.display="block",browserType.storage.local.set({indexXpathChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({indexXpathChoice:"no"},function(){}))});browserType.storage.local.get(["indexXpathChoice"],function(a){"no"==a.indexXpathChoice&&(indexXpathChoice.checked=!1)});var idChoice=document.querySelector(".choose.id");
idChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.id");idChoice.checked?(a.style.display="block",browserType.storage.local.set({idChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({idChoice:"no"},function(){}))});browserType.storage.local.get(["idChoice"],function(a){"no"==a.idChoice&&(idChoice.checked=!1)});var nameChoice=document.querySelector(".choose.name");
nameChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.name");nameChoice.checked?(a.style.display="block",browserType.storage.local.set({nameChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({nameChoice:"no"},function(){}))});browserType.storage.local.get(["nameChoice"],function(a){"no"==a.nameChoice&&(nameChoice.checked=!1)});var classNameChoice=document.querySelector(".choose.className");
classNameChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.className");classNameChoice.checked?(a.style.display="block",browserType.storage.local.set({classNameChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({classNameChoice:"no"},function(){}))});browserType.storage.local.get(["classNameChoice"],function(a){"no"==a.classNameChoice&&(classNameChoice.checked=!1)});var jQueryChoice=document.querySelector(".choose.jQuery");
jQueryChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.jQuery");jQueryChoice.checked?(a.style.display="block",browserType.storage.local.set({jQueryChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({jQueryChoice:"no"},function(){}))});browserType.storage.local.get(["jQueryChoice"],function(a){"no"==a.jQueryChoice&&(jQueryChoice.checked=!1)});var jsPathChoice=document.querySelector(".choose.jsPath");
jsPathChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.jsPath");jsPathChoice.checked?(a.style.display="block",browserType.storage.local.set({jsPathChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({jsPathChoice:"no"},function(){}))});browserType.storage.local.get(["jsPathChoice"],function(a){"no"==a.jsPathChoice&&(jsPathChoice.checked=!1)});var linkTextChoice=document.querySelector(".choose.linkText");
linkTextChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.linkText");linkTextChoice.checked?(a.style.display="block",browserType.storage.local.set({linkTextChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({linkTextChoice:"no"},function(){}))});browserType.storage.local.get(["linkTextChoice"],function(a){"no"==a.linkTextChoice&&(linkTextChoice.checked=!1)});var partialLinkTextChoice=document.querySelector(".choose.partialLinkText");
partialLinkTextChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.partialLinkText");partialLinkTextChoice.checked?(a.style.display="block",browserType.storage.local.set({partialLinkTextChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({partialLinkTextChoice:"no"},function(){}))});browserType.storage.local.get(["partialLinkTextChoice"],function(a){"no"==a.partialLinkTextChoice&&(partialLinkTextChoice.checked=!1)});
var absXpathChoice=document.querySelector(".choose.absXpath");absXpathChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.absXpath");absXpathChoice.checked?(a.style.display="block",browserType.storage.local.set({absXpathChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({absXpathChoice:"no"},function(){}))});browserType.storage.local.get(["absXpathChoice"],function(a){"no"==a.absXpathChoice&&(absXpathChoice.checked=!1)});
var tagNameChoice=document.querySelector(".choose.tagName");tagNameChoice.addEventListener("click",function(){var a=document.querySelector(".selectorsRow.tagName");tagNameChoice.checked?(a.style.display="block",browserType.storage.local.set({tagNameChoice:"yes"},function(){})):(a.style.display="none",browserType.storage.local.set({tagNameChoice:"no"},function(){}))});browserType.storage.local.get(["tagNameChoice"],function(a){"no"==a.tagNameChoice&&(tagNameChoice.checked=!1)});
var autoSuggestToggleChoice=document.querySelector(".choose.autoSuggestToggle");autoSuggestToggleChoice.addEventListener("click",function(){var a=document.querySelector("#autosuggestToggle");autoSuggestToggleChoice.checked?(a.style.visibility="visible",browserType.storage.local.set({autoSuggestToggleChoice:"yes"},function(){})):(a.style.visibility="hidden",browserType.storage.local.set({autoSuggestToggleChoice:"no"},function(){}))});
browserType.storage.local.get(["autoSuggestToggleChoice"],function(a){var b=document.querySelector("#autosuggestToggle");"no"==a.autoSuggestToggleChoice&&(autoSuggestToggleChoice.checked=!1,b.style.visibility="hidden")});var autoGenerateToggleChoice=document.querySelector(".choose.autoGenerateToggle");
autoGenerateToggleChoice.addEventListener("click",function(){var a=document.querySelector(".toggle-btn");autoGenerateToggleChoice.checked?(a.style.visibility="visible",browserType.storage.local.set({autoGenerateToggleChoice:"yes"},function(){})):(a.style.visibility="hidden",browserType.storage.local.set({autoGenerateToggleChoice:"no"},function(){}))});
browserType.storage.local.get(["autoGenerateToggleChoice"],function(a){var b=document.querySelector(".toggle-btn");"no"==a.autoGenerateToggleChoice&&(autoGenerateToggleChoice.checked=!1,b.style.visibility="hidden")});var caseInsensitiveBtnChoice=document.querySelector(".choose.caseInsensitiveBtn");
caseInsensitiveBtnChoice.addEventListener("click",function(){var a=document.querySelector(".ignoreCaseBtn");caseInsensitiveBtnChoice.checked?(a.style.visibility="visible",browserType.storage.local.set({caseInsensitiveBtnChoice:"yes"},function(){})):(a.style.visibility="hidden",browserType.storage.local.set({caseInsensitiveBtnChoice:"no"},function(){}))});
browserType.storage.local.get(["caseInsensitiveBtnChoice"],function(a){var b=document.querySelector(".ignoreCaseBtn");"no"==a.caseInsensitiveBtnChoice&&(caseInsensitiveBtnChoice.checked=!1,b.style.visibility="hidden")});
function deActivateAllButtons(a){var b=document.querySelector(".selectorsGenerator"),c=document.querySelector("#selectorsHubEleContainer");b.style.display="none";c.style.display="none";b=document.querySelector(".multiSelectorRecordBtn");a!=b&&(multiSelectorContainer.style.display="none",b.classList.add("grey"),b.classList.remove("red"));b=document.querySelector("#smartFix");a!=b&&(multiSelectorContainer.style.display="none",b.classList.remove("defaultsmartFix"));b=document.querySelector(".uiSetting-btn");
a!=b&&(uiConfig.style.display="none",b.classList.add("inactive"),b.classList.remove("active"));b=document.querySelector(".axes-btn");a!=b&&(axesXpathGenerator.style.display="none",b.classList.add("inactive"),b.classList.remove("active"));document.querySelector("#nestedBlock").style.display="none"}var countdownBtn=document.querySelector(".countdown-btn"),debugTimeBox=document.querySelector(".choose.debugTime");
debugTimeBox.addEventListener("keyup",function(a){a=debugTimeBox.value;browserType.storage.local.set({debugTime:a?a:5},function(){})});browserType.storage.local.get(["debugTime"],function(a){a.debugTime&&(debugTimeBox.value=a.debugTime)});var country,city;
function trackEvent(a){browserType.storage.local.get(["ext_uniq_id"],b=>{fetch(`${API_URL}analytics/trackAd`,{method:"post",headers:{"Content-Type":"application/json"},body:JSON.stringify({extension:EXT_ID,events:[{user_id:b.ext_uniq_id,event_type:a,user_properties:{Cohort:"Test A"},country:countryName,platform,OS}]})})})}window.onload=function(){let a=!1;browserType.cookies.getAll({},function(b){for(const c of b)"selectorshub authentication"==c.name&&(a=!0);a||setCookies()})};
function setCookies(){const a=Math.floor(Date.now()/1E3)+63072E4;isFirefox?browser.storage.sync.set({cookie:"authenticated"},function(){}):chrome.cookies.set({url:"https://selectorshub.com/",name:"selectorshub authentication",value:"true",secure:!0,expirationDate:a})}var driverCommandInput=document.querySelector(".driver-command"),savedCommand=document.querySelector(".savedCommand");
driverCommandInput.addEventListener("click",function(a){"block"==savedCommand.style.display?savedCommand.style.display="none":0<savedCommand.childElementCount&&(savedCommand.style.display="block")});var savedCommandRow=document.querySelectorAll(".savedCommandRow");for(i=0;i<savedCommandRow.length;i++)savedCommandRow[i].addEventListener("click",function(a){driverCommandInput.value=a.target.innerHTML;driverCommandInput.focus();savedCommand.style.display="none"});
function setElementType(a){document.querySelector(".jsSelector");var b=document.querySelector(".selectors-input"),c=document.querySelector(".selector-editor-div");document.querySelector(".selectorsRow.suggestedXpath");var d=document.querySelector(".selectorsRow.iframeXpath"),e=document.querySelector(".editorTitle"),f=document.querySelector(".editorContent"),h=document.querySelector(".total-match-count");let g="frame"==a?!0:"iframe"==a?!0:!1,l="svgelement"===a,n=a.includes("shadowdom"),m=a.includes("notIframe");
if(g||n||l){if(n?(e.innerText="in ShadowDOM",insideShadowDom=!0,c.style.width=selectorEditBoxWidthShadow,f.style.backgroundColor="#2196F3",f.style.color="#ffffff",h.style.marginLeft=totalCountMarginShadow,b.setAttribute("placeholder","Write & verify cssSelector as XPath doesn't support shadowDOM......Click on \u2795 icon to save value \u27a1\ufe0f"),elementInfoAlert('Alert: This element is inside shadow dom which can\'t be accessed through XPath, use cssSelector for it.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a>',
"#2196f3")):l?(f.style.color="#ffffff",c.style.width=selectorEditBoxWidth,e.innerText="SVG element...",n||g||(f.style.backgroundColor="#f5388fb3"),h.style.marginLeft=totalCountMarginSvg,b.setAttribute("placeholder","Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f"),elementInfoAlert('Alert: This is a svg element & it doesn\'t support standard XPath format.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a>',
"#f5388fb3")):(insideFrame=!0,f.style.color="black",e.innerText=" inside iframe ",f.style.backgroundColor="#cccccc",h.style.marginLeft="frame"==a?totalCountMarginFrame:totalCountMarginIframe,b.setAttribute("placeholder","Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f"),elementInfoAlert('Alert: This element is inside iframe. Switch inside iframe to access it through automation.<a class="training" href="https://bit.ly/sh_courses_recordings" target="_blank"> Learn more...</a>',
"#747272")),void 0===a||n||l)d.style.display="none"}else m&&(insideShadowDom=!1,f.style.color="black",e.innerText="XPath/cssSel..",f.style.backgroundColor="dark"===themeName?"#f29a00db":"#f29a00a8",c.style.width=selectorEditBoxWidth,d.style.display="none",h.style.marginLeft=totalCountMargin,b.setAttribute("placeholder","Write & verify XPath & CSS Selector here......Click on \u2795 icon to save value \u27a1\ufe0f"),setElementContainerHeight())}
function setElementAlertInfo(a){a=a.split("elementInfo-")[1];elementInfoAlert(a,"#eb3030")}
function receiveXpathResults(a){try{a.url&&!pageDomainName&&(pageDomainName=a.url.replace(/.+\/\/|www.|\..+/g,""))}catch(k){}document.querySelector(".jsSelector");document.querySelector(".selectors-input");document.querySelector(".selector-editor-div");var b=document.querySelector(".selectorsRow.suggestedXpath"),c=document.querySelector(".selectorsRow.iframeXpath");document.querySelector(".editorTitle");document.querySelector(".editorContent");document.querySelector(".total-match-count");b.style.display=
"none";try{var d=a.axesXpathWithCount}catch(k){}try{var e=a.errorDetail.includes("wrong");var f="iframe"==a.iframeSelector[0]?!0:"frame"==a.iframeSelector[0]?!0:!1;var h=a.suggestedSelector.includes("suggestedSelector");var g=a.indexBasedXpath.includes("sanjayXpathIndex")}catch(k){}d&&assignAxesXpath(d);if(f){f=document.querySelector(".valueSelector.iframeXpath");document.querySelector(".typeOfLocator.box.iframeCopyBtn").innerText=a.iframeSelector[0]+" XPath";f.style.color="white";c.style.display=
a.iframeSelector[1][0]?"block":"none";a.iframeSelector[1][0]=addDriverCommand.className.includes("inactive")?a.iframeSelector[1][0]:addPreCommandInSelector(a.iframeSelector[1][0]);c=a.iframeSelector[2];d=document.querySelector("#nestedCode");var l=document.querySelector("#nestedBlock");if(!(0<c.length)||multiSelectorRecordBtn.classList.contains("red")||smartFix.classList.contains("defaultsmartFix")||uiSettingBtn.classList.contains("active")||axesBtn.classList.contains("active"))insideShadowDom||(l.style.display=
"none",nestedBlockOn="no");else{nestedBlockOn="yes";l.style.display="block";l="//This element is inside "+(c.length+1)+" nested frames.";addDriverCommand.className.includes("inactive");var n=document.querySelector(".driver-command");n=!addDriverCommand.className.includes("inactive")&&n.value?"WebElement":"XPath for";for(var m=1;m<=c.length;m++)c[m-1]=addDriverCommand.className.includes("inactive")?c[m-1]:addPreCommandInSelector(c[m-1]),c[m-1]=c[m-1].replace(";",""),l=l+"\n"+n+" frame"+m+" = "+c[m-
1]+";";m=addDriverCommand.className.includes("inactive")?relXpathWithCount[0]:addPreCommandInSelector(relXpathWithCount[0]);l=l+"\n"+n+" frame"+(c.length+1)+" = "+a.iframeSelector[1][0].replace(";","")+";\n"+n+" inspectedElement = "+m.replace(";","")+";";d.textContent=l}f.innerText=a.iframeSelector[1][0];document.querySelector(".noOfMatch.iframeXpath").textContent=a.iframeSelector[1][1];f=document.querySelector(".noOfMatch.iframeXpath");f.style.backgroundColor="1"==a.iframeSelector[1][1]?"#0cb9a9":
"0"==a.iframeSelector[1][1]?"#ff0000":"#f78f06";setElementContainerHeight()}g&&(bothListOfTextAndAttr[3]=a.indexBasedXpath);h&&(h=document.querySelector(".valueSelector.suggestedXpath"),b.style.display="block",document.querySelector(".choose.suggestedXpath").checked||(b.style.display="none"),void 0===a.suggestedSelector[1]||insideShadowDom||"0"==a.suggestedSelector[1][1]?b.style.display="none":(a.suggestedSelector[1][0]=addDriverCommand.className.includes("inactive")?a.suggestedSelector[1][0]:addPreCommandInSelector(a.suggestedSelector[1][0]),
a.suggestedSelector[1][0]=a.suggestedSelector[1][0].includes("By.xpath")?a.suggestedSelector[1][0].replace("By.xpath","By.cssSelector"):a.suggestedSelector[1][0].includes("By(xpath")?a.suggestedSelector[1][0].replace("By(xpath","By(cssSelector"):a.suggestedSelector[1][0].includes("ByXPath")?a.suggestedSelector[1][0].replace("ByXPath","ByCssSelector"):a.suggestedSelector[1][0].includes("XPath")?a.suggestedSelector[1][0].replace("XPath","CssSelector"):a.suggestedSelector[1][0].includes("Xpath")?a.suggestedSelector[1][0].replace("Xpath",
"CssSelector"):a.suggestedSelector[1][0].includes("XPATH")?a.suggestedSelector[1][0].replace("XPATH","CSSSELECTOR"):a.suggestedSelector[1][0].replace("xpath","cssSelector"),h.innerText=a.suggestedSelector[1][0],document.querySelector(".noOfMatch.suggestedXpath").textContent=a.suggestedSelector[1][1],f=document.querySelector(".noOfMatch.suggestedXpath"),f.style.backgroundColor="1"==a.suggestedSelector[1][1]?"#0cb9a9":"0"==a.suggestedSelector[1][1]?"#ff0000":"#f78f06"),setElementContainerHeight());
if(e)b.style.display="none",highlightWrongXpath(),showTotalResults(a.errorDetail);else{removeWrongXpath();showTotalCount&&showTotalResults(a.allNodes);try{(document.querySelector(".jsSelector").value||1===a.allNodes.length&&!toggleElement.classList.contains("inactive"))&&showAllMatchingNode(a.allNodes)}catch(k){}document.querySelector(".selectorsGenerator").scrollTop=0}}var ctrlDown=!1,focusText1=!1,focusText2=!1,focusText3=!1,shiftKeyDown=!1,shiftKey=16,vKey=86,cKey=67,zKey=90;
function TempStorage(){this.undoStorage=[];this.tempStorage=[];this.push=function(a){this.tempStorage.push(a);this.print()};this.redo=function(a){if(a=this.undoStorage.pop())a.element.value=a.value,this.tempStorage.push(a)};this.pop=function(a){const b=this.tempStorage.pop();b?(b.element.value=b.value,this.undoStorage.push(b)):a&&(a.value="");this.print()};this.print=function(){}}
function onKeyUp(a,b,c,d){if(17==a.keyCode||91==a.keyCode)return ctrlDown&&!shiftKeyDown&&c.value&&b.push({value:c.value,element:c}),ctrlDown=!0,!1;if(a.keyCode==shiftKey)return shiftKeyDown=!0,!1;if(!d)return!1;if(ctrlDown&&shiftKeyDown&&a.keyCode==zKey)b.redo(c);else{if(ctrlDown&&a.keyCode==zKey)return b.pop(c),!1;ctrlDown&&a.keyCode==cKey||ctrlDown&&a.keyCode==vKey||c&&c.value&&b.push({value:c.value,element:c})}}const id1="searchbox1",searchStorage1=new TempStorage,inputBox1=document.getElementById(id1);
inputBox1.onkeydown=a=>onKeyUp(a,searchStorage1,inputBox1,focusText1);inputBox1.onfocus=()=>{focusText1=!0};inputBox1.onblur=()=>{focusText1=!1};const id2="searchbox2",searchStorage2=new TempStorage,inputBox2=document.getElementById(id2);inputBox2.onkeydown=a=>onKeyUp(a,searchStorage2,inputBox2,focusText2);inputBox2.onfocus=()=>{focusText2=!0};inputBox2.onblur=()=>{focusText2=!1};const id3="searchbox3",searchStorage3=new TempStorage,inputBox3=document.getElementById(id3);
inputBox3.onkeydown=a=>onKeyUp(a,searchStorage3,inputBox3,focusText3);inputBox3.onfocus=()=>{focusText3=!0};inputBox3.onblur=()=>{focusText3=!1};document.body.onkeydown=a=>{onKeyUp(a,searchStorage1,inputBox1,focusText1);onKeyUp(a,searchStorage2,inputBox2,focusText2);onKeyUp(a,searchStorage3,inputBox3,focusText3)};document.body.onkeyup=function(a){17==a.keyCode||91==a.keyCode?ctrlDown=!1:16==a.keyCode&&(shiftKeyDown=!1)};
let adsText=document.querySelector(".ads__text"),adsContainer=document.querySelector(".ads__container"),adsViewer=document.querySelector(".ads__viewer"),dummyVariable="";
const buildAdText=(a,b)=>{const c=[];dummyVariable=a;b.forEach((e,f)=>{let h=dummyVariable.split("");h.splice(e.position+7*f,0,"__img__");dummyVariable=h.join("")});a=document.createElement("span");a.innerText=dummyVariable;c.push(a);let d=0;c.forEach(e=>{let f=e.innerText;for(;-1!==e.innerText.indexOf("__img__");)f=f.replace("__img__",`<img src="${b[d].imgURL}" />`),e.innerText=e.innerText.replace("__img__",""),d++;e.innerHTML=f});return c},appendAd=({id:a,ad:b,link:c,images:d,company_name:e,display_time:f,
type:h,action:g,background_color:l,border_color:n,text_color:m})=>{d=buildAdText(b,d);const k=document.createElement("a");k.href=c;k.id=`ad__${a}`;k.className="ad__template";k.setAttribute("target","_blank");k.setAttribute("data-display-time",f);k.setAttribute("title",b);k.style.backgroundColor="#ffffff"===l.toLowerCase()?"transparent":l;k.style.border="1px solid "+n;k.innerHTML+=`
         <div class="status__block" style="${h?`display: block; background-color: ${h.color};`:"display: none;"}">${h?.name}</div>
         <div class="ad__text" style="display: flex; alignItems: center;">
             <div style="color: ${"dark"===themeName?"#eccbcb":m}"></div>
         </div>
         <div class="ad__btn" style="background-color: ${g.background_color}">
             <div class="btn">
             <p style="color: ${g.text_color};">${g.name}</p>
                 <div class="btn__icon">
                     <img src="${g.icon_url}" />
                 </div>
             </div>
             </div>
     `;let p=k.querySelector(".ad__text > div");k.addEventListener("click",()=>{trackEvent(`clicks:${a}:${e}:ext__ads`)});d.forEach(q=>{p.appendChild(q)});adsContainer.appendChild(k)};let currentIndex=1,_ads,sIntervals=[],sInterval=null,sStartTime=0,sTimeRest=0,sIsPaused=!1;
const applyInterval=a=>{const b=document.querySelectorAll(".ad__template");sIntervals.forEach(c=>clearInterval(c));sIntervals=[];sInterval=setInterval(()=>{if(b[currentIndex]){sStartTime=Date.now();let c=document.querySelector(".ad__template.translate__left");b[currentIndex].classList.remove("no__transition");c.querySelector(".ad__text > div").style.animation="none";c.classList.add("hide__left");b[currentIndex].classList.add("translate__left");if(b[currentIndex].clientWidth>=adsContainer.clientWidth){let e=
document.querySelector("#animation-style"),f=`
                        @keyframes scrollText {
                            from{
                                transform: translateX(${b[currentIndex].querySelector(".ad__text").clientWidth+30}px);
                            }
                            to{
                                transform: translateX(-102%);
                            }
                        }`;e.innerHTML=f;b[currentIndex].querySelector(".ad__text > div").style.animation="scrollText 30s linear infinite"}let d=setTimeout(()=>{c.classList.add("no__transition");c.classList.remove("hide__left");c.classList.remove("translate__left");clearTimeout(d);clearInterval(sInterval);sStartTime=0;const e=b[currentIndex].id.replace("ad__",""),f=_ads.filter(h=>h.id===e)[0];currentIndex===b.length-1?currentIndex=0:currentIndex++;sStartTime=Date.now();applyInterval(1E3*f.display_time)},
300)}},a);sIntervals.push(sInterval)},displayAds=a=>{clearInterval(sInterval);sStartTime=0;_ads=a;a.forEach((b,c)=>appendAd(b,c))};let adsViewerPrevBtn=document.querySelector(".navigate__prev__btn"),adsViewerNextBtn=document.querySelector(".navigate__next__btn");adsViewerPrevBtn.addEventListener("click",()=>{let a=document.querySelectorAll(".ad__template");currentIndex-=2;0>currentIndex&&(currentIndex=a.length+currentIndex);applyInterval(400)});adsViewerNextBtn.addEventListener("click",()=>{applyInterval(400)});
browserType.storage.local.get(["ext_uniq_id"],a=>{fetch(`${API_URL}whitelist/${a.ext_uniq_id||""}`).then(b=>b.json()).then(b=>{let c=!0,d=!0,e=!0;b.ipExists&&(c=!b.object.ads_types.includes("ext__ads"),d=!b.object.ads_types.includes("ctx__ads"),e=!b.object.ads_types.includes("links__ads"));b=`${API_URL}ad?published=true&extensions=${EXT_ID}&mode=ext__ads&browsers=${platform}&excluded_countries=${countryName.toLowerCase()}`;const f=`${API_URL}ad?published=true&extensions=${EXT_ID}&mode=ctx__ads&browsers=${platform}&excluded_countries=${countryName.toLowerCase()}`,
h=`${API_URL}ad?published=true&extensions=${EXT_ID}&mode=links__ads&browsers=${platform}&excluded_countries=${countryName.toLowerCase()}`;c&&fetch(b).then(g=>g.json()).then(g=>{if(0<g.ads.length&&g.ads[0].link){displayAds(g.ads);const l=document.querySelectorAll(".ad__template");currentIndex=1;l[0].classList.add("translate__left");let n=l[0].querySelector(".ad__text"),m=document.querySelector("#animation-style");l[0].clientWidth>=adsContainer.clientWidth&&(m.innerHTML=`
                                @keyframes scrollText {
                                    from{
                                        transform: translateX(${n.clientWidth+30}px);
                                    }
                                    to{
                                        transform: translateX(-102%);
                                    }
                                }`,l[0].querySelector(".ad__text > div").style.animation="scrollText 30s linear infinite");sStartTime=Date.now();applyInterval(1E3*g.ads[0].display_time);l.forEach(k=>{k.addEventListener("mouseenter",()=>{sIsPaused||(k.children[1].children[0].style.animationPlayState="paused",clearInterval(sInterval),sTimeRest=Date.now()-sStartTime,sIsPaused=!0)});k.addEventListener("mouseleave",()=>{if(sIsPaused){k.children[1].children[0].style.animationPlayState="running";var p=k.getAttribute("data-display-time");
if(p=parseInt(p))sStartTime=Date.now()-sTimeRest,applyInterval(1E3*p-sTimeRest),sIsPaused=!1}})});g.ads.forEach(k=>{trackEvent(`views:${k.id}:${k.company_name}:ext__ads`)})}}).catch(g=>console.log(g));d?fetch(f).then(g=>g.json()).then(async g=>{await chrome.storage.local.set({ctxads:g});chrome.runtime.sendMessage({action:"updateContextMenu"})}):(chrome.storage.local.remove("ctxads"),chrome.runtime.sendMessage({action:"updateContextMenu"}));e&&fetch(h).then(g=>g.json()).then(g=>{g=g.ads.filter(l=>
"on__uninstall"===l.type);browserType.runtime.setUninstallURL(0<g.length?g[0].link:"https://selectorshub.com/uninstall/")}).catch(g=>console.log(g))})});
