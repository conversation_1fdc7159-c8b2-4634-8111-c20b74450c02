#!/usr/bin/env python3
"""
Debug script to check Instagram login status and page access
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from configparser import ConfigParser
import time
import os

def debug_instagram():
    """Debug Instagram access step by step"""
    
    # Get current working directory
    cwd = os.getcwd()
    
    # Load config
    config_file = "config.ini"
    parser = ConfigParser()
    parser.read(config_file, encoding='utf-8')
    
    core = parser["Program_Data"]["core"]
    profile = parser["Program_Data"]["profile"]
    competitor_link = parser["Program_Data"]["competitor_link"]
    
    print(f"🔍 Debug Instagram Access")
    print(f"Target: {competitor_link}")
    print(f"Chrome Profile: {profile}")
    print("-" * 50)
    
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        print(f"User Data Dir: {user_data_dir}")
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        print("🚀 Starting Chrome browser...")
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Step 1: Go to Instagram main page
        print("\n📱 Step 1: Going to Instagram main page...")
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        current_url = driver.current_url
        page_title = driver.title
        print(f"Current URL: {current_url}")
        print(f"Page Title: {page_title}")
        
        # Check login status on main page
        page_source = driver.page_source.lower()
        login_indicators = [
            "log in" in page_source,
            "sign up" in page_source,
            "login" in current_url,
            "accounts/login" in current_url
        ]
        
        if any(login_indicators):
            print("❌ Not logged in to Instagram")
            print("Please log in manually and run the script again")
            input("Press Enter after logging in...")
        else:
            print("✅ Appears to be logged in to Instagram")
        
        # Step 2: Go to competitor profile
        print(f"\n👤 Step 2: Going to competitor profile...")
        driver.get(competitor_link)
        time.sleep(8)
        
        current_url = driver.current_url
        page_title = driver.title
        print(f"Current URL: {current_url}")
        print(f"Page Title: {page_title}")
        
        # Check if profile loaded
        page_source = driver.page_source.lower()
        
        # More specific checks
        profile_indicators = [
            "followers" in page_source,
            "following" in page_source,
            "posts" in page_source,
            competitor_link.split("/")[-2] in page_source  # Username should be in page
        ]
        
        login_indicators = [
            "accounts/login" in current_url,
            "login/?next=" in current_url,
            ("log in to instagram" in page_source and "followers" not in page_source)
        ]
        
        print(f"\nProfile indicators found: {sum(profile_indicators)}/4")
        print(f"Login indicators found: {sum(login_indicators)}/3")
        
        if any(login_indicators):
            print("❌ Redirected to login page")
            print("You need to log in to Instagram first")
        elif any(profile_indicators):
            print("✅ Profile page loaded successfully")
            
            # Step 3: Look for followers link
            print(f"\n🔍 Step 3: Looking for followers link...")
            
            followers_xpaths = [
                "//span[contains(text(),'followers')]",
                "//span[contains(text(),'follower')]",
                "//a[contains(@href, '/followers/')]"
            ]
            
            followers_found = False
            for xpath in followers_xpaths:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    if elements:
                        print(f"✅ Found followers element with: {xpath}")
                        print(f"   Text: {elements[0].text}")
                        followers_found = True
                        break
                except Exception as e:
                    print(f"❌ Failed with {xpath}: {e}")
            
            if not followers_found:
                print("❌ No followers link found")
                print("This might mean:")
                print("   - Account is private")
                print("   - You don't have permission to view followers")
                print("   - Page structure has changed")
            
        else:
            print("⚠️  Profile page status unclear")
            print("Page might be loading or there might be an issue")
        
        # Keep browser open for manual inspection
        print(f"\n🔍 Browser will stay open for 60 seconds for manual inspection...")
        print("Check the browser window to see the current state")
        time.sleep(60)
        
        driver.quit()
        print("✅ Debug completed!")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    debug_instagram()
