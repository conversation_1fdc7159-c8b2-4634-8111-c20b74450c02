{"account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autofill": {"orphan_rows_removed": true}, "browser": {"has_seen_welcome_page": false}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17498, "default_apps_install_state": 3, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "install_signature": {"expire_date": "2025-09-09", "ids": ["ghbmnnjooekpmoecnnnilnnbdlolhkhi"], "invalid_ids": [], "salt": "Ymchpcm+bmlX1mD3iBrZDMC51ZLBMtqgVQV/hW7REvE=", "signature": "aIghuEl5XPdvHTCtYemGfO3B43WXpfVtWNDxJZFSUrKQ/rWGkvH7T58Q0//RmeBPJZN7wdRZQ0XwpKgm5Hi2Ova/HDZJh0k4DrsJ5w3dXLY+DjA9MgX1juD5fZxi55PtnXmGUYb9FTM4CuRICz6nOMsKq9J9FLZPS514D2Szs1pi20cDYUtoi7r0gAAwC+M6VhCMnpLklHrOWnHF/BArklZ9uXwU1THrWmhaJ/YDj0da68ViHfMPIqa3QZ6EanWn16uhWM50XnBKciO06JQvIom5xbDheK0SlBDgDq/kqFV3Lj1AjoKN5XWzfx2HM6qlp84H9csLWDwfhfqxNX3iqw==", "signature_format_version": 2, "timestamp": "*****************"}, "last_chrome_version": "107.0.5304.88"}, "gaia_cookie": {"changed_time": **********.247791, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "fa9002fe-c007-46ad-82de-433f14d482df"}}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "media": {"device_id_salt": "9E00AD7B99038E61F6463B69BBE94F3E"}, "media_router": {"receiver_id_hash_token": "rY54qwBAoiOnG06t6ONBETKfBIAdApOz3yf/ajw3qP43hMOX0tRZ+nHHDgYgZBRH2gZpSKkyvGM/FR3iK4Drxw=="}, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}, "last_fetch_attempt": "*****************"}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true}, "store_file_paths_to_delete": {}}, "plugins": {"plugins_list": []}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"accessibility_events": {}, "app_banner": {}, "ar": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {}, "clipboard": {}, "cookies": {}, "durable_storage": {}, "fedcm_active_session": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "get_display_media_set_select_all_screens": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "ppapi_broker": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "107.0.5304.88", "creation_time": "*****************", "exit_type": "Normal", "last_time_obsolete_http_credentials_removed": **********.134661, "last_time_password_store_metrics_reported": **********.145322, "managed_user_id": "", "name": "Person 2", "password_account_storage_settings": {}, "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "***********"}, "sessions": {"event_log": [{"crashed": false, "time": "***********126902", "type": 0}, {"did_schedule_command": false, "first_session_service": true, "tab_count": 0, "time": "*****************", "type": 2, "window_count": 0}], "session_data_status": 5}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"requested": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "unified_consent": {"migration_state": 10}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "extension_ids": {"https://docs.google.com/document/installwebapp?usp=chrome_default": {"extension_id": "mpnpojknpmmopombnjdcgaaiekajbnjb", "install_source": 1, "is_placeholder": false}, "https://docs.google.com/presentation/installwebapp?usp=chrome_default": {"extension_id": "kefjledonklijopmnomlcbpllchaibag", "install_source": 1, "is_placeholder": false}, "https://docs.google.com/spreadsheets/installwebapp?usp=chrome_default": {"extension_id": "fhihpiojkbmbpdjeoajapmgkhlnakfjf", "install_source": 1, "is_placeholder": false}, "https://drive.google.com/drive/installwebapp?usp=chrome_default": {"extension_id": "aghbiahbpaijignceidepookljebhfak", "install_source": 1, "is_placeholder": false}, "https://mail.google.com/mail/installwebapp?usp=chrome_default": {"extension_id": "fmgjjmmmlfnkbppncabfkddbjimcfncm", "install_source": 1, "is_placeholder": false}, "https://www.youtube.com/s/notifications/manifest/cr_install.html": {"extension_id": "agimnkijcaahngcdmfeangaknmldooml", "install_source": 1, "is_placeholder": false}}, "isolation_state": {}, "last_preinstall_synchronize_version": "107", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}}