import streamlit as st
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from configparser import ConfigParser
import time
import os

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """No user validation - always return True"""
    print("UserID validation bypassed - [OK]")
    return True


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
         # Navigate to competitor's profile page first
        print(f"Navigating to profile: {competitor_link}")
        driver.get(competitor_link)
        time.sleep(8)  # Wait for profile page to load

        # Check if we're actually on a login page (more specific check)
        current_url = driver.current_url.lower()
        page_source = driver.page_source.lower()

        # More specific login detection
        login_indicators = [
            "accounts/login" in current_url,
            "login/?next=" in current_url,
            ("log in to instagram" in page_source and "followers" not in page_source),
            ("sign up" in page_source and "log in" in page_source and len(page_source) < 50000)
        ]

        if any(login_indicators):
            st.error("❌ Instagram login required!")
            st.info("💡 Please log in to Instagram manually in your browser first, then try again.")
            print("Login required - redirected to login page")
            print(f"Current URL: {current_url}")
            driver.quit()
            return

        # Check if profile loaded successfully
        if "instagram.com" not in current_url:
            st.error("❌ Failed to load Instagram page")
            driver.quit()
            return

        print("✅ Profile page loaded successfully")

        # Click on followers link to open the followers list
        print("Looking for followers link...")
        followers_clicked = False

        # Try multiple methods to find and click followers
        followers_xpaths = [
            "//span[contains(text(),'followers')]",
            "//span[contains(text(),'follower')]",  # In case of singular
            "//a[contains(@href, '/followers/')]",
            "//span[contains(text(),'abonnés')]",  # French
            "//span[contains(text(),'seguidores')]"  # Spanish
        ]

        for xpath in followers_xpaths:
            try:
                print(f"Trying XPath: {xpath}")
                followers_element = driver.find_element(By.XPATH, xpath)
                print("Found followers element, clicking...")

                # Scroll to element to make sure it's visible
                driver.execute_script("arguments[0].scrollIntoView(true);", followers_element)
                time.sleep(1)

                # Try clicking
                followers_element.click()
                time.sleep(5)  # Wait for followers dialog to open

                # Check if dialog opened
                try:
                    dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                    print("✅ Followers dialog opened successfully!")
                    followers_clicked = True
                    break
                except:
                    print("Dialog not found, trying next method...")
                    continue

            except Exception as e:
                print(f"Failed with XPath {xpath}: {e}")
                continue

        if not followers_clicked:
            print("Could not click followers link with any method")
            st.error("❌ Could not access followers list. The account might be private or the page structure has changed.")
            driver.quit()
            return
        
        # Wait for followers dialog to be fully loaded
        print("Waiting for followers dialog to load completely...")
        time.sleep(3)

        # Verify that followers dialog is open and show current status
        dialog_found = False
        for attempt in range(5):  # Try for 5 seconds
            try:
                dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                if dialog:
                    dialog_found = True
                    print("✅ Followers dialog found and loaded")
                    st.success("✅ Followers dialog opened successfully!")
                    break
            except:
                print(f"Attempt {attempt + 1}: Dialog not found, waiting...")
                time.sleep(1)
                continue

        if not dialog_found:
            print("❌ Followers dialog not found after 5 attempts")
            st.error("❌ Could not open followers dialog. The account might be private.")

            # Debug information
            current_url = driver.current_url
            print(f"Current URL: {current_url}")
            st.info(f"Current URL: {current_url}")

            # Check if we're still on the profile page
            if competitor_link.replace("https://www.instagram.com/", "") in current_url:
                st.info("💡 Still on profile page. The account might be private or followers count might be hidden.")

            driver.quit()
            return

        # Main scraping loop - increased iterations and target
        max_iterations = 50  # Increase iterations to get more followers
        iteration = 0
        no_new_data_count = 0  # Counter for consecutive iterations with no new data

        while len_list_2 < 1419 and iteration < max_iterations:  # Try to get all 1419 followers
            iteration += 1
            print(f"Iteration {iteration} - Current followers: {len(list_2)}")
            st.info(f"🔄 Iteration {iteration} - Extracted {len(list_2)} followers so far...")

            # Try multiple XPath patterns for followers links in the dialog
            followers_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                '//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]'
            ]

            followers_profiles_links_list = []

            # Try each XPath pattern until we find followers
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    # Filter for valid Instagram profile links
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            followers_profiles_links_list.append(link)

                    if followers_profiles_links_list:
                        print(f"Found {len(followers_profiles_links_list)} followers with XPath: {xpath[:50]}...")
                        break
                except Exception as e:
                    print(f"Error with XPath {xpath[:30]}...: {e}")
                    continue

            time.sleep(3)

            # Check if we found any followers links
            if not followers_profiles_links_list:
                print("No followers found with any XPath. Breaking loop.")
                break

            # Extract href attributes more efficiently
            new_followers_found = 0
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_1:  # Avoid duplicates and empty strings
                                list_1.append(id)
                                new_followers_found += 1
                except Exception as e:
                    print(f"Error extracting link: {e}")
                    continue

            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1} (+{new_followers_found} new in this batch)")

            # Aggressive scrolling to load ALL followers
            print(f"🔄 Scrolling to load more followers (current: {len(list_2)})...")

            # Method 1: Find and scroll the followers dialog container
            scroll_success = False
            scroll_xpaths = [
                '//div[@role="dialog"]//div[contains(@class, "_aano")]',
                '//div[@role="dialog"]//div[contains(@style, "overflow")]',
                '//div[@role="dialog"]//div[contains(@class, "x1n2onr6")]',
                '//div[contains(@class, "_aano")]'
            ]

            for scroll_xpath in scroll_xpaths:
                try:
                    scroll_elements = driver.find_elements(By.XPATH, scroll_xpath)
                    if scroll_elements:
                        scroll_element = scroll_elements[0]

                        # Get initial scroll position
                        initial_scroll = driver.execute_script("return arguments[0].scrollTop", scroll_element)

                        # Scroll multiple times with longer waits
                        for scroll_attempt in range(10):  # Increased scroll attempts
                            driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_element)
                            time.sleep(3)  # Longer wait for content to load

                            # Check if new content loaded
                            new_scroll = driver.execute_script("return arguments[0].scrollTop", scroll_element)
                            if new_scroll == initial_scroll and scroll_attempt > 2:
                                print(f"No more content to scroll after {scroll_attempt} attempts")
                                break
                            initial_scroll = new_scroll

                        scroll_success = True
                        print("✅ Successfully scrolled using dialog method")
                        break
                except Exception as e:
                    print(f"Error with scroll method {scroll_xpath}: {e}")
                    continue

            # Method 2: Alternative scrolling if dialog scroll failed
            if not scroll_success:
                try:
                    print("Trying alternative scrolling methods...")

                    # Try scrolling the dialog itself
                    dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                    for i in range(10):
                        driver.execute_script("arguments[0].scrollTop += 500", dialog)
                        time.sleep(2)

                    scroll_success = True
                    print("✅ Alternative scroll method worked")
                except Exception as e:
                    print(f"Alternative scroll failed: {e}")

            # Method 3: Keyboard scrolling as last resort
            if not scroll_success:
                try:
                    print("Trying keyboard scroll method...")
                    body = driver.find_element(By.TAG_NAME, "body")
                    for i in range(10):
                        body.send_keys(Keys.PAGE_DOWN)
                        time.sleep(1)
                    print("✅ Keyboard scroll completed")
                except Exception as e:
                    print(f"Keyboard scroll failed: {e}")

            # Wait for new content to load after scrolling
            print("⏳ Waiting for new followers to load...")
            time.sleep(5)  # Longer wait for content to load

            # Get updated followers list after scrolling using the same XPath patterns
            updated_followers_list = []
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            updated_followers_list.append(link)

                    if updated_followers_list:
                        break
                except Exception as e:
                    continue

            time.sleep(3)

            # Extract new followers more efficiently
            new_followers_this_batch = 0
            for link in updated_followers_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_2:  # Avoid duplicates and empty strings
                                list_2.append(id)
                                new_followers_this_batch += 1
                                if new_followers_this_batch <= 5:  # Only print first 5 to avoid spam
                                    print(f"✔️ Extracted DATA: {id}")
                except Exception as e:
                    print(f"Error extracting new link: {e}")
                    continue
                time.sleep(0.05)  # Reduced delay for faster processing

            print(f"📊 Extracted {new_followers_this_batch} new followers in this batch")

            # Remove duplicates and update length
            previous_count = len_list_2
            list_2 = list(dict.fromkeys(list_2))
            len_list_2 = len(list_2)
            new_followers_this_iteration = len_list_2 - previous_count

            print(f"---- Process: Len(List_2) = {len_list_2} (+{new_followers_this_iteration} new)")

            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")

            # Save data to file after each iteration
            if followers_list:  # Only save if we have data
                try:
                    with open("followers_data.txt", "w", encoding='utf-8') as text_file:
                        for element in followers_list:
                            text_file.write(element + "\n")
                    print(f"\n [+] Extracted Data Saved to : [ followers_data.txt ] ({len(followers_list)} followers)\n")
                except Exception as e:
                    print(f"Error saving file: {e}")

            # Update Streamlit progress
            st.info(f"📊 Progress: {len(followers_list)}/1419 followers extracted ({len(followers_list)/1419*100:.1f}%)")

            # Check stopping conditions
            if new_followers_this_iteration == 0:
                no_new_data_count += 1
                print(f"No new followers found in this iteration (count: {no_new_data_count})")

                if no_new_data_count >= 3:  # Stop after 3 consecutive iterations with no new data
                    print("No new followers found in 3 consecutive iterations. Ending scraping.")
                    st.info("✅ Reached end of followers list - no more followers to load")
                    break
            else:
                no_new_data_count = 0  # Reset counter if we found new followers

            # Check if we've reached the target or a reasonable limit
            if len(followers_list) >= 1419:
                print("[!] Successfully extracted all followers!")
                st.success(f"🎉 Successfully extracted all {len(followers_list)} followers!")
                break
            elif len(followers_list) >= 1000:  # Fallback limit for free version
                print("[!] Reached 1000 followers limit for free version!")
                st.success(f"✅ Successfully extracted {len(followers_list)} followers (free version limit)!")
                st.info("💡 Upgrade to Pro version to extract all followers")
                break

        # Close browser
        driver.quit()

        if followers_list:
            st.success(f"🎉 Scraping completed successfully! Extracted {len(followers_list)} followers.")
            st.info("📁 Data saved to: followers_data.txt")
        else:
            st.warning("⚠️ No followers data was extracted. Please check the Instagram page and try again.")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("💡 Possible solutions:\n- Check your internet connection\n- Verify the Instagram URL is correct\n- Try again later")
        try:
            driver.quit()
        except:
            pass
