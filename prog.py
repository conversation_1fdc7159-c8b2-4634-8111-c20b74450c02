import streamlit as st
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from configparser import ConfigParser
import time
import os

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """No user validation - always return True"""
    print("UserID validation bypassed - [OK]")
    return True


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
         # Navigate to competitor's page
        driver.get(f"{competitor_link}")
        time.sleep(5)

        # Navigate to competitor's followers page
        follow_button_xpath = '//span[contains(text(),"followers")]'
        follow_button = driver.find_element(By.XPATH, follow_button_xpath)
        follow_button.click()
        time.sleep(5)
        
        # XPath for followers links
        followers_links_xpath = '//body/div[@class="x1n2onr6 xzkaem6"]/div/div/div[@class="x1n2onr6 xzkaem6"]/div[@class="x9f619 x1n2onr6 x1ja2u2z"]/div[@class="x78zum5 xdt5ytf xg6iff7 xippug5 x1n2onr6"]/div[@class="x1uvtmcs x4k7w5x x1h91t0o x1beo9mf xaigb6o x12ejxvf x3igimt xarpa2k xedcshv x1lytzrv x1t2pt76 x7ja8zs x1n2onr6 x1qrby5j x1jfb8zj"]/div[@class="x1qjc9v5 x9f619 x78zum5 xdt5ytf x1iyjqo2 xl56j7k"]/div[@class="x1cy8zhl x9f619 x78zum5 xl56j7k x2lwn1j xeuugli x47corl"]/div[@role="dialog"]/div[@class="xs83m0k x1sy10c2 x3aesyq xieb3on xqsn43r x1n2onr6 xqui1pq x5mc7k8 xewp6mh x19lxi20 xw8ag78 x1v7wizp xc7qz2s xa3vuyk"]/div[@class="x7r02ix x15fl9t6 x1yw9sn2 x1evh3fb x4giqqa xb88tzc xw2csxc x1odjw0f x5fp0pe"]/div/div[@class="x9f619 xjbqb8w x78zum5 x15mokao x1ga7v0g x16uus16 xbiv7yw x1n2onr6 x1plvlek xryxfnj x1iyjqo2 x2lwn1j xeuugli xdt5ytf xqjyukv x1qjc9v5 x1oa3qoh x1nhvcw1"]/div[@class="x6nl9eh x1a5l9x9 x7vuprf x1mg3h75 x1lliihq x1iyjqo2 xs83m0k xz65tgg x1rife3k x1n2onr6"]/div[1]/div[1]'
        
        # Main scraping loop
        max_iterations = 10  # Prevent infinite loop
        iteration = 0

        while len_list_2 < 1000 and iteration < max_iterations:  # Limit to 1000 followers for free version
            iteration += 1
            print(f"Iteration {iteration}")

            # Get followers profile links
            followers_profiles_links_list = driver.find_elements(By.XPATH, followers_links_xpath)
            time.sleep(3)

            # Check if we found any followers links
            if not followers_profiles_links_list:
                print("No followers links found. Trying alternative XPath...")
                # Try alternative XPath patterns
                alternative_xpaths = [
                    '//a[contains(@href, "/")]',
                    '//*[@role="button"]//a',
                    '//div[@role="button"]//a'
                ]

                for alt_xpath in alternative_xpaths:
                    followers_profiles_links_list = driver.find_elements(By.XPATH, alt_xpath)
                    if followers_profiles_links_list:
                        print(f"Found {len(followers_profiles_links_list)} links with alternative XPath")
                        break

                if not followers_profiles_links_list:
                    print("No followers found with any XPath. Breaking loop.")
                    break

            # Extract href attributes
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href and "/p/" not in href:
                        id = href.replace("https://www.instagram.com/", "").replace("/", "")
                        if id and id not in list_1:  # Avoid duplicates
                            list_1.append(id)
                except Exception as e:
                    print(f"Error extracting link: {e}")
                    continue

            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1}")

            # Scroll down to load more followers
            scroll_xpath = '//*[@class="_aano"]'
            scroll_elements = driver.find_elements(By.XPATH, scroll_xpath)

            # Check if scroll element exists before trying to scroll
            if scroll_elements:
                scroll = 0
                while scroll < 3:
                    try:
                        driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_elements[0])
                        time.sleep(2)
                        scroll += 1
                    except Exception as e:
                        print(f"Error scrolling: {e}")
                        break
            else:
                # Try alternative scrolling method
                print("Scroll element not found, trying page scroll...")
                try:
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(3)
                except Exception as e:
                    print(f"Error with page scroll: {e}")

            # Get updated followers list after scrolling
            followers_profiles_links_list = driver.find_elements(By.XPATH, followers_links_xpath)
            time.sleep(3)

            # Extract new followers
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href and "/p/" not in href:
                        id = href.replace("https://www.instagram.com/", "").replace("/", "")
                        if id and id not in list_2:  # Avoid duplicates
                            list_2.append(id)
                            print(f"✔️ Extracted DATA: {id}")
                except Exception as e:
                    print(f"Error extracting new link: {e}")
                    continue
                time.sleep(0.1)

            # Remove duplicates and update length
            list_2 = list(dict.fromkeys(list_2))
            len_list_2 = len(list_2)
            print(f"---- Process: Len(List_2) = {len_list_2}")

            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")

            # Save data to file
            if followers_list:  # Only save if we have data
                try:
                    with open("followers_data.txt", "w", encoding='utf-8') as text_file:
                        for element in followers_list:
                            text_file.write(element + "\n")
                    print("\n [+] Extracted Data Saved to : [ followers_data.txt ] \n")
                except Exception as e:
                    print(f"Error saving file: {e}")

            # Check if limit reached or no new data
            if len(followers_list) >= 1000:
                print("[!] You have reached the scrapping followers limit !!")
                st.success(f"✅ Successfully extracted {len(followers_list)} followers data!")
                st.info("📁 Data saved to: followers_data.txt")
                break
            elif len_list_2 == len_list_1:  # No new data found
                print("No new followers found. Ending scraping.")
                break

        # Close browser
        driver.quit()

        if followers_list:
            st.success(f"🎉 Scraping completed successfully! Extracted {len(followers_list)} followers.")
            st.info("📁 Data saved to: followers_data.txt")
        else:
            st.warning("⚠️ No followers data was extracted. Please check the Instagram page and try again.")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("💡 Possible solutions:\n- Check your internet connection\n- Verify the Instagram URL is correct\n- Try again later")
        try:
            driver.quit()
        except:
            pass
