import streamlit as st
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from configparser import Config<PERSON>arser
import time
import os

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """No user validation - always return True"""
    print("UserID validation bypassed - [OK]")
    return True


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
         # Navigate to competitor's profile page first
        print(f"Navigating to profile: {competitor_link}")
        driver.get(competitor_link)
        time.sleep(8)  # Wait for profile page to load

        # Check if we're actually on a login page (more specific check)
        current_url = driver.current_url.lower()
        page_source = driver.page_source.lower()

        # More specific login detection
        login_indicators = [
            "accounts/login" in current_url,
            "login/?next=" in current_url,
            ("log in to instagram" in page_source and "followers" not in page_source),
            ("sign up" in page_source and "log in" in page_source and len(page_source) < 50000)
        ]

        if any(login_indicators):
            st.error("❌ Instagram login required!")
            st.info("💡 Please log in to Instagram manually in your browser first, then try again.")
            print("Login required - redirected to login page")
            print(f"Current URL: {current_url}")
            driver.quit()
            return

        # Check if profile loaded successfully
        if "instagram.com" not in current_url:
            st.error("❌ Failed to load Instagram page")
            driver.quit()
            return

        print("✅ Profile page loaded successfully")

        # Click on followers link to open the followers list
        print("Looking for followers link...")
        followers_clicked = False

        # Try multiple methods to find and click followers
        followers_xpaths = [
            "//span[contains(text(),'followers')]",
            "//span[contains(text(),'follower')]",  # In case of singular
            "//a[contains(@href, '/followers/')]",
            "//span[contains(text(),'abonnés')]",  # French
            "//span[contains(text(),'seguidores')]"  # Spanish
        ]

        for xpath in followers_xpaths:
            try:
                print(f"Trying XPath: {xpath}")
                followers_element = driver.find_element(By.XPATH, xpath)
                print("Found followers element, clicking...")

                # Scroll to element to make sure it's visible
                driver.execute_script("arguments[0].scrollIntoView(true);", followers_element)
                time.sleep(1)

                # Try clicking
                followers_element.click()
                time.sleep(5)  # Wait for followers dialog to open

                # Check if dialog opened
                try:
                    dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                    print("✅ Followers dialog opened successfully!")
                    followers_clicked = True
                    break
                except:
                    print("Dialog not found, trying next method...")
                    continue

            except Exception as e:
                print(f"Failed with XPath {xpath}: {e}")
                continue

        if not followers_clicked:
            print("Could not click followers link with any method")
            st.error("❌ Could not access followers list. The account might be private or the page structure has changed.")
            driver.quit()
            return
        
        # Wait for followers dialog to be fully loaded
        print("Waiting for followers dialog to load completely...")
        time.sleep(3)

        # Verify that followers dialog is open and show current status
        dialog_found = False
        for attempt in range(5):  # Try for 5 seconds
            try:
                dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                if dialog:
                    dialog_found = True
                    print("✅ Followers dialog found and loaded")
                    st.success("✅ Followers dialog opened successfully!")
                    break
            except:
                print(f"Attempt {attempt + 1}: Dialog not found, waiting...")
                time.sleep(1)
                continue

        if not dialog_found:
            print("❌ Followers dialog not found after 5 attempts")
            st.error("❌ Could not open followers dialog. The account might be private.")

            # Debug information
            current_url = driver.current_url
            print(f"Current URL: {current_url}")
            st.info(f"Current URL: {current_url}")

            # Check if we're still on the profile page
            if competitor_link.replace("https://www.instagram.com/", "") in current_url:
                st.info("💡 Still on profile page. The account might be private or followers count might be hidden.")

            driver.quit()
            return

        # Main scraping loop - increased iterations and target
        max_iterations = 50  # Increase iterations to get more followers
        iteration = 0
        no_new_data_count = 0  # Counter for consecutive iterations with no new data

        while iteration < max_iterations:  # Continue until no more followers found
            iteration += 1
            followers_before_iteration = len(list_2)
            print(f"\n{'='*60}")
            print(f"🔄 ITERATION {iteration} - Starting with {followers_before_iteration} followers")
            print(f"{'='*60}")
            st.info(f"🔄 Iteration {iteration} - Extracted {len(list_2)} followers so far...")

            # Try multiple XPath patterns for followers links in the dialog
            followers_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                '//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]'
            ]

            followers_profiles_links_list = []

            # Try each XPath pattern until we find followers
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    # Filter for valid Instagram profile links
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            followers_profiles_links_list.append(link)

                    if followers_profiles_links_list:
                        print(f"Found {len(followers_profiles_links_list)} followers with XPath: {xpath[:50]}...")
                        break
                except Exception as e:
                    print(f"Error with XPath {xpath[:30]}...: {e}")
                    continue

            time.sleep(3)

            # Check if we found any followers links
            if not followers_profiles_links_list:
                print("No followers found with any XPath. Breaking loop.")
                break

            # Extract href attributes more efficiently
            new_followers_found = 0
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_1:  # Avoid duplicates and empty strings
                                list_1.append(id)
                                new_followers_found += 1
                except Exception as e:
                    print(f"Error extracting link: {e}")
                    continue

            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1} (+{new_followers_found} new in this batch)")

            # SCROLLING ADAPTATIF - Test de toutes les méthodes possibles
            print(f"🔄 SCROLLING ADAPTATIF pour charger plus de followers (actuel: {len(list_2)})...")

            # Compter les followers avant scrolling
            followers_before_scroll = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))
            print(f"📊 Followers visibles avant scrolling: {followers_before_scroll}")

            # Méthode 1: Scrolling par détection automatique du conteneur
            scrolling_success = False

            # Essayer de trouver tous les éléments scrollables possibles
            possible_containers = []

            # Recherche exhaustive de conteneurs
            container_queries = [
                '//div[@role="dialog"]',
                '//div[@role="dialog"]//div',
                '//div[contains(@class, "_aano")]',
                '//div[contains(@style, "overflow")]',
                '//div[contains(@style, "height")]'
            ]

            for query in container_queries:
                try:
                    elements = driver.find_elements(By.XPATH, query)
                    for element in elements:
                        # Vérifier si l'élément est scrollable
                        try:
                            scroll_height = driver.execute_script("return arguments[0].scrollHeight", element)
                            client_height = driver.execute_script("return arguments[0].clientHeight", element)
                            if scroll_height > client_height:
                                possible_containers.append(element)
                        except:
                            continue
                except:
                    continue

            print(f"🔍 Trouvé {len(possible_containers)} conteneurs potentiellement scrollables")

            # Tester chaque conteneur
            for i, container in enumerate(possible_containers):
                print(f"🧪 Test du conteneur {i+1}/{len(possible_containers)}")

                try:
                    # Obtenir la hauteur initiale
                    initial_height = driver.execute_script("return arguments[0].scrollHeight", container)
                    initial_followers = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))

                    print(f"   Hauteur initiale: {initial_height}px, Followers: {initial_followers}")

                    # Essayer de scroller ce conteneur
                    for scroll_attempt in range(10):
                        driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", container)
                        time.sleep(1.5)

                        # Vérifier si de nouveaux followers sont apparus
                        new_followers_count = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))
                        new_height = driver.execute_script("return arguments[0].scrollHeight", container)

                        if new_followers_count > initial_followers:
                            print(f"   ✅ SUCCÈS! Nouveaux followers: {initial_followers} → {new_followers_count}")
                            scrolling_success = True

                            # Continuer à scroller ce conteneur qui fonctionne
                            last_height = new_height
                            for continued_scroll in range(50):  # Scroller plus agressivement
                                driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", container)
                                time.sleep(2)

                                current_height = driver.execute_script("return arguments[0].scrollHeight", container)
                                current_followers = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))

                                print(f"   Scroll {continued_scroll + 1}: {current_followers} followers, hauteur: {current_height}px")

                                # Arrêter si plus de changement
                                if current_height == last_height and continued_scroll > 5:
                                    print(f"   🛑 Fin atteinte après {continued_scroll + 1} scrolls")
                                    break
                                last_height = current_height

                                # Pause plus longue tous les 10 scrolls
                                if continued_scroll % 10 == 0:
                                    time.sleep(4)

                            break

                        elif new_height > initial_height:
                            print(f"   📏 Hauteur changée: {initial_height} → {new_height}, mais pas de nouveaux followers")
                            initial_height = new_height

                        if scroll_attempt >= 5 and new_followers_count == initial_followers:
                            print(f"   ❌ Pas de nouveaux followers après {scroll_attempt + 1} tentatives")
                            break

                    if scrolling_success:
                        break

                except Exception as e:
                    print(f"   ❌ Erreur avec conteneur {i+1}: {e}")
                    continue

            # Si aucun conteneur n'a fonctionné, essayer des méthodes alternatives
            if not scrolling_success:
                print("⚠️  Aucun conteneur scrollable trouvé, essai de méthodes alternatives...")

                # Méthode alternative 1: Scrolling de la page entière
                try:
                    print("🔄 Méthode alternative: Scrolling de page")
                    for page_scroll in range(20):
                        driver.execute_script("window.scrollBy(0, 500);")
                        time.sleep(1)

                        current_followers = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))
                        if current_followers > followers_before_scroll:
                            print(f"   ✅ Scrolling de page fonctionne! {followers_before_scroll} → {current_followers}")
                            scrolling_success = True
                            break
                except Exception as e:
                    print(f"   ❌ Scrolling de page échoué: {e}")

                # Méthode alternative 2: Simulation de touches
                if not scrolling_success:
                    try:
                        print("🔄 Méthode alternative: Simulation de touches")
                        body = driver.find_element(By.TAG_NAME, "body")
                        for key_attempt in range(15):
                            body.send_keys(Keys.PAGE_DOWN)
                            time.sleep(1)

                            current_followers = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))
                            if current_followers > followers_before_scroll:
                                print(f"   ✅ Touches fonctionnent! {followers_before_scroll} → {current_followers}")
                                scrolling_success = True
                                break
                    except Exception as e:
                        print(f"   ❌ Simulation de touches échouée: {e}")

            # Résultat final du scrolling
            followers_after_scroll = len(driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]'))
            print(f"📊 Résultat du scrolling: {followers_before_scroll} → {followers_after_scroll} followers")

            if followers_after_scroll > followers_before_scroll:
                print(f"✅ Scrolling réussi! +{followers_after_scroll - followers_before_scroll} nouveaux followers visibles")
            else:
                print("❌ Scrolling échoué - aucun nouveau follower chargé")

            # Attendre que le contenu se stabilise
            time.sleep(3)

            # EXTRACTION COMPLÈTE après scrolling intelligent
            print("🔍 Extraction complète de tous les followers après scrolling...")

            # Attendre un peu plus pour que tous les éléments soient bien chargés
            time.sleep(3)

            # Utiliser le XPath le plus simple et efficace
            primary_xpath = '//div[@role="dialog"]//a[contains(@href, "/")]'

            try:
                all_links = driver.find_elements(By.XPATH, primary_xpath)
                print(f"📊 Total de liens trouvés: {len(all_links)}")

                # Filtrer et extraire les followers valides
                valid_followers = []
                seen_usernames = set()

                for link in all_links:
                    try:
                        href = link.get_attribute("href")
                        if href and "instagram.com/" in href:
                            # Filtrage strict pour éviter les faux positifs
                            excluded_patterns = ["/p/", "/reel/", "/tv/", "/stories/", "/explore/",
                                               "/accounts/", "/direct/", "/help/", "/about/",
                                               "/legal/", "/safety/", "/press/"]

                            if not any(pattern in href for pattern in excluded_patterns):
                                # Extraire le nom d'utilisateur
                                username = href.replace("https://www.instagram.com/", "").replace("/", "").strip()

                                # Vérifications supplémentaires
                                if (username and
                                    len(username) > 0 and
                                    username not in seen_usernames and
                                    not username.startswith("http") and
                                    "." not in username[:5]):  # Éviter les URLs malformées

                                    valid_followers.append(username)
                                    seen_usernames.add(username)
                    except Exception as e:
                        continue

                print(f"✅ Followers valides extraits: {len(valid_followers)}")

                # Ajouter à la liste principale (éviter les doublons)
                new_followers_count = 0
                for username in valid_followers:
                    if username not in list_2:
                        list_2.append(username)
                        new_followers_count += 1

                print(f"📈 Nouveaux followers ajoutés: {new_followers_count}")

            except Exception as e:
                print(f"❌ Erreur lors de l'extraction: {e}")
                # Fallback vers l'ancienne méthode
                updated_followers_list = []
                try:
                    temp_links = driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[@role="link"]')
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if href and "instagram.com/" in href and "/p/" not in href:
                            updated_followers_list.append(link)
                except:
                    pass

            # Les followers ont déjà été extraits dans la section précédente
            # Afficher quelques exemples des nouveaux followers
            if new_followers_count > 0:
                print("📝 Exemples de nouveaux followers:")
                for i, username in enumerate(valid_followers[-min(5, new_followers_count):]):
                    print(f"   ✔️ {username}")
                if new_followers_count > 5:
                    print(f"   ... et {new_followers_count - 5} autres")

            print(f"📊 Total de nouveaux followers dans cette itération: {new_followers_count}")

            # Verify scrolling effectiveness
            followers_after_iteration = len(list_2)
            followers_gained_this_iteration = followers_after_iteration - followers_before_iteration

            print(f"📈 ITERATION {iteration} SUMMARY:")
            print(f"   Before: {followers_before_iteration} followers")
            print(f"   After:  {followers_after_iteration} followers")
            print(f"   Gained: {followers_gained_this_iteration} new followers")

            if followers_gained_this_iteration == 0:
                print("⚠️  WARNING: No new followers found in this iteration!")
                print("   This might indicate:")
                print("   - We've reached the end of the followers list")
                print("   - Instagram is rate limiting")
                print("   - All followers have been loaded")
            else:
                print(f"✅ SUCCESS: Found {followers_gained_this_iteration} new followers!")
                print("   Scrolling is working correctly!")

            # Remove duplicates and calculate new followers accurately
            list_2 = list(dict.fromkeys(list_2))  # Remove duplicates while preserving order
            len_list_2 = len(list_2)
            new_followers_this_iteration = followers_gained_this_iteration  # Use the already calculated value

            print(f"---- Process: Len(List_2) = {len_list_2} (+{new_followers_this_iteration} new)")

            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")

            # Save data to file after each iteration
            if followers_list:  # Only save if we have data
                try:
                    with open("followers_data.txt", "w", encoding='utf-8') as text_file:
                        for element in followers_list:
                            text_file.write(element + "\n")
                    print(f"\n [+] Extracted Data Saved to : [ followers_data.txt ] ({len(followers_list)} followers)\n")
                except Exception as e:
                    print(f"Error saving file: {e}")

            # Update Streamlit progress (dynamic)
            st.info(f"📊 Extracted: {len(followers_list)} followers (+{new_followers_this_iteration} this iteration)")

            # More aggressive stopping conditions - give more chances
            if new_followers_this_iteration == 0:
                no_new_data_count += 1
                print(f"⚠️  No new followers found in this iteration (consecutive count: {no_new_data_count})")

                # Try more iterations before giving up
                if no_new_data_count >= 5:  # Increased from 3 to 5
                    print("🛑 No new followers found in 5 consecutive iterations.")
                    print("✅ All available followers have been extracted!")
                    st.success(f"🎉 Extraction completed! Found all {len(followers_list)} available followers!")
                    st.info("✅ Reached end of followers list - no more followers to load")
                    break
                elif no_new_data_count >= 3:
                    print("⚠️  3 iterations without new followers - trying extra aggressive scrolling...")
                    # Extra aggressive scrolling when we're not finding new followers
                    try:
                        dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                        for extra_scroll in range(50):  # Extra scrolling
                            driver.execute_script("arguments[0].scrollTop += 200", dialog)
                            time.sleep(0.3)
                            if extra_scroll % 10 == 0:
                                driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", dialog)
                                time.sleep(1)
                        print("✅ Extra aggressive scrolling completed")
                        time.sleep(3)
                    except:
                        print("❌ Extra scrolling failed")
            else:
                no_new_data_count = 0  # Reset counter if we found new followers
                print(f"✅ Found {new_followers_this_iteration} new followers, continuing...")

            # Safety limit to prevent infinite loops
            if len(followers_list) >= 50000:
                print("[!] Reached safety limit of 50,000 followers!")
                st.warning(f"⚠️  Reached safety limit: {len(followers_list)} followers extracted")
                st.info("💡 This prevents infinite loops for extremely large accounts")
                break

        # Close browser
        driver.quit()

        # Final results
        if followers_list:
            total_followers = len(followers_list)
            print(f"\n🎉 EXTRACTION COMPLETED!")
            print(f"📊 Total followers extracted: {total_followers}")
            print(f"📁 Data saved to: followers_data.txt")

            st.success(f"🎉 Scraping completed successfully!")
            st.metric("Total Followers Extracted", total_followers)
            st.info("📁 Data saved to: followers_data.txt")

            # Show some sample followers
            if total_followers > 0:
                sample_size = min(5, total_followers)
                st.write("📝 Sample followers:")
                for i in range(sample_size):
                    st.write(f"   • {followers_list[i]}")
                if total_followers > sample_size:
                    st.write(f"   ... and {total_followers - sample_size} more in the file")
        else:
            print("❌ No followers data was extracted")
            st.warning("⚠️ No followers data was extracted. Please check the Instagram page and try again.")
            st.info("💡 Possible reasons:\n- Account is private\n- Login required\n- Network issues\n- Page structure changed")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("💡 Possible solutions:\n- Check your internet connection\n- Verify the Instagram URL is correct\n- Try again later")
        try:
            driver.quit()
        except:
            pass
