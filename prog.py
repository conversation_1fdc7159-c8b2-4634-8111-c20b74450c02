import streamlit as st
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from configparser import ConfigParser
import time
import os

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """No user validation - always return True"""
    print("UserID validation bypassed - [OK]")
    return True


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
         # Navigate to competitor's profile page first
        print(f"Navigating to profile: {competitor_link}")
        driver.get(competitor_link)
        time.sleep(8)  # Wait for profile page to load

        # Check if we're actually on a login page (more specific check)
        current_url = driver.current_url.lower()
        page_source = driver.page_source.lower()

        # More specific login detection
        login_indicators = [
            "accounts/login" in current_url,
            "login/?next=" in current_url,
            ("log in to instagram" in page_source and "followers" not in page_source),
            ("sign up" in page_source and "log in" in page_source and len(page_source) < 50000)
        ]

        if any(login_indicators):
            st.error("❌ Instagram login required!")
            st.info("💡 Please log in to Instagram manually in your browser first, then try again.")
            print("Login required - redirected to login page")
            print(f"Current URL: {current_url}")
            driver.quit()
            return

        # Check if profile loaded successfully
        if "instagram.com" not in current_url:
            st.error("❌ Failed to load Instagram page")
            driver.quit()
            return

        print("✅ Profile page loaded successfully")

        # Click on followers link to open the followers list
        print("Looking for followers link...")
        followers_clicked = False

        # Try multiple methods to find and click followers
        followers_xpaths = [
            "//span[contains(text(),'followers')]",
            "//span[contains(text(),'follower')]",  # In case of singular
            "//a[contains(@href, '/followers/')]",
            "//span[contains(text(),'abonnés')]",  # French
            "//span[contains(text(),'seguidores')]"  # Spanish
        ]

        for xpath in followers_xpaths:
            try:
                print(f"Trying XPath: {xpath}")
                followers_element = driver.find_element(By.XPATH, xpath)
                print("Found followers element, clicking...")

                # Scroll to element to make sure it's visible
                driver.execute_script("arguments[0].scrollIntoView(true);", followers_element)
                time.sleep(1)

                # Try clicking
                followers_element.click()
                time.sleep(5)  # Wait for followers dialog to open

                # Check if dialog opened
                try:
                    dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                    print("✅ Followers dialog opened successfully!")
                    followers_clicked = True
                    break
                except:
                    print("Dialog not found, trying next method...")
                    continue

            except Exception as e:
                print(f"Failed with XPath {xpath}: {e}")
                continue

        if not followers_clicked:
            print("Could not click followers link with any method")
            st.error("❌ Could not access followers list. The account might be private or the page structure has changed.")
            driver.quit()
            return
        
        # Wait for followers dialog to be fully loaded
        print("Waiting for followers dialog to load completely...")
        time.sleep(3)

        # Verify that followers dialog is open and show current status
        dialog_found = False
        for attempt in range(5):  # Try for 5 seconds
            try:
                dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                if dialog:
                    dialog_found = True
                    print("✅ Followers dialog found and loaded")
                    st.success("✅ Followers dialog opened successfully!")
                    break
            except:
                print(f"Attempt {attempt + 1}: Dialog not found, waiting...")
                time.sleep(1)
                continue

        if not dialog_found:
            print("❌ Followers dialog not found after 5 attempts")
            st.error("❌ Could not open followers dialog. The account might be private.")

            # Debug information
            current_url = driver.current_url
            print(f"Current URL: {current_url}")
            st.info(f"Current URL: {current_url}")

            # Check if we're still on the profile page
            if competitor_link.replace("https://www.instagram.com/", "") in current_url:
                st.info("💡 Still on profile page. The account might be private or followers count might be hidden.")

            driver.quit()
            return

        # Main scraping loop - increased iterations and target
        max_iterations = 50  # Increase iterations to get more followers
        iteration = 0
        no_new_data_count = 0  # Counter for consecutive iterations with no new data

        while iteration < max_iterations:  # Continue until no more followers found
            iteration += 1
            followers_before_iteration = len(list_2)
            print(f"\n{'='*60}")
            print(f"🔄 ITERATION {iteration} - Starting with {followers_before_iteration} followers")
            print(f"{'='*60}")
            st.info(f"🔄 Iteration {iteration} - Extracted {len(list_2)} followers so far...")

            # Try multiple XPath patterns for followers links in the dialog
            followers_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                '//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]'
            ]

            followers_profiles_links_list = []

            # Try each XPath pattern until we find followers
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    # Filter for valid Instagram profile links
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            followers_profiles_links_list.append(link)

                    if followers_profiles_links_list:
                        print(f"Found {len(followers_profiles_links_list)} followers with XPath: {xpath[:50]}...")
                        break
                except Exception as e:
                    print(f"Error with XPath {xpath[:30]}...: {e}")
                    continue

            time.sleep(3)

            # Check if we found any followers links
            if not followers_profiles_links_list:
                print("No followers found with any XPath. Breaking loop.")
                break

            # Extract href attributes more efficiently
            new_followers_found = 0
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_1:  # Avoid duplicates and empty strings
                                list_1.append(id)
                                new_followers_found += 1
                except Exception as e:
                    print(f"Error extracting link: {e}")
                    continue

            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1} (+{new_followers_found} new in this batch)")

            # IMPROVED SCROLLING - Focus on the followers list container
            print(f"🔄 Scrolling to load more followers (current: {len(list_2)})...")

            # First, let's find the exact scrollable container for followers
            scrollable_container = None
            container_xpaths = [
                '//div[@role="dialog"]//div[contains(@style, "height") and contains(@style, "overflow")]',
                '//div[@role="dialog"]//div[contains(@class, "_aano")]',
                '//div[@role="dialog"]//div[contains(@class, "x1n2onr6") and contains(@style, "overflow")]',
                '//div[@role="dialog"]//div[contains(@class, "x1n2onr6")]//div[contains(@style, "overflow")]'
            ]

            for xpath in container_xpaths:
                try:
                    elements = driver.find_elements(By.XPATH, xpath)
                    if elements:
                        scrollable_container = elements[0]
                        print(f"✅ Found scrollable container with: {xpath}")
                        break
                except:
                    continue

            if scrollable_container:
                # Method 1: Scroll the specific container
                try:
                    print("📜 Scrolling the followers container...")

                    # Get initial height to detect when we reach the end
                    initial_height = driver.execute_script("return arguments[0].scrollHeight", scrollable_container)
                    current_position = 0

                    for scroll_round in range(20):  # More scroll attempts
                        # Scroll down by small increments
                        scroll_amount = 300  # Smaller scroll amount for better loading
                        driver.execute_script(f"arguments[0].scrollTop += {scroll_amount}", scrollable_container)
                        current_position += scroll_amount

                        # Wait for content to load
                        time.sleep(2)

                        # Check if new content loaded by comparing heights
                        new_height = driver.execute_script("return arguments[0].scrollHeight", scrollable_container)
                        current_scroll_top = driver.execute_script("return arguments[0].scrollTop", scrollable_container)

                        print(f"   Scroll {scroll_round + 1}: Position {current_scroll_top}, Height {new_height}")

                        # If we've reached the bottom and no new content loaded
                        if current_scroll_top + driver.execute_script("return arguments[0].clientHeight", scrollable_container) >= new_height:
                            if new_height == initial_height:
                                print("   📍 Reached bottom - no new content loaded")
                                break
                            else:
                                initial_height = new_height  # Update height and continue

                        # Extra wait every few scrolls to let Instagram load content
                        if scroll_round % 3 == 0:
                            print("   ⏳ Pausing for content to load...")
                            time.sleep(3)

                    print("✅ Container scrolling completed")

                except Exception as e:
                    print(f"❌ Container scrolling failed: {e}")

            else:
                # Method 2: Alternative scrolling methods
                print("⚠️  No specific container found, trying alternative methods...")

                try:
                    # Try to scroll the entire dialog
                    dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                    print("📜 Scrolling entire dialog...")

                    for i in range(15):
                        # Use JavaScript to scroll
                        driver.execute_script("arguments[0].scrollTop += 400", dialog)
                        time.sleep(2)

                        # Also try scrolling with mouse wheel simulation
                        driver.execute_script("""
                            var event = new WheelEvent('wheel', {
                                deltaY: 500,
                                bubbles: true,
                                cancelable: true
                            });
                            arguments[0].dispatchEvent(event);
                        """, dialog)
                        time.sleep(1)

                    print("✅ Dialog scrolling completed")

                except Exception as e:
                    print(f"❌ Dialog scrolling failed: {e}")

                    # Method 3: Page-level scrolling
                    try:
                        print("📜 Trying page-level scrolling...")
                        for i in range(10):
                            driver.execute_script("window.scrollBy(0, 300);")
                            time.sleep(1)
                        print("✅ Page scrolling completed")
                    except Exception as e:
                        print(f"❌ Page scrolling failed: {e}")

            # Wait for new content to load after all scrolling attempts
            print("⏳ Waiting for new followers to load...")
            time.sleep(4)

            # Get ALL followers after scrolling - use multiple methods to ensure we get everything
            print("🔍 Scanning for all followers after scrolling...")
            updated_followers_list = []

            # Try multiple XPath patterns and combine results
            all_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                # Additional patterns for better coverage
                '//div[@role="dialog"]//a[contains(@href, "instagram.com/")]',
                '//div[@role="dialog"]//a[starts-with(@href, "/")]'
            ]

            for xpath in all_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    print(f"   Found {len(temp_links)} links with pattern: {xpath[:50]}...")

                    for link in temp_links:
                        try:
                            href = link.get_attribute("href")
                            if href and "instagram.com/" in href:
                                # More comprehensive filtering
                                if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/", "/direct/", "/help/"]):
                                    updated_followers_list.append(link)
                        except:
                            continue

                except Exception as e:
                    print(f"   Error with pattern {xpath[:30]}...: {e}")
                    continue

            # Remove duplicate links based on href
            unique_links = []
            seen_hrefs = set()
            for link in updated_followers_list:
                try:
                    href = link.get_attribute("href")
                    if href and href not in seen_hrefs:
                        unique_links.append(link)
                        seen_hrefs.add(href)
                except:
                    continue

            updated_followers_list = unique_links
            print(f"📊 Total unique follower links found: {len(updated_followers_list)}")

            time.sleep(2)

            # Extract new followers more efficiently
            new_followers_this_batch = 0
            for link in updated_followers_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_2:  # Avoid duplicates and empty strings
                                list_2.append(id)
                                new_followers_this_batch += 1
                                if new_followers_this_batch <= 5:  # Only print first 5 to avoid spam
                                    print(f"✔️ Extracted DATA: {id}")
                except Exception as e:
                    print(f"Error extracting new link: {e}")
                    continue
                time.sleep(0.05)  # Reduced delay for faster processing

            print(f"📊 Extracted {new_followers_this_batch} new followers in this batch")

            # Verify scrolling effectiveness
            followers_after_iteration = len(list_2)
            followers_gained_this_iteration = followers_after_iteration - followers_before_iteration

            print(f"📈 ITERATION {iteration} SUMMARY:")
            print(f"   Before: {followers_before_iteration} followers")
            print(f"   After:  {followers_after_iteration} followers")
            print(f"   Gained: {followers_gained_this_iteration} new followers")

            if followers_gained_this_iteration == 0:
                print("⚠️  WARNING: No new followers found in this iteration!")
                print("   This might indicate:")
                print("   - Scrolling is not working properly")
                print("   - We've reached the end of the list")
                print("   - Instagram is rate limiting")
            else:
                print(f"✅ SUCCESS: Found {followers_gained_this_iteration} new followers!")

            # Remove duplicates and calculate new followers accurately
            previous_count = len(list_2)
            list_2 = list(dict.fromkeys(list_2))  # Remove duplicates while preserving order
            len_list_2 = len(list_2)
            new_followers_this_iteration = len_list_2 - previous_count

            print(f"---- Process: Len(List_2) = {len_list_2} (+{new_followers_this_iteration} new)")

            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")

            # Save data to file after each iteration
            if followers_list:  # Only save if we have data
                try:
                    with open("followers_data.txt", "w", encoding='utf-8') as text_file:
                        for element in followers_list:
                            text_file.write(element + "\n")
                    print(f"\n [+] Extracted Data Saved to : [ followers_data.txt ] ({len(followers_list)} followers)\n")
                except Exception as e:
                    print(f"Error saving file: {e}")

            # Update Streamlit progress (dynamic)
            st.info(f"📊 Extracted: {len(followers_list)} followers (+{new_followers_this_iteration} this iteration)")

            # Check stopping conditions - ONLY based on new data availability
            if new_followers_this_iteration == 0:
                no_new_data_count += 1
                print(f"⚠️  No new followers found in this iteration (consecutive count: {no_new_data_count})")

                if no_new_data_count >= 3:  # Stop after 3 consecutive iterations with no new data
                    print("🛑 No new followers found in 3 consecutive iterations.")
                    print("✅ All available followers have been extracted!")
                    st.success(f"🎉 Extraction completed! Found all {len(followers_list)} available followers!")
                    st.info("✅ Reached end of followers list - no more followers to load")
                    break
            else:
                no_new_data_count = 0  # Reset counter if we found new followers
                print(f"✅ Found {new_followers_this_iteration} new followers, continuing...")

            # Optional: Safety limit to prevent infinite loops (very high number)
            if len(followers_list) >= 50000:  # Safety limit for very large accounts
                print("[!] Reached safety limit of 50,000 followers!")
                st.warning(f"⚠️  Reached safety limit: {len(followers_list)} followers extracted")
                st.info("💡 This prevents infinite loops for extremely large accounts")
                break

        # Close browser
        driver.quit()

        # Final results
        if followers_list:
            total_followers = len(followers_list)
            print(f"\n🎉 EXTRACTION COMPLETED!")
            print(f"📊 Total followers extracted: {total_followers}")
            print(f"📁 Data saved to: followers_data.txt")

            st.success(f"🎉 Scraping completed successfully!")
            st.metric("Total Followers Extracted", total_followers)
            st.info("📁 Data saved to: followers_data.txt")

            # Show some sample followers
            if total_followers > 0:
                sample_size = min(5, total_followers)
                st.write("📝 Sample followers:")
                for i in range(sample_size):
                    st.write(f"   • {followers_list[i]}")
                if total_followers > sample_size:
                    st.write(f"   ... and {total_followers - sample_size} more in the file")
        else:
            print("❌ No followers data was extracted")
            st.warning("⚠️ No followers data was extracted. Please check the Instagram page and try again.")
            st.info("💡 Possible reasons:\n- Account is private\n- Login required\n- Network issues\n- Page structure changed")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("💡 Possible solutions:\n- Check your internet connection\n- Verify the Instagram URL is correct\n- Try again later")
        try:
            driver.quit()
        except:
            pass
