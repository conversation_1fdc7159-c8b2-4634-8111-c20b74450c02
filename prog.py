import streamlit as st
import streamlit.components.v1 as components
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from configparser import ConfigParser
import time
import webbrowser
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
import itertools
import os
import re
from pymongo import MongoClient
import sys

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """Check user ID validation"""
    try:
        # MongoDB connection string with timeout
        client = MongoClient(
            "mongodb+srv://instagram_project:<EMAIL>/?retryWrites=true&w=majority",
            serverSelectionTimeoutMS=5000  # 5 second timeout
        )

        # Test connection
        client.admin.command('ping')

        # Access database and collection
        instagram_project_database = client["instagram_project"]
        user_id_collection = instagram_project_database["user_id"]

        # Check if user_id exists
        response = user_id_collection.find_one({"user_id": user_id})

        if response is not None:
            print("UserID - [OK]")
            client.close()
            return True
        else:
            print("UserID - [ERROR]")
            client.close()
            return False

    except Exception as e:
        print(f"Database connection error: {e}")
        return False


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
        # Navigate to competitor's followers page
        driver.get(f"{competitor_link}/followers/")
        time.sleep(5)
        
        # XPath for followers links
        followers_links_xpath = '//*[@class="_aap6 _aap7 _aap8"]/a'
        
        # Main scraping loop
        while len_list_2 < 1000:  # Limit to 1000 followers for free version
            # Get followers profile links
            followers_profiles_links_list = driver.find_elements(By.XPATH, followers_links_xpath)
            time.sleep(3)
            
            # Extract href attributes
            for link in followers_profiles_links_list:
                id = link.get_attribute("href")
                id = id.replace("https://www.instagram.com/", "").replace("/", "")
                list_1.append(id)
            
            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1}")
            
            # Scroll down to load more followers
            scroll_xpath = '//*[@class="_aano"]'
            followers_profiles_links_list = driver.find_elements(By.XPATH, scroll_xpath)
            
            scroll = 0
            while scroll < 3:
                driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", followers_profiles_links_list[0])
                time.sleep(2)
                scroll += 1
            
            # Get updated followers list after scrolling
            followers_profiles_links_list = driver.find_elements(By.XPATH, followers_links_xpath)
            time.sleep(3)
            
            # Extract new followers
            for link in followers_profiles_links_list:
                id = link.get_attribute("href")
                id = id.replace("https://www.instagram.com/", "").replace("/", "")
                list_2.append(id)
                print(f"✔️ Extracted DATA: {id}")
                time.sleep(0.1)
            
            # Remove duplicates and update length
            list_2 = list(dict.fromkeys(list_2))
            len_list_2 = len(list_2)
            print(f"---- Process: Len(List_2) = {len_list_2}")
            
            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")
            
            # Save data to file
            with open("followers_data.txt", "w") as text_file:
                for element in followers_list:
                    text_file.write(element + "\n")
            text_file.close()
            
            print("\n [+] Extracted Data Saved to : [ followers_data.txt ] \n")
            
            # Check if limit reached
            if len(followers_list) >= 1000:
                print("[!] You have reached the scrapping followers limit !!")
                st.success(f"✅ Successfully extracted {len(followers_list)} followers data!")
                st.info("📁 Data saved to: followers_data.txt")
                break

        # Close browser
        driver.quit()
        st.success("🎉 Scraping completed successfully!")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        try:
            driver.quit()
        except:
            pass
