import streamlit as st
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from configparser import Config<PERSON>arser
import time
import os

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """No user validation - always return True"""
    print("UserID validation bypassed - [OK]")
    return True


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
         # Navigate to competitor's profile page first
        print(f"Navigating to profile: {competitor_link}")
        driver.get(competitor_link)
        time.sleep(8)  # Wait for profile page to load

        # Check if we're actually on a login page (more specific check)
        current_url = driver.current_url.lower()
        page_source = driver.page_source.lower()

        # More specific login detection
        login_indicators = [
            "accounts/login" in current_url,
            "login/?next=" in current_url,
            ("log in to instagram" in page_source and "followers" not in page_source),
            ("sign up" in page_source and "log in" in page_source and len(page_source) < 50000)
        ]

        if any(login_indicators):
            st.error("❌ Instagram login required!")
            st.info("💡 Please log in to Instagram manually in your browser first, then try again.")
            print("Login required - redirected to login page")
            print(f"Current URL: {current_url}")
            driver.quit()
            return

        # Check if profile loaded successfully
        if "instagram.com" not in current_url:
            st.error("❌ Failed to load Instagram page")
            driver.quit()
            return

        print("✅ Profile page loaded successfully")

        # Click on followers link to open the followers list
        print("Looking for followers link...")
        followers_clicked = False

        # Try multiple methods to find and click followers
        followers_xpaths = [
            "//span[contains(text(),'followers')]",
            "//span[contains(text(),'follower')]",  # In case of singular
            "//a[contains(@href, '/followers/')]",
            "//span[contains(text(),'abonnés')]",  # French
            "//span[contains(text(),'seguidores')]"  # Spanish
        ]

        for xpath in followers_xpaths:
            try:
                print(f"Trying XPath: {xpath}")
                followers_element = driver.find_element(By.XPATH, xpath)
                print("Found followers element, clicking...")

                # Scroll to element to make sure it's visible
                driver.execute_script("arguments[0].scrollIntoView(true);", followers_element)
                time.sleep(1)

                # Try clicking
                followers_element.click()
                time.sleep(5)  # Wait for followers dialog to open

                # Check if dialog opened
                try:
                    dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                    print("✅ Followers dialog opened successfully!")
                    followers_clicked = True
                    break
                except:
                    print("Dialog not found, trying next method...")
                    continue

            except Exception as e:
                print(f"Failed with XPath {xpath}: {e}")
                continue

        if not followers_clicked:
            print("Could not click followers link with any method")
            st.error("❌ Could not access followers list. The account might be private or the page structure has changed.")
            driver.quit()
            return
        
        # Wait for followers dialog to be fully loaded
        print("Waiting for followers dialog to load completely...")
        time.sleep(3)

        # Verify that followers dialog is open and show current status
        dialog_found = False
        for attempt in range(5):  # Try for 5 seconds
            try:
                dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                if dialog:
                    dialog_found = True
                    print("✅ Followers dialog found and loaded")
                    st.success("✅ Followers dialog opened successfully!")
                    break
            except:
                print(f"Attempt {attempt + 1}: Dialog not found, waiting...")
                time.sleep(1)
                continue

        if not dialog_found:
            print("❌ Followers dialog not found after 5 attempts")
            st.error("❌ Could not open followers dialog. The account might be private.")

            # Debug information
            current_url = driver.current_url
            print(f"Current URL: {current_url}")
            st.info(f"Current URL: {current_url}")

            # Check if we're still on the profile page
            if competitor_link.replace("https://www.instagram.com/", "") in current_url:
                st.info("💡 Still on profile page. The account might be private or followers count might be hidden.")

            driver.quit()
            return

        # Main scraping loop - increased iterations and target
        max_iterations = 50  # Increase iterations to get more followers
        iteration = 0
        no_new_data_count = 0  # Counter for consecutive iterations with no new data

        while iteration < max_iterations:  # Continue until no more followers found
            iteration += 1
            followers_before_iteration = len(list_2)
            print(f"\n{'='*60}")
            print(f"🔄 ITERATION {iteration} - Starting with {followers_before_iteration} followers")
            print(f"{'='*60}")
            st.info(f"🔄 Iteration {iteration} - Extracted {len(list_2)} followers so far...")

            # Try multiple XPath patterns for followers links in the dialog
            followers_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                '//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]'
            ]

            followers_profiles_links_list = []

            # Try each XPath pattern until we find followers
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    # Filter for valid Instagram profile links
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            followers_profiles_links_list.append(link)

                    if followers_profiles_links_list:
                        print(f"Found {len(followers_profiles_links_list)} followers with XPath: {xpath[:50]}...")
                        break
                except Exception as e:
                    print(f"Error with XPath {xpath[:30]}...: {e}")
                    continue

            time.sleep(3)

            # Check if we found any followers links
            if not followers_profiles_links_list:
                print("No followers found with any XPath. Breaking loop.")
                break

            # Extract href attributes more efficiently
            new_followers_found = 0
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_1:  # Avoid duplicates and empty strings
                                list_1.append(id)
                                new_followers_found += 1
                except Exception as e:
                    print(f"Error extracting link: {e}")
                    continue

            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1} (+{new_followers_found} new in this batch)")

            # AGGRESSIVE SCROLLING - Multiple methods to ensure we load ALL followers
            print(f"🔄 SCROLLING to load more followers (current: {len(list_2)})...")

            # Method 1: Direct scrolling of the followers dialog with multiple techniques
            try:
                dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                print("📜 Found followers dialog, starting aggressive scrolling...")

                # Technique 1: Continuous scrolling with JavaScript
                print("   🔄 Technique 1: JavaScript scrolling...")
                for scroll_attempt in range(30):  # Increased attempts
                    # Multiple scroll commands for better coverage
                    driver.execute_script("arguments[0].scrollTop += 500", dialog)
                    time.sleep(1)
                    driver.execute_script("arguments[0].scrollBy(0, 300)", dialog)
                    time.sleep(1)

                    # Every 5 scrolls, do a big scroll to bottom
                    if scroll_attempt % 5 == 0:
                        driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", dialog)
                        time.sleep(2)
                        print(f"      Big scroll #{scroll_attempt//5 + 1}")

                # Technique 2: Mouse wheel events
                print("   🖱️  Technique 2: Mouse wheel simulation...")
                for wheel_attempt in range(20):
                    driver.execute_script("""
                        var dialog = arguments[0];
                        var wheelEvent = new WheelEvent('wheel', {
                            deltaY: 1000,
                            bubbles: true,
                            cancelable: true
                        });
                        dialog.dispatchEvent(wheelEvent);
                    """, dialog)
                    time.sleep(0.5)

                # Technique 3: Find and scroll specific scrollable elements inside dialog
                print("   📋 Technique 3: Scrolling internal elements...")
                internal_scroll_selectors = [
                    '//div[@role="dialog"]//div[contains(@class, "_aano")]',
                    '//div[@role="dialog"]//div[contains(@style, "overflow")]',
                    '//div[@role="dialog"]//div[contains(@style, "height")]'
                ]

                for selector in internal_scroll_selectors:
                    try:
                        internal_elements = driver.find_elements(By.XPATH, selector)
                        for element in internal_elements:
                            for i in range(10):
                                driver.execute_script("arguments[0].scrollTop += 400", element)
                                time.sleep(0.5)
                    except:
                        continue

                # Technique 4: ActionChains scrolling (more natural)
                print("   🖱️  Technique 4: ActionChains scrolling...")
                try:
                    actions = ActionChains(driver)
                    # Move to dialog and scroll
                    actions.move_to_element(dialog).perform()
                    time.sleep(1)

                    for action_scroll in range(15):
                        # Scroll down with mouse wheel
                        actions.scroll_by_amount(0, 500).perform()
                        time.sleep(0.8)

                        # Occasionally scroll up a bit and then down more (natural behavior)
                        if action_scroll % 4 == 0:
                            actions.scroll_by_amount(0, -200).perform()
                            time.sleep(0.3)
                            actions.scroll_by_amount(0, 700).perform()
                            time.sleep(1)

                except Exception as e:
                    print(f"      ActionChains scrolling failed: {e}")

                # Technique 5: Keyboard scrolling
                print("   ⌨️  Technique 5: Keyboard scrolling...")
                try:
                    dialog.click()  # Focus on dialog
                    time.sleep(1)
                    for key_scroll in range(20):
                        driver.execute_script("""
                            var dialog = arguments[0];
                            var keyEvent = new KeyboardEvent('keydown', {
                                key: 'PageDown',
                                code: 'PageDown',
                                bubbles: true
                            });
                            dialog.dispatchEvent(keyEvent);
                        """, dialog)
                        time.sleep(0.5)
                except Exception as e:
                    print(f"      Keyboard scrolling failed: {e}")

                print("✅ All scrolling techniques completed")

            except Exception as e:
                print(f"❌ Dialog scrolling failed: {e}")

                # Fallback: Try page-level scrolling
                print("🔄 Fallback: Page-level scrolling...")
                try:
                    for fallback_scroll in range(20):
                        driver.execute_script("window.scrollBy(0, 500);")
                        time.sleep(1)
                        # Also try scrolling up and down to trigger loading
                        if fallback_scroll % 3 == 0:
                            driver.execute_script("window.scrollBy(0, -200);")
                            time.sleep(0.5)
                            driver.execute_script("window.scrollBy(0, 700);")
                            time.sleep(1)
                except Exception as e:
                    print(f"Fallback scrolling failed: {e}")

            # Wait longer for content to load after aggressive scrolling
            print("⏳ Waiting for new followers to load after scrolling...")
            time.sleep(6)  # Longer wait

            # Get ALL followers after scrolling - use multiple methods to ensure we get everything
            print("🔍 Scanning for all followers after scrolling...")
            updated_followers_list = []

            # Try multiple XPath patterns and combine results
            all_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                # Additional patterns for better coverage
                '//div[@role="dialog"]//a[contains(@href, "instagram.com/")]',
                '//div[@role="dialog"]//a[starts-with(@href, "/")]'
            ]

            for xpath in all_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    print(f"   Found {len(temp_links)} links with pattern: {xpath[:50]}...")

                    for link in temp_links:
                        try:
                            href = link.get_attribute("href")
                            if href and "instagram.com/" in href:
                                # More comprehensive filtering
                                if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/", "/direct/", "/help/"]):
                                    updated_followers_list.append(link)
                        except:
                            continue

                except Exception as e:
                    print(f"   Error with pattern {xpath[:30]}...: {e}")
                    continue

            # Remove duplicate links based on href
            unique_links = []
            seen_hrefs = set()
            for link in updated_followers_list:
                try:
                    href = link.get_attribute("href")
                    if href and href not in seen_hrefs:
                        unique_links.append(link)
                        seen_hrefs.add(href)
                except:
                    continue

            updated_followers_list = unique_links
            print(f"📊 Total unique follower links found: {len(updated_followers_list)}")

            time.sleep(2)

            # Extract new followers more efficiently
            new_followers_this_batch = 0
            for link in updated_followers_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        # More thorough filtering
                        if not any(x in href for x in ["/p/", "/reel/", "/tv/", "/stories/", "/explore/", "/accounts/"]):
                            id = href.replace("https://www.instagram.com/", "").replace("/", "").strip()
                            if id and len(id) > 0 and id not in list_2:  # Avoid duplicates and empty strings
                                list_2.append(id)
                                new_followers_this_batch += 1
                                if new_followers_this_batch <= 5:  # Only print first 5 to avoid spam
                                    print(f"✔️ Extracted DATA: {id}")
                except Exception as e:
                    print(f"Error extracting new link: {e}")
                    continue
                time.sleep(0.05)  # Reduced delay for faster processing

            print(f"📊 Extracted {new_followers_this_batch} new followers in this batch")

            # Verify scrolling effectiveness
            followers_after_iteration = len(list_2)
            followers_gained_this_iteration = followers_after_iteration - followers_before_iteration

            print(f"📈 ITERATION {iteration} SUMMARY:")
            print(f"   Before: {followers_before_iteration} followers")
            print(f"   After:  {followers_after_iteration} followers")
            print(f"   Gained: {followers_gained_this_iteration} new followers")

            if followers_gained_this_iteration == 0:
                print("⚠️  WARNING: No new followers found in this iteration!")
                print("   This might indicate:")
                print("   - Scrolling is not working properly")
                print("   - We've reached the end of the list")
                print("   - Instagram is rate limiting")
            else:
                print(f"✅ SUCCESS: Found {followers_gained_this_iteration} new followers!")

            # Remove duplicates and calculate new followers accurately
            previous_count = len(list_2)
            list_2 = list(dict.fromkeys(list_2))  # Remove duplicates while preserving order
            len_list_2 = len(list_2)
            new_followers_this_iteration = len_list_2 - previous_count

            print(f"---- Process: Len(List_2) = {len_list_2} (+{new_followers_this_iteration} new)")

            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")

            # Save data to file after each iteration
            if followers_list:  # Only save if we have data
                try:
                    with open("followers_data.txt", "w", encoding='utf-8') as text_file:
                        for element in followers_list:
                            text_file.write(element + "\n")
                    print(f"\n [+] Extracted Data Saved to : [ followers_data.txt ] ({len(followers_list)} followers)\n")
                except Exception as e:
                    print(f"Error saving file: {e}")

            # Update Streamlit progress (dynamic)
            st.info(f"📊 Extracted: {len(followers_list)} followers (+{new_followers_this_iteration} this iteration)")

            # More aggressive stopping conditions - give more chances
            if new_followers_this_iteration == 0:
                no_new_data_count += 1
                print(f"⚠️  No new followers found in this iteration (consecutive count: {no_new_data_count})")

                # Try more iterations before giving up
                if no_new_data_count >= 5:  # Increased from 3 to 5
                    print("🛑 No new followers found in 5 consecutive iterations.")
                    print("✅ All available followers have been extracted!")
                    st.success(f"🎉 Extraction completed! Found all {len(followers_list)} available followers!")
                    st.info("✅ Reached end of followers list - no more followers to load")
                    break
                elif no_new_data_count >= 3:
                    print("⚠️  3 iterations without new followers - trying extra aggressive scrolling...")
                    # Extra aggressive scrolling when we're not finding new followers
                    try:
                        dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                        for extra_scroll in range(50):  # Extra scrolling
                            driver.execute_script("arguments[0].scrollTop += 200", dialog)
                            time.sleep(0.3)
                            if extra_scroll % 10 == 0:
                                driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", dialog)
                                time.sleep(1)
                        print("✅ Extra aggressive scrolling completed")
                        time.sleep(3)
                    except:
                        print("❌ Extra scrolling failed")
            else:
                no_new_data_count = 0  # Reset counter if we found new followers
                print(f"✅ Found {new_followers_this_iteration} new followers, continuing...")

            # Safety limit to prevent infinite loops
            if len(followers_list) >= 50000:
                print("[!] Reached safety limit of 50,000 followers!")
                st.warning(f"⚠️  Reached safety limit: {len(followers_list)} followers extracted")
                st.info("💡 This prevents infinite loops for extremely large accounts")
                break

        # Close browser
        driver.quit()

        # Final results
        if followers_list:
            total_followers = len(followers_list)
            print(f"\n🎉 EXTRACTION COMPLETED!")
            print(f"📊 Total followers extracted: {total_followers}")
            print(f"📁 Data saved to: followers_data.txt")

            st.success(f"🎉 Scraping completed successfully!")
            st.metric("Total Followers Extracted", total_followers)
            st.info("📁 Data saved to: followers_data.txt")

            # Show some sample followers
            if total_followers > 0:
                sample_size = min(5, total_followers)
                st.write("📝 Sample followers:")
                for i in range(sample_size):
                    st.write(f"   • {followers_list[i]}")
                if total_followers > sample_size:
                    st.write(f"   ... and {total_followers - sample_size} more in the file")
        else:
            print("❌ No followers data was extracted")
            st.warning("⚠️ No followers data was extracted. Please check the Instagram page and try again.")
            st.info("💡 Possible reasons:\n- Account is private\n- Login required\n- Network issues\n- Page structure changed")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("💡 Possible solutions:\n- Check your internet connection\n- Verify the Instagram URL is correct\n- Try again later")
        try:
            driver.quit()
        except:
            pass
