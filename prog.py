import streamlit as st
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from configparser import ConfigParser
import time
import os

# Get current working directory
cwd = os.getcwd()

# Config file setup
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get configuration data
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


def state_101():
    """No user validation - always return True"""
    print("UserID validation bypassed - [OK]")
    return True


def program():
    """Main Instagram scraping program"""
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Initialize lists for storing data
        list_1 = []
        list_2 = []
        len_list_1 = 0
        len_list_2 = 0
        followers_list = []
        
         # Navigate to competitor's profile page first
        print(f"Navigating to profile: {competitor_link}")
        driver.get(competitor_link)
        time.sleep(8)  # Wait for profile page to load

        # Check if we need to handle login
        page_source = driver.page_source.lower()
        if "login" in page_source or "log in" in page_source:
            st.warning("⚠️ Instagram requires login. Please make sure you're logged in to Instagram in your browser.")
            print("Login required - please log in to Instagram first")
            driver.quit()
            return

        # Click on followers link to open the followers list
        print("Looking for followers link...")
        try:
            liste_followers_xpath = "//span[contains(text(),'followers')]"
            liste_followers = driver.find_element(By.XPATH, liste_followers_xpath)
            print("Found followers link, clicking...")
            liste_followers.click()
            time.sleep(5)  # Wait for followers dialog to open
            print("Followers dialog should be open now")
        except Exception as e:
            print(f"Error clicking followers link: {e}")
            # Try alternative methods to find followers link
            try:
                print("Trying alternative followers link...")
                alt_followers_xpath = "//a[contains(@href, '/followers/')]"
                alt_followers = driver.find_element(By.XPATH, alt_followers_xpath)
                alt_followers.click()
                time.sleep(5)
            except Exception as e2:
                print(f"Could not find followers link: {e2}")
                st.error("❌ Could not access followers list. The account might be private or login is required.")
                driver.quit()
                return
        
        # Main scraping loop
        max_iterations = 10  # Prevent infinite loop
        iteration = 0

        while len_list_2 < 1000 and iteration < max_iterations:  # Limit to 1000 followers for free version
            iteration += 1
            print(f"Iteration {iteration}")

            # Try multiple XPath patterns for followers links in the dialog
            followers_xpaths = [
                '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]',
                '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a',
                '//div[@role="dialog"]//div[contains(@class, "_aacl")]//a',
                '//div[@role="dialog"]//a[@role="link"]',
                '//div[contains(@class, "_aano")]//a[contains(@href, "/")]',
                '//a[contains(@href, "/") and not(contains(@href, "/p/")) and not(contains(@href, "/reel/")) and not(contains(@href, "/tv/"))]'
            ]

            followers_profiles_links_list = []

            # Try each XPath pattern until we find followers
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    # Filter for valid Instagram profile links
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            followers_profiles_links_list.append(link)

                    if followers_profiles_links_list:
                        print(f"Found {len(followers_profiles_links_list)} followers with XPath: {xpath[:50]}...")
                        break
                except Exception as e:
                    print(f"Error with XPath {xpath[:30]}...: {e}")
                    continue

            time.sleep(3)

            # Check if we found any followers links
            if not followers_profiles_links_list:
                print("No followers found with any XPath. Breaking loop.")
                break

            # Extract href attributes
            for link in followers_profiles_links_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href and "/p/" not in href:
                        id = href.replace("https://www.instagram.com/", "").replace("/", "")
                        if id and id not in list_1:  # Avoid duplicates
                            list_1.append(id)
                except Exception as e:
                    print(f"Error extracting link: {e}")
                    continue

            # Remove duplicates and get length
            list_1 = list(dict.fromkeys(list_1))
            len_list_1 = len(list_1)
            print(f"---- Process: Len(List_1) = {len_list_1}")

            # Scroll down to load more followers - focus on dialog scrolling
            scroll_success = False

            # Method 1: Try to find and scroll the followers dialog specifically
            scroll_xpaths = [
                '//div[@role="dialog"]//div[contains(@class, "_aano")]',
                '//div[@role="dialog"]//div[contains(@style, "overflow")]',
                '//div[@role="dialog"]//div[contains(@class, "x1n2onr6")]',
                '//div[contains(@class, "_aano")]',
                '//div[@role="dialog"]'
            ]

            for scroll_xpath in scroll_xpaths:
                try:
                    scroll_elements = driver.find_elements(By.XPATH, scroll_xpath)
                    if scroll_elements:
                        for i in range(3):  # Scroll 3 times
                            driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_elements[0])
                            time.sleep(2)
                        scroll_success = True
                        print("Successfully scrolled using dialog method")
                        break
                except Exception as e:
                    continue

            # Method 2: If dialog scroll failed, try page scroll
            if not scroll_success:
                try:
                    print("Trying page scroll method...")
                    for i in range(3):
                        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                        time.sleep(2)
                    scroll_success = True
                except Exception as e:
                    print(f"Error with page scroll: {e}")

            # Method 3: Try keyboard scrolling
            if not scroll_success:
                try:
                    print("Trying keyboard scroll method...")
                    body = driver.find_element(By.TAG_NAME, "body")
                    for i in range(5):
                        body.send_keys(Keys.PAGE_DOWN)
                        time.sleep(1)
                except Exception as e:
                    print(f"Error with keyboard scroll: {e}")

            # Get updated followers list after scrolling using the same XPath patterns
            updated_followers_list = []
            for xpath in followers_xpaths:
                try:
                    temp_links = driver.find_elements(By.XPATH, xpath)
                    for link in temp_links:
                        href = link.get_attribute("href")
                        if (href and "instagram.com/" in href and
                            "/p/" not in href and "/reel/" not in href and
                            "/tv/" not in href and "/stories/" not in href):
                            updated_followers_list.append(link)

                    if updated_followers_list:
                        break
                except Exception as e:
                    continue

            time.sleep(3)

            # Extract new followers
            for link in updated_followers_list:
                try:
                    href = link.get_attribute("href")
                    if href and "instagram.com/" in href:
                        id = href.replace("https://www.instagram.com/", "").replace("/", "")
                        if id and id not in list_2 and len(id) > 0:  # Avoid duplicates and empty strings
                            list_2.append(id)
                            print(f"✔️ Extracted DATA: {id}")
                except Exception as e:
                    print(f"Error extracting new link: {e}")
                    continue
                time.sleep(0.1)

            # Remove duplicates and update length
            list_2 = list(dict.fromkeys(list_2))
            len_list_2 = len(list_2)
            print(f"---- Process: Len(List_2) = {len_list_2}")

            # Update followers list
            followers_list = list_2
            print(f"\nCurrent Followers Data Volume : {len(followers_list)}\n")

            # Save data to file
            if followers_list:  # Only save if we have data
                try:
                    with open("followers_data.txt", "w", encoding='utf-8') as text_file:
                        for element in followers_list:
                            text_file.write(element + "\n")
                    print("\n [+] Extracted Data Saved to : [ followers_data.txt ] \n")
                except Exception as e:
                    print(f"Error saving file: {e}")

            # Check if limit reached or no new data
            if len(followers_list) >= 1000:
                print("[!] You have reached the scrapping followers limit !!")
                st.success(f"✅ Successfully extracted {len(followers_list)} followers data!")
                st.info("📁 Data saved to: followers_data.txt")
                break
            elif len_list_2 == len_list_1:  # No new data found
                print("No new followers found. Ending scraping.")
                break

        # Close browser
        driver.quit()

        if followers_list:
            st.success(f"🎉 Scraping completed successfully! Extracted {len(followers_list)} followers.")
            st.info("📁 Data saved to: followers_data.txt")
        else:
            st.warning("⚠️ No followers data was extracted. Please check the Instagram page and try again.")

    except Exception as e:
        print(f"Error in program execution: {e}")
        st.error(f"❌ An error occurred: {str(e)}")
        st.info("💡 Possible solutions:\n- Check your internet connection\n- Verify the Instagram URL is correct\n- Try again later")
        try:
            driver.quit()
        except:
            pass
