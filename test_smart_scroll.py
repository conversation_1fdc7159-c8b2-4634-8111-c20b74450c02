#!/usr/bin/env python3
"""
Test du scrolling intelligent basé sur la détection de hauteur
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from configparser import ConfigParser
import time
import os

def test_smart_scrolling():
    """Test du scrolling intelligent avec détection de hauteur"""
    
    # Load config
    cwd = os.getcwd()
    config_file = "config.ini"
    parser = ConfigParser()
    parser.read(config_file, encoding='utf-8')
    
    core = parser["Program_Data"]["core"]
    profile = parser["Program_Data"]["profile"]
    competitor_link = parser["Program_Data"]["competitor_link"]
    
    print("🧪 TEST DU SCROLLING INTELLIGENT")
    print(f"Target: {competitor_link}")
    print("=" * 60)
    
    try:
        # Setup Chrome
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram and profile
        print("📱 Going to Instagram...")
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        print("👤 Going to competitor profile...")
        driver.get(competitor_link)
        time.sleep(8)
        
        # Click followers
        print("👆 Clicking followers...")
        followers_xpath = "//span[contains(text(),'followers')]"
        followers_element = driver.find_element(By.XPATH, followers_xpath)
        followers_element.click()
        time.sleep(5)
        
        # Find followers container
        print("🔍 Finding followers container...")
        followers_container = None
        container_selectors = [
            '//div[@role="dialog"]//div[contains(@class, "_aano")]',
            '//div[@role="dialog"]//div[contains(@style, "overflow")]',
            '//div[@role="dialog"]//div[contains(@style, "height")]',
            '//div[@role="dialog"]'
        ]
        
        for selector in container_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    followers_container = elements[0]
                    print(f"✅ Found container with: {selector}")
                    break
            except:
                continue
        
        if not followers_container:
            print("❌ No container found!")
            return
        
        # Count initial followers
        initial_followers = count_followers(driver)
        print(f"📊 Initial followers: {initial_followers}")
        
        # Test smart scrolling
        print("\n🔄 Starting smart scrolling test...")
        
        # Paramètres de scrolling
        scroll_pause_time = 2
        max_scrolls = 20  # Limite pour le test
        scroll_count = 0
        last_height = driver.execute_script("return arguments[0].scrollHeight", followers_container)
        
        print(f"📏 Initial container height: {last_height}px")
        
        followers_counts = [initial_followers]
        
        while scroll_count < max_scrolls:
            print(f"\n--- Scroll {scroll_count + 1} ---")
            
            # Scroll to bottom
            driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", followers_container)
            time.sleep(scroll_pause_time)
            
            # Check new height
            new_height = driver.execute_script("return arguments[0].scrollHeight", followers_container)
            current_position = driver.execute_script("return arguments[0].scrollTop", followers_container)
            
            # Count followers
            current_followers = count_followers(driver)
            followers_counts.append(current_followers)
            
            print(f"Height: {last_height}px → {new_height}px")
            print(f"Position: {current_position}px")
            print(f"Followers: {current_followers} (+{current_followers - followers_counts[-2]})")
            
            # Check if we reached the end
            if new_height == last_height:
                print("⚠️  Height unchanged")
                max_scroll_position = new_height - driver.execute_script("return arguments[0].clientHeight", followers_container)
                if current_position >= max_scroll_position - 100:
                    print("🛑 Reached the end!")
                    break
            else:
                print("✅ New content loaded")
                last_height = new_height
            
            scroll_count += 1
        
        # Final results
        final_followers = count_followers(driver)
        print(f"\n📊 FINAL RESULTS:")
        print(f"Initial followers: {initial_followers}")
        print(f"Final followers: {final_followers}")
        print(f"Total loaded: {final_followers - initial_followers}")
        print(f"Scrolls performed: {scroll_count}")
        print(f"Final height: {last_height}px")
        
        if final_followers > initial_followers:
            print("🎉 SUCCESS! Smart scrolling works!")
            print(f"✅ Loaded {final_followers - initial_followers} additional followers")
        else:
            print("❌ FAILED! No additional followers loaded")
            print("💡 Possible issues:")
            print("   - Container selector is wrong")
            print("   - Instagram changed structure")
            print("   - Account is private")
            print("   - Rate limiting")
        
        # Show progression
        print(f"\n📈 Progression:")
        for i, count in enumerate(followers_counts):
            if i == 0:
                print(f"   Start: {count} followers")
            else:
                diff = count - followers_counts[i-1]
                print(f"   Scroll {i}: {count} followers (+{diff})")
        
        # Keep browser open
        print(f"\n🔍 Browser staying open for 30 seconds for manual inspection...")
        time.sleep(30)
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        try:
            driver.quit()
        except:
            pass

def count_followers(driver):
    """Count visible followers"""
    try:
        links = driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]')
        valid_count = 0
        for link in links:
            try:
                href = link.get_attribute("href")
                if href and "instagram.com/" in href and "/p/" not in href and "/reel/" not in href:
                    valid_count += 1
            except:
                continue
        return valid_count
    except:
        return 0

if __name__ == "__main__":
    test_smart_scrolling()
