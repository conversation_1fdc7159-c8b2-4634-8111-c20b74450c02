#!/usr/bin/env python3
"""
Test script to verify Instagram access and followers dialog opening
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from configparser import ConfigParser
import time
import os

def test_instagram_access():
    """Test if we can access Instagram and open followers dialog"""
    
    # Get current working directory
    cwd = os.getcwd()
    
    # Load config
    config_file = "config.ini"
    parser = ConfigParser()
    parser.read(config_file, encoding='utf-8')
    
    core = parser["Program_Data"]["core"]
    profile = parser["Program_Data"]["profile"]
    competitor_link = parser["Program_Data"]["competitor_link"]
    
    print(f"🔍 Testing Instagram access for: {competitor_link}")
    
    try:
        # Setup Chrome profile path
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        
        # Chrome options setup
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        # Chrome preferences
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        # Initialize Chrome driver
        print("🚀 Starting Chrome browser...")
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram
        print("📱 Navigating to Instagram...")
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Navigate to competitor's profile
        print(f"👤 Navigating to profile: {competitor_link}")
        driver.get(competitor_link)
        time.sleep(8)
        
        # Check if login is required
        page_source = driver.page_source.lower()
        if "login" in page_source or "log in" in page_source:
            print("⚠️  Login required - please log in to Instagram first")
            input("Press Enter after logging in...")
            driver.get(competitor_link)
            time.sleep(5)
        
        # Look for followers link
        print("🔍 Looking for followers link...")
        try:
            liste_followers_xpath = "//span[contains(text(),'followers')]"
            liste_followers = driver.find_element(By.XPATH, liste_followers_xpath)
            print("✅ Found followers link!")
            
            # Click on followers
            print("👆 Clicking on followers...")
            liste_followers.click()
            time.sleep(5)
            
            # Check if dialog opened
            try:
                dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
                print("✅ Followers dialog opened successfully!")
                
                # Try to find some followers
                followers_xpaths = [
                    '//div[@role="dialog"]//a[contains(@href, "/")]',
                    '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a'
                ]
                
                followers_found = 0
                for xpath in followers_xpaths:
                    try:
                        links = driver.find_elements(By.XPATH, xpath)
                        for link in links:
                            href = link.get_attribute("href")
                            if href and "instagram.com/" in href and "/p/" not in href:
                                followers_found += 1
                        if followers_found > 0:
                            break
                    except:
                        continue
                
                print(f"✅ Found {followers_found} potential followers in dialog!")
                
                if followers_found > 0:
                    print("🎉 Test PASSED - Instagram access and followers dialog working!")
                else:
                    print("⚠️  Dialog opened but no followers found - might need login or account is private")
                
            except Exception as e:
                print(f"❌ Could not find followers dialog: {e}")
                
        except Exception as e:
            print(f"❌ Could not find followers link: {e}")
            print("💡 This might mean:")
            print("   - Account is private")
            print("   - Login is required")
            print("   - Page structure has changed")
        
        # Keep browser open for manual inspection
        print("\n🔍 Browser will stay open for 30 seconds for manual inspection...")
        time.sleep(30)
        
        driver.quit()
        print("✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        try:
            driver.quit()
        except:
            pass

if __name__ == "__main__":
    print("🧪 Testing Instagram Access and Followers Dialog\n")
    test_instagram_access()
