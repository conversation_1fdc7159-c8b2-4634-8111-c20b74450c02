{"NewTabPage": {"PrevNavigationTime": "*****************"}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 107}, "autofill": {"orphan_rows_removed": true}, "browser": {"has_seen_welcome_page": false, "navi_onboard_group": "", "window_placement": {"bottom": 828, "left": 16, "maximized": true, "right": 1072, "top": 16, "work_area_bottom": 824, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17498, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.349895, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "b358f25a-30a1-48e1-ab29-e8a0bbce77e4"}}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "language_model_counters": {"en": 9}, "media": {"device_id_salt": "97DFF3668EB6DB6A593A577F216E9E0C", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "glnO5I6L8cxQx/PXI36khmsjhdlXWncHTHOYCVO41rnYmy4cAxtcfQI/VbbSS0VlByUFS5YwsyvtfHqhDAePLg=="}, "ntp": {"num_personal_suggestions": 4}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}, "last_fetch_attempt": "*****************"}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true}, "store_file_paths_to_delete": {}}, "plugins": {"plugins_list": []}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"accessibility_events": {}, "app_banner": {}, "ar": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {"https://www.instagram.com:443,*": {"last_modified": "13394700856512388", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "cookies": {}, "durable_storage": {}, "fedcm_active_session": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "get_display_media_set_select_all_screens": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {"https://www.instagram.com:443,*": {"expiration": "13402476974851223", "last_modified": "13394700974851241", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 13}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "ppapi_broker": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://www.instagram.com:443,*": {"last_modified": "13394700867433815", "setting": {"lastEngagementTime": 1.3394700867433788e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 29.52384}}}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "107.0.5304.88", "creation_time": "13394668790249657", "exit_type": "Normal", "last_engagement_time": "13394700867433788", "last_time_obsolete_http_credentials_removed": 1750195250.691884, "last_time_password_store_metrics_reported": 1750195220.690698, "managed_user_id": "", "name": "Person 1", "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13394668790"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394675319786558", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394675482150681", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394675523955299", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394677593536459", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394677632650867", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394696819384172", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394696859854977", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394697430923494", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394697497049309", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394698626071500", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394698648751354", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394698726139197", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394698748683938", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394699133889000", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394699186584721", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394700050189276", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394700172343625", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394700842964263", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394700974843338", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "107"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"bernard lacombe alzheimer\",\"geely\",\"crb mca\",\"manchester city vs wydad\",\"audi q3 2025\",\"rayan kolli\",\"tirage euromillions\",\"ici tout commence\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:headertexts\":{\"a\":{\"8\":\"Recherches populaires\"}},\"google:suggestdetail\":[{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"a\":\"Footballeur\",\"dc\":\"#163775\",\"i\":\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3N//AABEIAEAAQAMBIgACEQEDEQH/xAAcAAACAwEBAQEAAAAAAAAAAAAFBgIEBwMBCAD/xAA2EAACAQIEAwUGAwkAAAAAAAABAgMEEQAFEiEGMUETMlFxkQciYYGh8COxwRQVF0NScpLh8f/EABoBAAMBAQEBAAAAAAAAAAAAAAECAwQFBgD/xAAeEQEAAgICAwEAAAAAAAAAAAABAAIDERIhBCIxQf/aAAwDAQACEQMRAD8A1NRcY/PGskbRsPdYFT5HEgMRnlSnhaWQ2VcW3E1BlItNldGkVzv752xWTiXLTV9gZ1BIuCdt/DGc8a8XzyuaWBmSMDTpBIJ88JElfKrreQ2BuxO++F5MbiTeKTiSKsmaOB0bSbCyk6tulvvbB2CSOeMNGwbx6Y+bafM54rlGYKtjsefiPrjQOA+Ij2yxSTOLnY6vseuBySHiPyao6nHO2+Oyus0SvGdSkc8eCPxxUZJJMYF8VSGDIaufcrDGzkDrYbfXGY0vtBeMKP2ytjtzLOJPowb88Ev4hJU0UtNVVFNPHLEY5Uli06wRYi4YWvv0wXEvxIeczHMZneZ5JT7/ADN8VqJXlnQke6vw54nm0RjqH0lmjLHSzMGJ8yNsX8rgBhjYqdxexHLGe3UtXtjnw/m+XGMw1TRFkF2BjBFut7jAW9PTcZTQUJU0byKYjGbrpdQTa3S5PlieTmiXOadJIYAkiNHKJHCKQQQbnyxCcUycW11RDEIqaGZY00bAFFCG3zU4lQ9tEvkfU3N1ypWXLKcSEltG5OLWMxm9pOYsLQ0tIg8XDMfzGKU/tAzt+VVFD/ZCv6g43VwX1MLc3F2XhahkZhR53lNRYA6o6tbC5a43PwH688cabg6esWRqOSCoCP2bCOqjBDfMi4+IxdyV5/3lQRVVJTBaunYG/wCGZbOdiBzICk2I3vvgrmWU0VDSzVE+W0i08JDB3cHawFjdLk3O29+XwxnctTrcfqKVfwhnWXxq1RTOqubKyspF7EkbHwBPyxTySRSsqrJrYEemPZKQTU89Y0cNHTN3QV3lPOxIF7bf9wNiqWhrDUUsQjU/ytV9vC+KWxth1Pq2KoseuHKGWWoqapUW1NA73aQrvY2sBz8jthYky3NpUWSHtJ1kUyWRrm17EkeeCVJxPHBQ1UcCv21RAY+53Sdjc8uWBlHWV1NKZqJ5rKrbx32U94HwG1/MfPB8bCvJsRs+U6Bg+Tt1vHMrqykghxYg9QeuIe/cHkfTDDSCo4jllka0k8SKWYKNTi4A2uOX3fYYJxcLaGQVEmk6+zZuyLEk2AseWm5G+1gSemEvatXSxA3GlqWNqyKYWWOMRgR6b93X1v11j/EYWuPKkVVZDBFMiQUkfbzK4JV2J91WHkp9fjhmO57x+RxmGb1bStXzlpNdTKV0HSQFGwuDurC33bHP8Eb5OT+RZTq8xao1QqOzptZZIQbhOewPhufXHaly+efLJ65WiEULEFC/vnkSQvhvgMCwfccsX6Weukh/ZYJZ+y3/AAlchTfnf/eOzS79gQfsiGsD4k4P5BnNNQUkkM8eqTWXS21zYW6EX2tvYbnAOGjnllMYTSVNmLGwXmd/Q4ux0UEaM8rM73CRi2lb33PiduXQ39WtYTUSojuNns3yqaOkrc3mjCiR1ghUix07sx8u79cOJdv6B8jhe4ClnkymujdbRxWZQx3JuL/S+DYnXqvpjzvkrbIrNF68dT//2Q\\u003d\\u003d\",\"q\":\"gs_ssp\\u003deJzj4tVP1zc0LE5PybGoKqgwYPTiLkqsTMxTyM7PyckEAIGhCV8\",\"t\":\"Rayan Kolli\",\"zae\":\"/g/11sgdl8zpx\",\"zl\":8},{\"zl\":8},{\"a\":\"Feuilleton télévisé\",\"dc\":\"#424242\",\"i\":\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEAAkGBwgHBgkIBwgKCgkLDRYPDQwMDRsUFRAWIB0iIiAdHx8kKDQsJCYxJx8fLT0tMTU3Ojo6Iys/RD84QzQ5OjcBCgoKDQwNGg8PGjclHyU3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3N//AABEIAEAAKwMBIgACEQEDEQH/xAAbAAADAAIDAAAAAAAAAAAAAAADBAUCBgABB//EACoQAAIBAwQABAYDAAAAAAAAAAECAwAEEQUSITETIkGBBjJCUXHBYZGh/8QAFwEAAwEAAAAAAAAAAAAAAAAAAAEDAv/EABwRAQACAgMBAAAAAAAAAAAAAAAREgECAyFhQf/aAAwDAQACEQMRAD8A9MVaIBXEFEUc1plC1P4q0LSbsWuoajFFNwCu1m2Z63FQQvvirVvLFcQpNA6yRONyupyGH3Brzc6TpUljdRT2BnnlnczMVO52JO593f8AVbd8FWR0uyuNO3M8UMmYWYdowBFT15JzC2/FXWV8rXYTis8ZogXiqJE1FEArEdUZIN8YLfNjPfH4oDUdStZLfVTt8VYZDldseQSSc8+9WdNvPG3h1VW3YXB+dQBg/n+KrtCs8LW8oJRuwKlXNqtnqTPEg2zchRwAejj7VKtdpwve+tcqkZyKKOqEiFTg+lFqqBNeqatWBiTd2ODn1pQHimLfa8bLnkdjPNAMqoDY7pO+sRPMsiuI2A5Y88Dkfujo8gYBcfbqgveeHqiW0nCvFuBI4zk8Z/Aoz6eJ+CSttcDJPGOsVkDS91OC6pnJUnkVkrcCggM0pqN1Y2cAl1ANsy2GTOfKjOc4P2Q/5Xd7HJPaTRQzGGR0IWQfSfb9VPi0a/mOV1SSEeHsXazsU9ONzEH1O4gt6E44oC3Z3tlJkLbMrIql1YKxXO71BI+k9GmlaC6jeJA3nB+c5wR78Vrr/DmpbcRa/OuInQARgDJEoVuMYI8RTgYHkHHWDnRdUEpCa3KsTQrH4XmPmGznfncN2HBPfmBGCOV3J9Q5bHFUVbyioOm2FzZ6hdtcXTTI21UQsxEe0AHG4nsgnPfNW16psv/Z\",\"q\":\"gs_ssp\\u003deJzj4tVP1zc0zMorMTctMMowYPQSzEzOVCjJLy1RSM7PzU3NS04FALPACxU\",\"t\":\"Ici tout commence\",\"zae\":\"/g/11jnt75p2h\",\"zl\":8}],\"google:suggesteventid\":\"-7093498309395977752\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\",\"ENTITY\"]}]"}}