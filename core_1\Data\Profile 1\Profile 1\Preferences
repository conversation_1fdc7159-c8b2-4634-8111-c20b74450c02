{"NewTabPage": {"PrevNavigationTime": "*****************"}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 107}, "autofill": {"orphan_rows_removed": true}, "browser": {"has_seen_welcome_page": false, "navi_onboard_group": "", "window_placement": {"bottom": 828, "left": 16, "maximized": true, "right": 1072, "top": 16, "work_area_bottom": 824, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 17498, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.349895, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "cb07f70f-7ffb-473f-a632-81a5154b2a0b"}}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "language_model_counters": {"en": 7}, "media": {"device_id_salt": "97DFF3668EB6DB6A593A577F216E9E0C", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "glnO5I6L8cxQx/PXI36khmsjhdlXWncHTHOYCVO41rnYmy4cAxtcfQI/VbbSS0VlByUFS5YwsyvtfHqhDAePLg=="}, "ntp": {"num_personal_suggestions": 4}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}, "last_fetch_attempt": "*****************"}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true}, "store_file_paths_to_delete": {}}, "plugins": {"plugins_list": []}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"accessibility_events": {}, "app_banner": {}, "ar": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {"https://www.instagram.com:443,*": {"last_modified": "13394698739279926", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "cookies": {}, "durable_storage": {}, "fedcm_active_session": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "get_display_media_set_select_all_screens": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {"https://www.instagram.com:443,*": {"expiration": "13402474748694690", "last_modified": "13394698748694699", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 10}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "ppapi_broker": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"https://www.instagram.com:443,*": {"last_modified": "13394698739337826", "setting": {"lastEngagementTime": 1.3394698739337802e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 13.5, "rawScore": 28.02384}}}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "107.0.5304.88", "creation_time": "13394668790249657", "exit_type": "Normal", "last_engagement_time": "13394698739337802", "last_time_obsolete_http_credentials_removed": 1750195250.691884, "last_time_password_store_metrics_reported": 1750195220.690698, "managed_user_id": "", "name": "Person 1", "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "13394668790"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394670826241190", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394674246083361", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394674279936284", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394675026715566", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394675079938228", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394675276724121", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394675319786558", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394675482150681", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394675523955299", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394677593536459", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394677632650867", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394696819384172", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394696859854977", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394697430923494", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394697497049309", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394698626071500", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394698648751354", "type": 2, "window_count": 1}, {"crashed": false, "time": "13394698726139197", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13394698748683938", "type": 2, "window_count": 1}], "session_data_status": 5}, "settings": {"a11y": {"apply_page_colors_only_on_increased_contrast": true}}, "signin": {"allowed": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "107"}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"audi q3 2025\",\"nico williams fc barcelone\",\"geely\",\"manchester city wac\",\"tour de suisse 2025 classement\",\"f35\",\"sujet philo bac 2025\",\"real madrid al hilal sur quelle chaine\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:headertexts\":{\"a\":{\"8\":\"Recherches populaires\"}},\"google:suggestdetail\":[{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8},{\"zl\":8}],\"google:suggesteventid\":\"4347200981411290903\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362],[3,143,362]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\"]}]"}}