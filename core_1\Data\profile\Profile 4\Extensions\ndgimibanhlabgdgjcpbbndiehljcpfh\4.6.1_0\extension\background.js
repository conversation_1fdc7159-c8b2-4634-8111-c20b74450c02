var devtoolsRegEx=/^chrome-devtools:\/\//,connections={},clickEnabled=!0,master={},API=chrome||browser,contentWindowId="";const API_URL="https://shubads.testcasehub.net/",EXT_ID="1";var isFirefox="undefined"!==typeof InstallTrigger,browserType=chrome;isFirefox&&(browserType=browser);let isEdge=-1<navigator.userAgent.indexOf("Edg/")?!0:!1,isChrome=!!chrome&&(!!chrome.webstore||!!chrome.runtime);var isOpera=0<=navigator.userAgent.indexOf(" OPR/");let platform="chrome";isChrome&&(platform="chrome");
isFirefox&&(platform="firefox");isEdge&&(platform="edge");isOpera&&(platform="opera");importScripts("./countries.js");let countryName=null;if(Intl){let a=Intl.DateTimeFormat().resolvedOptions().timeZone.split("/");countryName=COUNTRIES[a[a.length-1]]}var messageToContentScript=function(a){browserType.tabs.sendMessage(a.tabId,a)};
browserType.runtime.onConnect.addListener(function(a){var b=function(c,d,e){c&&"DeattachStudio"===c.message&&(clickEnabled=!0);"init"==c.name?connections[c.tabId]=a:"active"==c.name?window.browserType.windows.update(contentWindowId,{focused:!0}):"openStudio"==c.name?clickEnabled?openStudioWindow():window.browserType.windows.update(contentWindowId,{focused:!0}):messageToContentScript(c)};a.onMessage.addListener(b);a.onDisconnect.addListener(function(c){c.onMessage.removeListener(b);for(var d=Object.keys(connections),
e=0,f=d.length;e<f;e++)if(connections[d[e]]==c){delete connections[d[e]];break}})});var relXpath="",relCssSelector="",jspath="",absXpath="",oldValue=[],contextOption="active",relXpathChecked="relXpathOn",cssSelectorChecked="cssSelectorOff",jspathChecked="jspathOff",absXpathChecked="absXpathOff";function deleteContextMenuItem(a){try{browserType.contextMenus.remove(a,()=>{})}catch(b){}}
function createContextMenuItem(a,b){contextOption=browserType.contextMenus.create({title:a,id:b,parentId:"parent",contexts:["all"]});oldValue.push(contextOption)}let contextMenuAds=[];function deleteContextMenuAds(a){try{browserType.contextMenus.removeAll(a),contextMenuAds=[]}catch(b){}}
async function updateContextMenu(){const a=await browserType.storage.local.get(["ctxads"]);deleteContextMenuAds(async()=>{await createContextMenu();a.ctxads&&a.ctxads.ads.forEach(b=>{b=browserType.contextMenus.create({title:b.ad,id:b.id,parentId:"parent",contexts:["all"]});contextMenuAds.push(b)})})}
function trackEvent(a){browserType.runtime.getPlatformInfo(async b=>{let c=await browserType.storage.local.get(["ext_uniq_id"]);fetch(`${API_URL}analytics/trackAd`,{method:"post",headers:{"Content-Type":"application/json"},body:JSON.stringify({extension:EXT_ID,events:[{user_id:c.ext_uniq_id,event_type:a,user_properties:{Cohort:"Test A"},country:countryName,platform,OS:b.os}]})})})}
browserType.runtime.onMessage.addListener(function(a,b,c){a.contextMenu&&(contextMenu=a.contextMenu,browserType.storage.local.set({contextMenu},function(){}),setTimeout(async function(){if(contextMenu.includes("inactive"))deletePreviousMenuItems();else try{createContextMenu()}catch(d){}},100));a&&"updateContextMenu"===a.action&&updateContextMenu();a&&"AttachStudio"===a.message&&(clickEnabled=!1);a&&"DeattachStudio"===a.message&&(clickEnabled=!0);if(b.tab)if(devtoolsRegEx.test(b.tab.url)){if("shown"===
a.event||"hidden"===a.event)b=b.tab.id,b in connections&&connections[b].postMessage(a);messageToContentScript(a)}else b=b.tab.id,b in connections&&connections[b].postMessage(a);return!0});
browserType.contextMenus.onClicked.addListener(function(a,b){browserType.tabs.query({active:!0,currentWindow:!0},async function(c){let d=await browserType.storage.local.get(["ctxads"]);"parent1"==a.menuItemId?browserType.tabs.sendMessage(c[0].id,{name:"copy relXpath"},function(e){}):"parent2"==a.menuItemId?browserType.tabs.sendMessage(c[0].id,{name:"copy relCssSelector"},function(e){}):"parent3"==a.menuItemId?browserType.tabs.sendMessage(c[0].id,{name:"copy jspath"},function(e){}):"parent4"==a.menuItemId?
browserType.tabs.sendMessage(c[0].id,{name:"copy absXpath"},function(e){}):"parent7"==a.menuItemId?browserType.tabs.sendMessage(c[0].id,{name:"copy testRigor"},function(e){}):d.ctxads&&(c=d.ctxads.ads.filter(e=>e.id===a.menuItemId)[0])&&(openLink(c.link),trackEvent(`clicks:${c.id}:${c.company_name}:ctx__ads`))})});
var deletePreviousMenuItems=function(){if(0<oldValue.length)try{for(var a=0;a<oldValue.length;a++)browserType.contextMenus.remove(oldValue[a],()=>{})}catch(b){}oldValue=[]},installURL="https://bit.ly/4_Playlist",updateURL="https://www.selectorshub.com/changelog/",uninstallURL="https://www.selectorshub.com/uninstall/";
const manifest=browserType.runtime.getManifest(),generateExtensionUniqueId=a=>{let b="";for(let c=0;c<a;c++)b+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".charAt(Math.floor(62*Math.random()));return"sh_"+b},installedListener=async a=>{var b=await browserType.storage.local.get(["ext_uniq_id"]),c=b.ext_uniq_id;b.ext_uniq_id||(c=generateExtensionUniqueId(20),await browserType.storage.local.set({ext_uniq_id:c}));"normal"===(await browserType.management.getSelf()).installType&&
(b=await fetch(`${API_URL}whitelist/${c}`),b=400>b.status?await b.json():{ipExists:!1,object:{ads_types:[]}},c=b.ipExists,!c||c&&!b.object.ads_types.includes("links__ads"))&&(b=(await (await fetch(`${API_URL}ad?published=true&extensions=${EXT_ID}&mode=links__ads&browsers=${platform}&excluded_countries=${countryName.toLowerCase()}`)).json()).ads,0<b.length&&(c=b.filter(d=>"on__uninstall"===d.type),browserType.runtime.setUninstallURL(0<c.length?c[0].link:uninstallURL),"install"==a.reason?(a=b.filter(d=>
"on__install"===d.type),b=0<a.length&&a[0].link,0<a.length&&(b&&openLink(b),trackEvent(`clicks:${a[0].id}:${a[0].company_name}:links__ads`))):"update"==a.reason&&(a=b.filter(d=>"on__update"===d.type),b=0<a.length&&a[0].link,0<a.length&&(b&&openLink(b),trackEvent(`clicks:${a[0].id}:${a[0].company_name}:links__ads`)))))};function onClickInstallNoti(){browserType.tabs.create({url:installURL})}function onClickNoti(){browserType.tabs.create({url:updateURL})}
const updateNotification=()=>{browserType.notifications.create({title:"SelectorsHub",message:`Click here to see the Changelog of new version ${manifest.version}`,type:"basic",iconUrl:"logo-128.png"})},installNotification=()=>{browserType.notifications.create("onInstalled",{title:"SelectorsHub",message:"Refresh the opened tab & watch the video tutorial to make best use of SelectorsHub",type:"basic",iconUrl:"logo-128.png"})};browserType.runtime.onInstalled.addListener(installedListener);
var contextMenu="active";browserType.storage.local.get(["contextMenu"],function(a){(contextMenu=a.contextMenu)?contextMenu.includes("inactive")?deletePreviousMenuItems():createContextMenu():createContextMenu()});function openLink(a){browserType.tabs.create({url:a})}
async function createContextMenu(){contextOption=browserType.contextMenus.create({id:"parent",title:"SelectorsHub",contexts:["all"]});oldValue.push(contextOption);contextOption=browserType.contextMenus.create({title:"Copy Relative XPath",id:"parent1",parentId:"parent",contexts:["all"]});oldValue.push(contextOption);contextOption=browserType.contextMenus.create({title:"Copy Relative cssSelector",id:"parent2",parentId:"parent",contexts:["all"]});oldValue.push(contextOption);contextOption=browserType.contextMenus.create({title:"Copy JS path",
id:"parent3",parentId:"parent",contexts:["all"]});oldValue.push(contextOption);contextOption=browserType.contextMenus.create({title:"Copy testRigor Path",id:"parent7",parentId:"parent",contexts:["all"]});oldValue.push(contextOption);contextOption=browserType.contextMenus.create({title:"Copy abs XPath",id:"parent4",parentId:"parent",contexts:["all"]});oldValue.push(contextOption);const a=await chrome.storage.local.get(["ctxads"]);a.ctxads&&a.ctxads.ads.forEach(b=>{trackEvent(`views:${b.id}:${b.company_name}:ctx__ads`)})}
function openStudioWindow(){browser.windows.create({url:API.runtime.getURL("testCaseStudio/studioWindow.html"),type:"popup",height:600,width:400}).then(function(a){master[a.id]=a;contentWindowId=a.id;browser.tabs.sendMessage(contentWindowId,{type:"studioId",studioId:a});browser.tabs.query({active:!0,windowId:a.id,status:"complete"}).then(function(b){1!=b.length&&(master[a.id]=a.id)})}).catch(function(a){})};
