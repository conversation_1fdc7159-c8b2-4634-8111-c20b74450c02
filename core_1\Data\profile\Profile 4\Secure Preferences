{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13315681491920433", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\Desktop\\auto_instagram\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "lmjegmlicamnimmfhcmpkclmigmmcbeh": {"ack_prompt_count": 1, "active_permissions": {"api": ["nativeMessaging"], "explicit_host": ["https://docs.google.com/*", "https://drive.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 9, "disable_reasons": 8192, "from_webstore": true, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13320689773103500", "lastpingday": "13320662433457650", "location": 6, "manifest": {"author": {"email": "<EMAIL>"}, "background": {"persistent": false, "scripts": ["background_compiled.js"]}, "browser_action": {"default_icon": {"128": "images/drive-sync128.png", "16": "images/drive-sync16.png", "256": "images/drive-sync256.png", "64": "images/drive-sync64.png"}}, "current_locale": "en_US", "default_locale": "en_US", "description": "Open Drive files directly from your browser in compatible applications installed on your computer.", "externally_connectable": {"matches": ["*://*.google.com/*"]}, "icons": {"128": "images/drive-sync128.png", "16": "images/drive-sync16.png", "256": "images/drive-sync256.png", "64": "images/drive-sync64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo6ls4QXUUamoPAlpgJvnpfy2H/54Zsl5mMSrs1zWaIcVqyGRTvzE9d7ekAfJXoPTplwddWd5Kz0QODFPLTxgl+R9wKhrLgeJi6V4Mx8pzDXgxujlQsiHZjikvWEFhMeXssh+W+AbYt9HJnxnYTg3VD2vXc5hO4eApJ8GGIJijMqVSR4YkwttJhxMwuGWyc1WU/b2OsuCsTBOq52HsWZZoZ0iuN51Iu7kBREYMc/QD6p/YFt9WEWzJwC2/G8JoTL+KD9V++tEsMqBOivNfcLAIp04BViC9plYmcNjtYJ9aCGwIQmNKeUHKRado9nHloeKm8m7GZp+JNQNEJDcaiFzewIDAQAB", "manifest_version": 2, "name": "Application Launcher For Drive (by Google)", "natively_connectable": ["com.google.drive.nativeproxy"], "permissions": ["nativeMessaging", "https://docs.google.com/*", "https://drive.google.com/*"], "short_name": "Google Drive App Launcher", "update_url": "https://clients2.google.com/service/update2/crx", "version": "3.5"}, "path": "lmjegmlicamnimmfhcmpkclmigmmcbeh\\3.5_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "state": 0, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13315681491923370", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\Desktop\\auto_instagram\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "ndgimibanhlabgdgjcpbbndiehljcpfh": {"active_bit": false, "active_permissions": {"api": ["clipboardWrite", "contextMenus", "cookies", "devtools", "notifications", "storage", "tabs"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 9, "from_webstore": true, "granted_permissions": {"api": ["clipboardWrite", "contextMenus", "cookies", "devtools", "notifications", "storage", "tabs"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>"]}, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13315681584336004", "location": 1, "manifest": {"action": {"default_popup": "extension/popup.html", "default_title": "xPath plugin to auto generate, write and verify xpath & cssSelector."}, "author": "<PERSON><PERSON>", "background": {"service_worker": "extension/background.js"}, "content_scripts": [{"all_frames": true, "css": ["content-script/contentScript.css"], "js": ["content-script/injectedScript.js", "content-script/consoleApi.js", "content-script/contentScript.js"], "match_about_blank": true, "matches": ["<all_urls>"], "run_at": "document_start"}], "current_locale": "en_US", "default_locale": "en", "description": "xPath plugin to auto generate, write and verify xpath & cssSelector.", "devtools_page": "devtools-panel/devtools.html", "homepage_url": "https://www.selectorshub.com/", "host_permissions": ["<all_urls>"], "icons": {"128": "logo-128.png", "48": "logo-48.png", "96": "logo-96.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApjGa2/5aakd9AkHpa4FJTX1GaHT7ZbGRlsxb6R9iEIAXXi1iomcHOi3Xqsv14m94rzzUGsOHEJBqQWfiNHCNgc29pRlLzEbi61MvcHCULqLvJgBwyy9MEYLeUIf0bw2TQxI34swHBEed1Y+Rr3RA/YLDv/r85aibOpBtDaWqy5xnXVVLXJraK2AuOnZpS3ti5ue/w53+IM58qH3NTuDMv3s84u7FAU/n/c8wQxx68QQB1nUOGxtwa2q2stOk6eWW7N+VhhSQTjwBJVKuYQ03Y17hb7RttVgDj17kpv76GzoJXinwkhqO4/oZ4zh2zXxUWXMFI4TbbEmOtzGkAOqbIQIDAQAB", "manifest_version": 3, "name": "SelectorsHub", "offline_enabled": true, "permissions": ["tabs", "notifications", "storage", "contextMenus", "clipboardWrite", "cookies"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "4.6.1"}, "needs_sync": true, "path": "ndgimibanhlabgdgjcpbbndiehljcpfh\\4.6.1_0", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "4.6.1"}, "serviceworkerevents": ["contextMenus.onClicked", "runtime.onInstalled"], "state": 1, "uninstall_url": "https://selectorshub.com/uninstall/", "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"active_permissions": {"api": ["systemPrivate", "ttsEngine"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13315681491929708", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Users\\<USER>\\Desktop\\auto_instagram\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"active_permissions": {"api": ["desktopCapture", "processes", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13315681491926551", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["desktopCapture", "enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate"], "version": "1.3.21"}, "path": "C:\\Users\\<USER>\\Desktop\\auto_instagram\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"ack_external": true, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 137, "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13320689782983861", "lastpingday": "13320662433457650", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "en_US", "default_locale": "en", "description": "Chrome Web Store Payments", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome Web Store Payments", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda\\1.0.0.6_0", "preferences": {}, "regular_only_preferences": {}, "running": true, "state": 1, "was_installed_by_default": true, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"browser": {"show_home_button": "32D929B1126674E4F140A62CA6AADB624305C6B14D8EA37FC827CA32BBACB05B"}, "default_search_provider_data": {"template_url_data": "41D3F461518A756E473775C092E702DA04CCD3C762E68B7C52291679B8147956"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "10F9236A951F43A075DCB0028D45796B4ADD893A4B5D43E0C33F02D0327FEE72", "lmjegmlicamnimmfhcmpkclmigmmcbeh": "2FA744E4B1DABDA76BDCEF42EC4388C6C56D542E5AA93B265BC219B01FADDA61", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "746D6D626412D900FFF16E8144BE8BE136E125742142BD424E73EFC8D51F12BC", "ndgimibanhlabgdgjcpbbndiehljcpfh": "55F81CA70EFE87F572C2188A3691C3F4B2A00365C4DFE0887ACB98329A52C15F", "neajdppkdcdipfabeoofebfddakdcjhd": "AF0CECFA52974B49B7EE50F69A3D00524F95E29FE568F6165B5526C71D249D56", "nkeimhogjdpnpccoofpliimaahmaaome": "70518816C89AC3D2962A5BF01E99984DF0A2DF465E768DF65EA3402A99015535", "nmmhkkegccagdldgiimedpiccmgmieda": "8B7337627009AF9A4D69FCEE6014BD8FE7792A7D856D9BDD066E487A98CEA56C"}}, "google": {"services": {"account_id": "412037E0EB15FF82F607DF2A01511F8D5D8B5A967C0997770E4F6D09F6967224", "last_account_id": "96C4888086B49A71F98195C308C8FEB1E05B9FEEDA118A005FFD2B58F7FD42BE", "last_username": "E098A9C199185FFAD9A788F6E43C7C84D82EC21C53E936ABBBEE932DDB2F4832"}}, "homepage": "294F2000F0B06E3C96B8F6F8C813FED2FB2C637174D312BC31D8A9078A01B46F", "homepage_is_newtabpage": "D04144B85CFA9484A9DE9ABD2756B97D935B3CEFF03C80921BA693D9EDF628D4", "media": {"cdm": {"origin_data": "97468D364EBCA2110E951521853D90C830AA889CDEEFD6727C7698B0AA18E7C5"}, "storage_id_salt": "55F209D14F5FD6F2DB3AC3F86DFCD6E4A659574AF74ED3060C3608EB3EE0A9B0"}, "module_blocklist_cache_md5_digest": "E7B7B90F19D5BD09E30E6A4E295A43B3024DAE85ECFD454DE75D15AB13C76CB4", "pinned_tabs": "CD8035A177DE56127D219628F2C0AB59F9C3D5229BC2ED826983F7D87CA6A1E0", "prefs": {"preference_reset_time": "D627C097EF7A9A8505E1708A568146935E535CCD71E75F4FBFB1C003DB2FA390"}, "safebrowsing": {"incidents_sent": "B6C1B9A5C8FD6EB7DBD89031D0A42AFF016956B5F119F8853D809D79CD81D794"}, "search_provider_overrides": "9281114E329838161F9EA61E7ECFD9118BF2003BA7356A7208434A8D6BB30E11", "session": {"restore_on_startup": "FF4B86825A681049932462C85AD6EF6D3C2A5BF5E8BAC4F0D72D1A1D9200CBF0", "startup_urls": "B10DAEDEEDAA97FDCDCAED97C44C2AB1C4E8B5191FE871C1E499CF17F835186F"}, "settings_reset_prompt": {"last_triggered_for_default_search": "D4BF36636808B85F438C91E58789393AFC003A95E5F6DBAB08B30582B0740EB8", "last_triggered_for_homepage": "DD577D573564523291AC33CF4DD8AA1DC86B4532AA5E2DBE625462AA11D32B4C", "last_triggered_for_startup_urls": "60DA673596784F8A9CBAF100F08C2FAD8ECAA844A2828E9375700D6224EE1826", "prompt_wave": "DE10FFC888D8D96073980DBCF126FBB5BC044F1D1FD6129E3B1BA690768F908E"}, "software_reporter": {"prompt_seed": "EF627E563F7158F5037A4D2747483386B9A1B8F8D49E579DF5E27440745CF4C1", "prompt_version": "50EE0CCB44A4DCFA8C689D464B08B5AD25CB628B0B1BFF16441AF3479BDB342B", "reporting": "3149A480C3BBA138B35693599FAD44CD76A543F7959D8C861ADE1FA1849069D1"}}, "super_mac": "44AB308A7B34CA4D3F5C76BB07A636C33C9F22DB0D7EC7945F6EAEEB16689FAE"}}