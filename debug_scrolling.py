#!/usr/bin/env python3
"""
Debug script specifically for testing Instagram followers scrolling
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from configparser import ConfigParser
import time
import os

def debug_followers_scrolling():
    """Debug the followers scrolling functionality step by step"""
    
    # Get current working directory
    cwd = os.getcwd()
    
    # Load config
    config_file = "config.ini"
    parser = ConfigParser()
    parser.read(config_file, encoding='utf-8')
    
    core = parser["Program_Data"]["core"]
    profile = parser["Program_Data"]["profile"]
    competitor_link = parser["Program_Data"]["competitor_link"]
    
    print(f"🔍 DEBUG: Instagram Followers Scrolling")
    print(f"Target: {competitor_link}")
    print("-" * 60)
    
    try:
        # Setup Chrome
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        print("🚀 Starting Chrome...")
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Go to Instagram
        print("📱 Going to Instagram...")
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        # Go to competitor profile
        print(f"👤 Going to competitor profile...")
        driver.get(competitor_link)
        time.sleep(8)
        
        # Click followers
        print("👆 Clicking followers...")
        followers_xpath = "//span[contains(text(),'followers')]"
        followers_element = driver.find_element(By.XPATH, followers_xpath)
        followers_element.click()
        time.sleep(5)
        
        # Wait for dialog
        print("⏳ Waiting for followers dialog...")
        dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
        time.sleep(3)
        
        print("🔍 TESTING SCROLLING METHODS...")
        print("-" * 40)
        
        # Method 1: Find scrollable container
        print("1️⃣  Testing scrollable container detection...")
        container_xpaths = [
            '//div[@role="dialog"]//div[contains(@style, "height") and contains(@style, "overflow")]',
            '//div[@role="dialog"]//div[contains(@class, "_aano")]',
            '//div[@role="dialog"]//div[contains(@class, "x1n2onr6") and contains(@style, "overflow")]'
        ]
        
        scrollable_container = None
        for xpath in container_xpaths:
            try:
                elements = driver.find_elements(By.XPATH, xpath)
                if elements:
                    scrollable_container = elements[0]
                    print(f"   ✅ Found container with: {xpath}")
                    break
            except:
                continue
        
        if not scrollable_container:
            print("   ❌ No scrollable container found")
        
        # Count initial followers
        print("\n2️⃣  Counting initial followers...")
        initial_followers = count_followers(driver)
        print(f"   📊 Initial followers visible: {initial_followers}")
        
        # Test scrolling
        print("\n3️⃣  Testing scrolling...")
        if scrollable_container:
            print("   📜 Scrolling container...")
            for i in range(5):
                driver.execute_script("arguments[0].scrollTop += 300", scrollable_container)
                time.sleep(2)
                current_followers = count_followers(driver)
                print(f"   Scroll {i+1}: {current_followers} followers visible")
        else:
            print("   📜 Scrolling dialog...")
            for i in range(5):
                driver.execute_script("arguments[0].scrollTop += 300", dialog)
                time.sleep(2)
                current_followers = count_followers(driver)
                print(f"   Scroll {i+1}: {current_followers} followers visible")
        
        # Final count
        print("\n4️⃣  Final count after scrolling...")
        final_followers = count_followers(driver)
        print(f"   📊 Final followers visible: {final_followers}")
        print(f"   📈 Increase: {final_followers - initial_followers}")
        
        if final_followers > initial_followers:
            print("   ✅ SUCCESS: Scrolling worked!")
        else:
            print("   ❌ PROBLEM: Scrolling didn't load new followers")
            print("   💡 Possible solutions:")
            print("      - Try different scroll methods")
            print("      - Increase scroll delays")
            print("      - Use mouse wheel events")
            print("      - Check for rate limiting")
        
        # Keep browser open for manual inspection
        print(f"\n🔍 Browser will stay open for 60 seconds for manual inspection...")
        print("Check the followers dialog and try manual scrolling")
        time.sleep(60)
        
        driver.quit()
        print("✅ Debug completed!")
        
    except Exception as e:
        print(f"❌ Error during debug: {e}")
        try:
            driver.quit()
        except:
            pass

def count_followers(driver):
    """Count visible followers in the dialog"""
    try:
        # Try multiple XPath patterns to count followers
        xpaths = [
            '//div[@role="dialog"]//a[contains(@href, "/") and not(contains(@href, "/p/"))]',
            '//div[@role="dialog"]//a[@role="link"]',
            '//div[@role="dialog"]//span[contains(@class, "x1lliihq")]//a'
        ]
        
        max_count = 0
        for xpath in xpaths:
            try:
                elements = driver.find_elements(By.XPATH, xpath)
                # Filter for valid Instagram profile links
                valid_count = 0
                for element in elements:
                    href = element.get_attribute("href")
                    if href and "instagram.com/" in href and "/p/" not in href:
                        valid_count += 1
                max_count = max(max_count, valid_count)
            except:
                continue
        
        return max_count
    except:
        return 0

if __name__ == "__main__":
    debug_followers_scrolling()
