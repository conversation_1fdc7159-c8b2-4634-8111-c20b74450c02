#!/usr/bin/env python3
"""
Diagnostic complet pour identifier le problème de scrolling Instagram
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from configparser import Config<PERSON>arser
import time
import os

def diagnostic_complet():
    """Diagnostic complet du problème de scrolling"""
    
    # Load config
    cwd = os.getcwd()
    config_file = "config.ini"
    parser = ConfigParser()
    parser.read(config_file, encoding='utf-8')
    
    core = parser["Program_Data"]["core"]
    profile = parser["Program_Data"]["profile"]
    competitor_link = parser["Program_Data"]["competitor_link"]
    
    print("🔬 DIAGNOSTIC COMPLET DU SCROLLING INSTAGRAM")
    print("=" * 60)
    print(f"Target: {competitor_link}")
    print("=" * 60)
    
    try:
        # Setup Chrome
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Étape 1: Navigation
        print("\n📱 ÉTAPE 1: Navigation vers Instagram")
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        print("✅ Instagram chargé")
        
        print("\n👤 ÉTAPE 2: Navigation vers le profil concurrent")
        driver.get(competitor_link)
        time.sleep(8)
        print("✅ Profil concurrent chargé")
        
        # Étape 3: Clic sur followers
        print("\n👆 ÉTAPE 3: Clic sur followers")
        followers_xpath = "//span[contains(text(),'followers')]"
        followers_element = driver.find_element(By.XPATH, followers_xpath)
        followers_element.click()
        time.sleep(5)
        print("✅ Clic sur followers effectué")
        
        # Étape 4: Analyse de la structure de la page
        print("\n🔍 ÉTAPE 4: Analyse de la structure de la page")
        
        # Vérifier si le dialog est ouvert
        try:
            dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
            print("✅ Dialog des followers trouvé")
            
            # Analyser la structure du dialog
            print("\n📋 Structure du dialog:")
            dialog_html = dialog.get_attribute('outerHTML')[:500]
            print(f"   HTML (premiers 500 chars): {dialog_html}")
            
        except Exception as e:
            print(f"❌ Dialog non trouvé: {e}")
            return
        
        # Étape 5: Recherche de tous les éléments scrollables
        print("\n🔍 ÉTAPE 5: Recherche d'éléments scrollables")
        
        all_elements = driver.find_elements(By.XPATH, '//div[@role="dialog"]//div')
        scrollable_elements = []
        
        for i, element in enumerate(all_elements):
            try:
                scroll_height = driver.execute_script("return arguments[0].scrollHeight", element)
                client_height = driver.execute_script("return arguments[0].clientHeight", element)
                overflow = driver.execute_script("return window.getComputedStyle(arguments[0]).overflow", element)
                overflow_y = driver.execute_script("return window.getComputedStyle(arguments[0]).overflowY", element)
                
                if scroll_height > client_height:
                    scrollable_elements.append({
                        'element': element,
                        'index': i,
                        'scroll_height': scroll_height,
                        'client_height': client_height,
                        'overflow': overflow,
                        'overflow_y': overflow_y
                    })
                    
            except:
                continue
        
        print(f"📊 Trouvé {len(scrollable_elements)} éléments scrollables:")
        for elem_info in scrollable_elements:
            print(f"   Élément {elem_info['index']}: {elem_info['scroll_height']}px scroll, {elem_info['client_height']}px visible")
            print(f"      overflow: {elem_info['overflow']}, overflow-y: {elem_info['overflow_y']}")
        
        # Étape 6: Compter les followers initiaux
        print("\n📊 ÉTAPE 6: Comptage des followers initiaux")
        initial_followers = count_all_followers(driver)
        print(f"Followers initiaux détectés: {initial_followers}")
        
        # Étape 7: Test de scrolling sur chaque élément scrollable
        print("\n🧪 ÉTAPE 7: Test de scrolling sur chaque élément")
        
        for i, elem_info in enumerate(scrollable_elements):
            print(f"\n--- Test de l'élément {i+1}/{len(scrollable_elements)} ---")
            element = elem_info['element']
            
            try:
                # Position initiale
                initial_scroll_top = driver.execute_script("return arguments[0].scrollTop", element)
                print(f"Position initiale: {initial_scroll_top}px")
                
                # Essayer de scroller
                driver.execute_script("arguments[0].scrollTop += 500", element)
                time.sleep(2)
                
                # Vérifier la nouvelle position
                new_scroll_top = driver.execute_script("return arguments[0].scrollTop", element)
                print(f"Nouvelle position: {new_scroll_top}px")
                
                if new_scroll_top > initial_scroll_top:
                    print("✅ Scrolling fonctionne sur cet élément!")
                    
                    # Compter les nouveaux followers
                    new_followers_count = count_all_followers(driver)
                    print(f"Followers après scroll: {new_followers_count}")
                    
                    if new_followers_count > initial_followers:
                        print(f"🎉 SUCCÈS! Nouveaux followers chargés: {initial_followers} → {new_followers_count}")
                        
                        # Continuer à scroller cet élément
                        print("🔄 Scrolling continu sur cet élément...")
                        for continued in range(10):
                            driver.execute_script("arguments[0].scrollTop += 300", element)
                            time.sleep(1.5)
                            current_count = count_all_followers(driver)
                            print(f"   Scroll {continued + 1}: {current_count} followers")
                        
                        break
                    else:
                        print("⚠️  Scrolling fonctionne mais pas de nouveaux followers")
                else:
                    print("❌ Scrolling ne fonctionne pas sur cet élément")
                    
            except Exception as e:
                print(f"❌ Erreur lors du test: {e}")
        
        # Étape 8: Test de méthodes alternatives
        print("\n🔄 ÉTAPE 8: Test de méthodes alternatives")
        
        # Méthode 1: Scrolling de page
        print("Test 1: Scrolling de page")
        initial_count = count_all_followers(driver)
        driver.execute_script("window.scrollBy(0, 500);")
        time.sleep(2)
        new_count = count_all_followers(driver)
        print(f"   Résultat: {initial_count} → {new_count}")
        
        # Méthode 2: Touches clavier
        print("Test 2: Touches clavier")
        try:
            body = driver.find_element(By.TAG_NAME, "body")
            initial_count = count_all_followers(driver)
            body.send_keys(Keys.PAGE_DOWN)
            time.sleep(2)
            new_count = count_all_followers(driver)
            print(f"   Résultat: {initial_count} → {new_count}")
        except Exception as e:
            print(f"   Erreur: {e}")
        
        # Méthode 3: Wheel events
        print("Test 3: Wheel events")
        try:
            initial_count = count_all_followers(driver)
            driver.execute_script("""
                var dialog = document.querySelector('div[role="dialog"]');
                var wheelEvent = new WheelEvent('wheel', {
                    deltaY: 1000,
                    bubbles: true,
                    cancelable: true
                });
                dialog.dispatchEvent(wheelEvent);
            """)
            time.sleep(2)
            new_count = count_all_followers(driver)
            print(f"   Résultat: {initial_count} → {new_count}")
        except Exception as e:
            print(f"   Erreur: {e}")
        
        # Résumé final
        final_count = count_all_followers(driver)
        print(f"\n📊 RÉSUMÉ FINAL:")
        print(f"Followers initiaux: {initial_followers}")
        print(f"Followers finaux: {final_count}")
        print(f"Différence: {final_count - initial_followers}")
        
        if final_count > initial_followers:
            print("✅ Au moins une méthode de scrolling fonctionne!")
        else:
            print("❌ Aucune méthode de scrolling ne fonctionne")
            print("💡 Causes possibles:")
            print("   - Compte privé")
            print("   - Structure Instagram changée")
            print("   - Rate limiting")
            print("   - JavaScript désactivé")
        
        # Garder le navigateur ouvert
        print(f"\n🔍 Navigateur ouvert pour inspection manuelle (60 secondes)...")
        time.sleep(60)
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        try:
            driver.quit()
        except:
            pass

def count_all_followers(driver):
    """Compter tous les followers visibles avec différentes méthodes"""
    counts = []
    
    # Méthode 1
    try:
        links1 = driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]')
        valid1 = sum(1 for link in links1 if is_valid_follower_link(link))
        counts.append(valid1)
    except:
        counts.append(0)
    
    # Méthode 2
    try:
        links2 = driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[@role="link"]')
        valid2 = sum(1 for link in links2 if is_valid_follower_link(link))
        counts.append(valid2)
    except:
        counts.append(0)
    
    return max(counts)

def is_valid_follower_link(link):
    """Vérifier si un lien est un follower valide"""
    try:
        href = link.get_attribute("href")
        return (href and 
                "instagram.com/" in href and 
                "/p/" not in href and 
                "/reel/" not in href and
                "/tv/" not in href)
    except:
        return False

if __name__ == "__main__":
    diagnostic_complet()
