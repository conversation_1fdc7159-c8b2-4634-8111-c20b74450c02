#!/usr/bin/env python3
"""
Simple test to verify Instagram followers scrolling works
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from configparser import ConfigParser
import time
import os

def simple_scroll_test():
    """Simple test to see if we can scroll and load more followers"""
    
    # Load config
    cwd = os.getcwd()
    config_file = "config.ini"
    parser = ConfigParser()
    parser.read(config_file, encoding='utf-8')
    
    core = parser["Program_Data"]["core"]
    profile = parser["Program_Data"]["profile"]
    competitor_link = parser["Program_Data"]["competitor_link"]
    
    print("🧪 SIMPLE SCROLL TEST")
    print(f"Target: {competitor_link}")
    print("=" * 50)
    
    try:
        # Setup Chrome
        user_data_dir = f"{cwd}\\{core}\\Data\\{profile}"
        options = Options()
        options.binary_location = f"{cwd}\\{core}\\App\\Chrome-bin\\chrome.exe"
        
        prefs = {"profile.default_content_setting_values.notifications": 2}
        options.add_experimental_option("prefs", prefs)
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--detach=True")
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--profile-directory={profile}")
        
        driver = webdriver.Chrome(options=options)
        driver.delete_all_cookies()
        driver.implicitly_wait(5)
        driver.maximize_window()
        time.sleep(3)
        
        # Navigate to Instagram and profile
        print("📱 Going to Instagram...")
        driver.get("https://www.instagram.com/")
        time.sleep(5)
        
        print("👤 Going to competitor profile...")
        driver.get(competitor_link)
        time.sleep(8)
        
        # Click followers
        print("👆 Clicking followers...")
        followers_xpath = "//span[contains(text(),'followers')]"
        followers_element = driver.find_element(By.XPATH, followers_xpath)
        followers_element.click()
        time.sleep(5)
        
        # Count initial followers
        print("📊 Counting initial followers...")
        initial_count = count_visible_followers(driver)
        print(f"   Initial followers visible: {initial_count}")
        
        if initial_count == 0:
            print("❌ No followers found! Check if dialog opened correctly.")
            return
        
        # Test scrolling
        print("\n🔄 Testing scrolling...")
        dialog = driver.find_element(By.XPATH, '//div[@role="dialog"]')
        
        for scroll_test in range(10):
            print(f"   Scroll attempt {scroll_test + 1}...")
            
            # Try multiple scroll methods
            driver.execute_script("arguments[0].scrollTop += 500", dialog)
            time.sleep(1)
            driver.execute_script("arguments[0].scrollBy(0, 300)", dialog)
            time.sleep(1)
            
            # Count followers after scroll
            current_count = count_visible_followers(driver)
            print(f"   After scroll {scroll_test + 1}: {current_count} followers visible")
            
            if current_count > initial_count:
                print(f"   ✅ SUCCESS! Loaded {current_count - initial_count} new followers!")
                break
            
            # Try wheel event
            driver.execute_script("""
                var wheelEvent = new WheelEvent('wheel', {
                    deltaY: 1000,
                    bubbles: true,
                    cancelable: true
                });
                arguments[0].dispatchEvent(wheelEvent);
            """, dialog)
            time.sleep(2)
            
            current_count = count_visible_followers(driver)
            if current_count > initial_count:
                print(f"   ✅ SUCCESS with wheel! Loaded {current_count - initial_count} new followers!")
                break
        
        # Final count
        final_count = count_visible_followers(driver)
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Initial: {initial_count} followers")
        print(f"   Final:   {final_count} followers")
        print(f"   Loaded:  {final_count - initial_count} new followers")
        
        if final_count > initial_count:
            print("🎉 SCROLLING WORKS! The issue is solved!")
        else:
            print("❌ SCROLLING FAILED! Need to try different approach.")
            print("💡 Possible solutions:")
            print("   - Instagram changed their structure")
            print("   - Need different scroll selectors")
            print("   - Rate limiting is preventing loading")
            print("   - Account might be private")
        
        # Keep browser open for manual testing
        print(f"\n🔍 Browser staying open for 30 seconds...")
        print("Try manual scrolling to see if it works manually")
        time.sleep(30)
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        try:
            driver.quit()
        except:
            pass

def count_visible_followers(driver):
    """Count how many followers are currently visible"""
    try:
        # Count links that look like Instagram profiles
        links = driver.find_elements(By.XPATH, '//div[@role="dialog"]//a[contains(@href, "/")]')
        
        valid_count = 0
        for link in links:
            try:
                href = link.get_attribute("href")
                if href and "instagram.com/" in href and "/p/" not in href and "/reel/" not in href:
                    valid_count += 1
            except:
                continue
        
        return valid_count
    except:
        return 0

if __name__ == "__main__":
    simple_scroll_test()
